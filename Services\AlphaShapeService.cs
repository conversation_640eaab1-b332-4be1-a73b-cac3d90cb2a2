using Autodesk.AutoCAD.Geometry;
using System;
using System.Collections.Generic;
using System.Linq;
using NetTopologySuite.Geometries;
using NetTopologySuite.Triangulate;

namespace IECAD.Services
{
    /// <summary>
    /// Alpha Shape算法服务 - 实现真正的Alpha Shape凹包算法
    /// </summary>
    public class AlphaShapeService
    {
        private readonly double _tolerance;
        private readonly GeometryFactory _geometryFactory;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="tolerance">计算容差</param>
        public AlphaShapeService(double tolerance = 1e-6)
        {
            _tolerance = tolerance;
            _geometryFactory = new GeometryFactory();
        }

        /// <summary>
        /// 计算Alpha Shape凹包
        /// </summary>
        /// <param name="points">输入点集</param>
        /// <param name="alpha">Alpha参数，控制凹包的凹度</param>
        /// <returns>Alpha Shape轮廓点集</returns>
        public List<Point3d> ComputeAlphaShape(List<Point3d> points, double alpha)
        {
            if (points == null || points.Count < 3)
                return points ?? new List<Point3d>();

            try
            {
                // 1. 转换为NetTopologySuite坐标
                var coordinates = points.Select(p => new Coordinate(p.X, p.Y)).ToArray();

                // 2. 创建Delaunay三角剖分
                var triangulator = new DelaunayTriangulationBuilder();
                triangulator.SetSites(coordinates);
                var triangulation = triangulator.GetTriangles(_geometryFactory);

                // 3. 提取三角形
                var triangles = ExtractTriangles(triangulation);

                // 4. 应用Alpha Shape算法
                var alphaEdges = ComputeAlphaShapeEdges(triangles, alpha);

                // 5. 构建边界轮廓
                var boundaryPoints = BuildBoundaryFromEdges(alphaEdges);

                // 6. 转换回AutoCAD Point3d
                return boundaryPoints.Select(c => new Point3d(c.X, c.Y, 0)).ToList();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Alpha Shape计算失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 从几何对象中提取三角形
        /// </summary>
        /// <param name="triangulation">三角剖分结果</param>
        /// <returns>三角形列表</returns>
        private List<Triangle> ExtractTriangles(Geometry triangulation)
        {
            var triangles = new List<Triangle>();

            if (triangulation is GeometryCollection collection)
            {
                for (int i = 0; i < collection.NumGeometries; i++)
                {
                    var geom = collection.GetGeometryN(i);
                    if (geom is Polygon polygon && polygon.ExteriorRing.Coordinates.Length == 4) // 三角形+闭合点
                    {
                        var coords = polygon.ExteriorRing.Coordinates;
                        triangles.Add(new Triangle(coords[0], coords[1], coords[2]));
                    }
                }
            }

            return triangles;
        }

        /// <summary>
        /// 计算Alpha Shape的边
        /// </summary>
        /// <param name="triangles">三角形列表</param>
        /// <param name="alpha">Alpha参数</param>
        /// <returns>Alpha Shape边集合</returns>
        private List<Edge> ComputeAlphaShapeEdges(List<Triangle> triangles, double alpha)
        {
            var edges = new List<Edge>();
            var edgeCount = new Dictionary<string, int>();
            var edgeTriangles = new Dictionary<string, List<Triangle>>();

            // 收集所有边
            foreach (var triangle in triangles)
            {
                var triangleEdges = triangle.GetEdges();
                foreach (var edge in triangleEdges)
                {
                    var key = edge.GetKey();
                    
                    if (!edgeCount.ContainsKey(key))
                    {
                        edgeCount[key] = 0;
                        edgeTriangles[key] = new List<Triangle>();
                    }
                    
                    edgeCount[key]++;
                    edgeTriangles[key].Add(triangle);
                }
            }

            // 应用Alpha Shape条件
            foreach (var kvp in edgeCount)
            {
                var edge = ParseEdgeFromKey(kvp.Key);
                var count = kvp.Value;
                var associatedTriangles = edgeTriangles[kvp.Key];

                bool includeEdge = false;

                if (count == 1)
                {
                    // 边界边：检查外接圆半径
                    var triangle = associatedTriangles[0];
                    var circumRadius = triangle.GetCircumRadius();
                    includeEdge = circumRadius <= 1.0 / alpha;
                }
                else if (count == 2)
                {
                    // 内部边：检查两个三角形的外接圆
                    var triangle1 = associatedTriangles[0];
                    var triangle2 = associatedTriangles[1];
                    
                    var radius1 = triangle1.GetCircumRadius();
                    var radius2 = triangle2.GetCircumRadius();
                    
                    // 如果任一三角形的外接圆半径大于1/alpha，则包含此边
                    includeEdge = radius1 > 1.0 / alpha || radius2 > 1.0 / alpha;
                }

                if (includeEdge)
                {
                    edges.Add(edge);
                }
            }

            return edges;
        }

        /// <summary>
        /// 从边集合构建边界轮廓
        /// </summary>
        /// <param name="edges">边集合</param>
        /// <returns>边界点序列</returns>
        private List<Coordinate> BuildBoundaryFromEdges(List<Edge> edges)
        {
            if (edges.Count == 0)
                return new List<Coordinate>();

            var boundary = new List<Coordinate>();
            var usedEdges = new HashSet<Edge>();
            var adjacencyMap = BuildAdjacencyMap(edges);

            // 找到起始边（优先选择边界边）
            var startEdge = edges.FirstOrDefault(e => GetAdjacentEdges(e, adjacencyMap).Count == 1) ?? edges[0];
            
            var currentEdge = startEdge;
            var currentPoint = currentEdge.Start;
            
            boundary.Add(currentPoint);
            usedEdges.Add(currentEdge);

            // 遍历边界
            while (true)
            {
                var nextPoint = currentEdge.End;
                boundary.Add(nextPoint);

                // 查找下一条边
                var adjacentEdges = GetAdjacentEdges(currentEdge, adjacencyMap)
                    .Where(e => !usedEdges.Contains(e))
                    .ToList();

                if (adjacentEdges.Count == 0)
                    break;

                // 选择下一条边（优先选择能形成连续路径的边）
                var nextEdge = adjacentEdges.FirstOrDefault(e => 
                    e.Start.Equals2D(nextPoint) || e.End.Equals2D(nextPoint));

                if (nextEdge == null)
                    break;

                // 确保边的方向正确
                if (nextEdge.End.Equals2D(nextPoint))
                {
                    nextEdge = new Edge(nextEdge.End, nextEdge.Start);
                }

                currentEdge = nextEdge;
                currentPoint = nextPoint;
                usedEdges.Add(nextEdge);

                // 检查是否回到起点
                if (currentPoint.Equals2D(boundary[0]) && boundary.Count > 3)
                    break;
            }

            return boundary;
        }

        /// <summary>
        /// 构建边的邻接映射
        /// </summary>
        /// <param name="edges">边集合</param>
        /// <returns>邻接映射</returns>
        private Dictionary<Coordinate, List<Edge>> BuildAdjacencyMap(List<Edge> edges)
        {
            var adjacencyMap = new Dictionary<Coordinate, List<Edge>>();

            foreach (var edge in edges)
            {
                if (!adjacencyMap.ContainsKey(edge.Start))
                    adjacencyMap[edge.Start] = new List<Edge>();
                if (!adjacencyMap.ContainsKey(edge.End))
                    adjacencyMap[edge.End] = new List<Edge>();

                adjacencyMap[edge.Start].Add(edge);
                adjacencyMap[edge.End].Add(edge);
            }

            return adjacencyMap;
        }

        /// <summary>
        /// 获取与指定边相邻的边
        /// </summary>
        /// <param name="edge">指定边</param>
        /// <param name="adjacencyMap">邻接映射</param>
        /// <returns>相邻边列表</returns>
        private List<Edge> GetAdjacentEdges(Edge edge, Dictionary<Coordinate, List<Edge>> adjacencyMap)
        {
            var adjacent = new List<Edge>();

            if (adjacencyMap.ContainsKey(edge.Start))
                adjacent.AddRange(adjacencyMap[edge.Start].Where(e => !e.Equals(edge)));
            
            if (adjacencyMap.ContainsKey(edge.End))
                adjacent.AddRange(adjacencyMap[edge.End].Where(e => !e.Equals(edge)));

            return adjacent.Distinct().ToList();
        }

        /// <summary>
        /// 从键字符串解析边
        /// </summary>
        /// <param name="key">边的键字符串</param>
        /// <returns>边对象</returns>
        private Edge ParseEdgeFromKey(string key)
        {
            var parts = key.Split('|');
            var start = ParseCoordinate(parts[0]);
            var end = ParseCoordinate(parts[1]);
            return new Edge(start, end);
        }

        /// <summary>
        /// 从字符串解析坐标
        /// </summary>
        /// <param name="coordStr">坐标字符串</param>
        /// <returns>坐标对象</returns>
        private Coordinate ParseCoordinate(string coordStr)
        {
            var parts = coordStr.Split(',');
            return new Coordinate(double.Parse(parts[0]), double.Parse(parts[1]));
        }
    }

    /// <summary>
    /// 三角形类
    /// </summary>
    public class Triangle
    {
        public Coordinate A { get; }
        public Coordinate B { get; }
        public Coordinate C { get; }

        public Triangle(Coordinate a, Coordinate b, Coordinate c)
        {
            A = a;
            B = b;
            C = c;
        }

        /// <summary>
        /// 获取三角形的边
        /// </summary>
        /// <returns>边列表</returns>
        public List<Edge> GetEdges()
        {
            return new List<Edge>
            {
                new Edge(A, B),
                new Edge(B, C),
                new Edge(C, A)
            };
        }

        /// <summary>
        /// 计算外接圆半径
        /// </summary>
        /// <returns>外接圆半径</returns>
        public double GetCircumRadius()
        {
            var a = B.Distance(C);
            var b = C.Distance(A);
            var c = A.Distance(B);

            var s = (a + b + c) / 2.0; // 半周长
            var area = Math.Sqrt(s * (s - a) * (s - b) * (s - c)); // 海伦公式

            if (Math.Abs(area) < 1e-10)
                return double.MaxValue; // 退化三角形

            return (a * b * c) / (4.0 * area);
        }
    }

    /// <summary>
    /// 边类
    /// </summary>
    public class Edge
    {
        public Coordinate Start { get; }
        public Coordinate End { get; }

        public Edge(Coordinate start, Coordinate end)
        {
            Start = start;
            End = end;
        }

        /// <summary>
        /// 获取边的唯一键
        /// </summary>
        /// <returns>边的键字符串</returns>
        public string GetKey()
        {
            // 确保键的唯一性（不考虑方向）
            var start = Start;
            var end = End;
            
            if (start.X > end.X || (Math.Abs(start.X - end.X) < 1e-10 && start.Y > end.Y))
            {
                (start, end) = (end, start);
            }

            return $"{start.X},{start.Y}|{end.X},{end.Y}";
        }

        public override bool Equals(object obj)
        {
            if (obj is Edge other)
            {
                return GetKey() == other.GetKey();
            }
            return false;
        }

        public override int GetHashCode()
        {
            return GetKey().GetHashCode();
        }
    }
}
