<UserControl x:Class="IECAD.Views.MainSidebarControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IECAD.Views"
             xmlns:helpers="clr-namespace:IECAD.Helpers"
             xmlns:constants="clr-namespace:IECAD.Constants"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="300"
             Background="#F8F9FA">

    <UserControl.Resources>
        <ResourceDictionary>
            <!-- Converters -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <!-- 颜色定义 -->
            <SolidColorBrush x:Key="PrimaryColor" Color="#0066CC"/>
            <SolidColorBrush x:Key="TextColor" Color="#2C3E50"/>
            <SolidColorBrush x:Key="SecondaryTextColor" Color="#6C7A89"/>
            <SolidColorBrush x:Key="BorderColor" Color="#E9ECEF"/>
            <SolidColorBrush x:Key="HoverColor" Color="#F1F5F9"/>
            <SolidColorBrush x:Key="HeaderBackground" Color="#FFFFFF"/>

            <!-- 按钮样式 -->
            <Style x:Key="ModernButton" TargetType="Button">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
                <Setter Property="BorderBrush" Value="Transparent"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="8,6"/>
                <Setter Property="Margin" Value="0,1"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="16"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <!-- 图标位置 -->
                                    <Path Grid.Column="0" 
                                          Width="12" Height="12"
                                          Fill="{StaticResource SecondaryTextColor}"
                                          Data="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6"
                                          Stretch="Uniform"/>
                                    <!-- 文本 -->
                                    <TextBlock Grid.Column="1"
                                             Text="{TemplateBinding Content}"
                                             VerticalAlignment="Center"
                                             Margin="8,0,0,0"/>
                                </Grid>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{StaticResource HoverColor}"/>
                                    <Setter Property="Foreground" Value="{StaticResource PrimaryColor}"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Expander样式 -->
            <Style x:Key="GroupExpander" TargetType="Expander">
                <Setter Property="Margin" Value="0,1"/>
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderBrush" Value="Transparent"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Expander">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}">
                                <DockPanel>
                                    <ToggleButton x:Name="HeaderSite"
                                                DockPanel.Dock="Top"
                                                IsChecked="{Binding IsExpanded, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                Padding="16,8">
                                        <ToggleButton.Template>
                                            <ControlTemplate TargetType="ToggleButton">
                                                <Border Background="{TemplateBinding Background}"
                                                        Padding="{TemplateBinding Padding}">
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="16"/>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>
                                                        <!-- 分组图标 -->
                                                        <Path Grid.Column="0"
                                                              Width="12" Height="12"
                                                              Fill="{StaticResource SecondaryTextColor}"
                                                              Data="M3,6H21V8H3V6M3,11H21V13H3V11M3,16H21V18H3V16Z"
                                                              Stretch="Uniform"/>
                                                        <!-- 标题 -->
                                                        <ContentPresenter Grid.Column="1"
                                                                        Margin="8,0,0,0"
                                                                        ContentSource="Content"
                                                                        RecognizesAccessKey="True">
                                                            <ContentPresenter.Resources>
                                                                <Style TargetType="TextBlock">
                                                                    <Setter Property="FontWeight" Value="Medium"/>
                                                                    <Setter Property="FontSize" Value="11"/>
                                                                    <Setter Property="Foreground" Value="{StaticResource SecondaryTextColor}"/>
                                                                </Style>
                                                            </ContentPresenter.Resources>
                                                        </ContentPresenter>
                                                        <!-- 展开/折叠箭头 -->
                                                        <Path Grid.Column="2"
                                                              x:Name="arrow"
                                                              Width="8" Height="8"
                                                              Fill="{StaticResource SecondaryTextColor}"
                                                              Data="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"
                                                              Stretch="Uniform"
                                                              RenderTransformOrigin="0.5,0.5">
                                                            <Path.RenderTransform>
                                                                <RotateTransform Angle="0"/>
                                                            </Path.RenderTransform>
                                                        </Path>
                                                    </Grid>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <DataTrigger Binding="{Binding IsExpanded, RelativeSource={RelativeSource AncestorType=Expander}}" Value="True">
                                                        <Setter TargetName="arrow" Property="RenderTransform">
                                                            <Setter.Value>
                                                                <RotateTransform Angle="180"/>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </DataTrigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </ToggleButton.Template>
                                        <ContentPresenter Content="{TemplateBinding Header}"
                                                        RecognizesAccessKey="True"/>
                                    </ToggleButton>
                                    <Border x:Name="ExpandSite"
                                            DockPanel.Dock="Bottom"
                                            Background="{TemplateBinding Background}"
                                            Visibility="Collapsed">
                                        <ContentPresenter x:Name="ExpanderContent"/>
                                    </Border>
                                </DockPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsExpanded" Value="True">
                                    <Setter TargetName="ExpandSite" Property="Visibility" Value="Visible"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <!-- 顶部标题栏 -->
        <DockPanel>
            <Border DockPanel.Dock="Top"
                    Background="{StaticResource HeaderBackground}"
                    BorderThickness="0,0,0,1"
                    BorderBrush="{StaticResource BorderColor}">
                <Grid Margin="16,8">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Header Row -->
                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Logo -->
                        <Image Grid.Column="0"
                               Height="28" Width="28"
                               x:Name="LogoImage"
                               VerticalAlignment="Center"/>

                        <!-- Title -->
                        <TextBlock Grid.Column="1"
                                   Text="{Binding ApplicationTitle}"
                                   FontSize="16"
                                   FontWeight="SemiBold"
                                   Foreground="{StaticResource TextColor}"
                                   Margin="12,0,8,0"
                                   VerticalAlignment="Center"/>

                        <!-- Toolbar -->
                        <StackPanel Grid.Column="2"
                                   Orientation="Horizontal"
                                   VerticalAlignment="Center">
                            <Button Content="🔄"
                                   Command="{Binding RefreshCommand}"
                                   ToolTip="Refresh"
                                   Style="{StaticResource ModernButton}"
                                   Width="24" Height="24"
                                   FontSize="10"
                                   Margin="2,0"/>
                            <Button Content="🗑️"
                                   Command="{Binding ClearCacheCommand}"
                                   ToolTip="Clear Cache"
                                   Style="{StaticResource ModernButton}"
                                   Width="24" Height="24"
                                   FontSize="10"
                                   Margin="2,0"/>
                        </StackPanel>
                    </Grid>

                    <!-- Progress Indicator Row -->
                    <local:ProgressIndicator Grid.Row="1"
                                           Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}"
                                           Message="{Binding BusyMessage}"
                                           Margin="0,8,0,0"/>
                </Grid>
            </Border>

            <!-- 内容区域 -->
            <ScrollViewer VerticalScrollBarVisibility="Auto"
                         Margin="0,1,0,0">
                <StackPanel Margin="8,4">
                    <!-- 方案设计组 -->
                    <Expander Header="方案设计" 
                              Style="{StaticResource GroupExpander}"
                              IsExpanded="True">
                        <StackPanel Margin="16,2,8,2">
                            <Button Content="建设备块" 
                                    Click="CreateBlockFromXLS_Click" 
                                    Style="{StaticResource ModernButton}"
                                    ToolTip="从Excel表格中读取设备信息并创建设备图块"/>
                            <Button Content="房间方案" 
                                    Click="ImportRoomPlan_Click" 
                                    Style="{StaticResource ModernButton}"
                                    ToolTip="从Excel表格中读取房间长宽面积信息并创建房间图块，只能生成矩形图块"/>
                        </StackPanel>
                    </Expander>

                    <!-- 施工图设计组 -->
                    <Expander Header="施工图设计" 
                              Style="{StaticResource GroupExpander}"
                              IsExpanded="True">
                        <StackPanel Margin="16,2,8,2">
                            <!-- 设备轮廓提取区域 -->
                            <Expander Header="设备轮廓" 
                                      Style="{StaticResource GroupExpander}" 
                                      IsExpanded="False"
                                      Margin="0,2">
                                <StackPanel Margin="8,4">
                                    <!-- 策略选择标题 -->
                                    <TextBlock Text="轮廓跟踪策略:" 
                                               FontSize="11" 
                                               Foreground="{StaticResource SecondaryTextColor}" 
                                               Margin="0,0,0,6"/>
                                    
                                    <!-- 策略选择选项 -->
                                    <StackPanel>
                                        <RadioButton x:Name="PreciseStrategyRadio" 
                                                     Content="精确步行者" 
                                                     IsChecked="True" 
                                                     GroupName="WalkerStrategy"
                                                     Margin="0,2" 
                                                     FontSize="11"
                                                     ToolTip="严格按几何边界行走，适用于精确工程图纸"/>
                                        <RadioButton x:Name="TolerantStrategyRadio" 
                                                     Content="容差步行者" 
                                                     GroupName="WalkerStrategy" 
                                                     Margin="0,2" 
                                                     FontSize="11"
                                                     ToolTip="智能桥接微小缝隙，适用于有小间隙的图纸"/>
                                        <RadioButton x:Name="MorphologicalStrategyRadio" 
                                                     Content="形态学步行者" 
                                                     GroupName="WalkerStrategy" 
                                                     Margin="0,2" 
                                                     FontSize="11"
                                                     ToolTip="生成平滑轮廓，适用于需要膨胀分析的场景"/>
                                        <RadioButton x:Name="GridStrategyRadio" 
                                                     Content="栅格步行者" 
                                                     GroupName="WalkerStrategy" 
                                                     Margin="0,2" 
                                                     FontSize="11"
                                                     ToolTip="传统栅格化方法，兼容性最好"/>
                                    </StackPanel>
                                    
                                    <!-- 容差参数 -->
                                    <StackPanel Orientation="Horizontal" Margin="0,8,0,4">
                                        <TextBlock Text="容差:" 
                                                   FontSize="11" 
                                                   Foreground="{StaticResource SecondaryTextColor}" 
                                                   VerticalAlignment="Center" 
                                                   Margin="0,0,6,0"/>
                                        <TextBox x:Name="ToleranceTextBox" 
                                                 Text="1.0" 
                                                 Width="50" 
                                                 Height="24" 
                                                 FontSize="11" 
                                                 VerticalContentAlignment="Center" 
                                                 ToolTip="轮廓跟踪的容差参数"/>
                                    </StackPanel>
                                    
                                    <!-- 执行按钮 -->
                                    <Button x:Name="ExtractOutlineButton" 
                                            Content="提取轮廓" 
                                            Click="ExtractOutlineWithStrategy_Click" 
                                            Style="{StaticResource ModernButton}" 
                                            Background="{StaticResource PrimaryColor}" 
                                            Foreground="White" 
                                            FontWeight="SemiBold" 
                                            Margin="0,6,0,0" 
                                            ToolTip="使用选定策略提取实体外轮廓"/>
                                </StackPanel>
                            </Expander>
                            
                            <Button Content="设备对齐"
                                    Click="AlignBlock_Click"
                                    Style="{StaticResource ModernButton}"
                                    ToolTip="沿线对齐选中设备块"/>
                            <Button Content="图块排列"
                                    Click="ArrangeBlocks_Click"
                                    Style="{StaticResource ModernButton}"
                                    ToolTip="按插入点排列选中的设备块"/>
                        </StackPanel>
                    </Expander>

                    <!-- 其它组 -->
                    <Expander Header="其它" 
                              Style="{StaticResource GroupExpander}"
                              IsExpanded="True">
                        <StackPanel Margin="16,2,8,2">
                            <Button Content="压平图块" 
                                    Click="FlattenBlocks_Click" 
                                    Style="{StaticResource ModernButton}"
                                    ToolTip="将选中的嵌套块变成非嵌套块"/>
                            <Button Content="批量文字编辑" 
                                    Click="BatchEditText_Click" 
                                    Style="{StaticResource ModernButton}"
                                    ToolTip="批量编辑选择的文字对象"/>
                        </StackPanel>
                    </Expander>

                </StackPanel>
            </ScrollViewer>
        </DockPanel>
    </Grid>
</UserControl>
