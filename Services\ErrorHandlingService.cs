using System;
using System.IO;
using System.Windows;
using IECAD.Constants;

namespace IECAD.Services
{
    /// <summary>
    /// Error handling service implementation that provides consistent error handling across the application
    /// </summary>
    public class ErrorHandlingService : IErrorHandlingService
    {
        private readonly ILoggingService _loggingService;

        public ErrorHandlingService(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        public void HandleException(Exception exception, string userMessage = null, string source = null, bool showToUser = true)
        {
            if (exception == null) return;

            // Log the exception
            _loggingService.LogException(exception, userMessage, source);

            if (showToUser)
            {
                string displayMessage = GetUserFriendlyMessage(exception, userMessage);
                ShowErrorToUser(displayMessage, "Error");
            }
        }

        public void HandleError(string errorMessage, string source = null, bool showToUser = true)
        {
            if (string.IsNullOrEmpty(errorMessage)) return;

            // Log the error
            _loggingService.LogError(errorMessage, source);

            if (showToUser)
            {
                ShowErrorToUser(errorMessage, "Error");
            }
        }

        public void HandleWarning(string warningMessage, string source = null, bool showToUser = false)
        {
            if (string.IsNullOrEmpty(warningMessage)) return;

            // Log the warning
            _loggingService.LogWarning(warningMessage, source);

            if (showToUser)
            {
                ShowWarningToUser(warningMessage, "Warning");
            }
        }

        public void ShowInfo(string message, string title = null)
        {
            if (string.IsNullOrEmpty(message)) return;

            _loggingService.LogInfo($"Info message shown to user: {message}");
            
            MessageBox.Show(message, title ?? "Information", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        public void ShowSuccess(string message, string title = null)
        {
            if (string.IsNullOrEmpty(message)) return;

            _loggingService.LogInfo($"Success message shown to user: {message}");
            
            MessageBox.Show(message, title ?? "Success", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private string GetUserFriendlyMessage(Exception exception, string userMessage)
        {
            if (!string.IsNullOrEmpty(userMessage))
            {
                return userMessage;
            }

            // Provide user-friendly messages for common exception types
            return exception switch
            {
                FileNotFoundException => "The required file could not be found. Please check the file path and try again.",
                UnauthorizedAccessException => "Access denied. Please check file permissions and try again.",
                DirectoryNotFoundException => "The specified directory could not be found.",
                IOException => "An error occurred while accessing the file system. Please try again.",
                ArgumentException => "Invalid input provided. Please check your data and try again.",
                InvalidOperationException => "The operation could not be completed at this time. Please try again.",
                Autodesk.AutoCAD.Runtime.Exception acadEx => $"AutoCAD error: {acadEx.Message}",
                _ => $"{ApplicationConstants.ERROR_PREFIX}{exception.Message}"
            };
        }

        private void ShowErrorToUser(string message, string title)
        {
            try
            {
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch (Exception ex)
            {
                // Fallback to debug output if MessageBox fails
                System.Diagnostics.Debug.WriteLine($"Failed to show error message: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Original error: {message}");
            }
        }

        private void ShowWarningToUser(string message, string title)
        {
            try
            {
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            catch (Exception ex)
            {
                // Fallback to debug output if MessageBox fails
                System.Diagnostics.Debug.WriteLine($"Failed to show warning message: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Original warning: {message}");
            }
        }
    }
}
