using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using System;
using System.Collections.Generic;
using System.Linq;
using NetTopologySuite.Geometries;
using NetTopologySuite.Algorithm;
using NetTopologySuite.Operation.Buffer;
using IECAD.Services.OutlineWalker;

namespace IECAD.Services
{
    /// <summary>
    /// 几何算法服务 - 使用NetTopologySuite进行高级几何运算
    /// </summary>
    public class GeometryAlgorithmService : IDisposable
    {
        private readonly GeometryFactory _geometryFactory;
        private readonly GeometryValidationService _validationService;
        private readonly double _tolerance;
        private readonly int _maxEntityCount;
        private readonly int _maxPointCount;
        private bool _disposed = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="tolerance">几何运算容差</param>
        /// <param name="maxEntityCount">最大实体数量限制</param>
        /// <param name="maxPointCount">最大点数量限制</param>
        public GeometryAlgorithmService(double tolerance = 1e-6, int maxEntityCount = 10000, int maxPointCount = 50000)
        {
            _tolerance = tolerance;
            _maxEntityCount = maxEntityCount;
            _maxPointCount = maxPointCount;
            _geometryFactory = new GeometryFactory();
            _validationService = new GeometryValidationService(tolerance);
        }

        /// <summary>
        /// 从AutoCAD实体集合提取凸包轮廓
        /// </summary>
        /// <param name="entities">AutoCAD实体集合</param>
        /// <returns>凸包轮廓点集</returns>
        public List<Point3d> ExtractConvexHull(IEnumerable<Entity> entities)
        {
            try
            {
                // 1. 验证输入实体
                var validation = _validationService.ValidateEntities(entities);
                if (!validation.IsValid)
                {
                    throw new ArgumentException($"实体验证失败: {validation.ErrorMessage}");
                }

                // 2. 将AutoCAD实体转换为点集
                var points = ExtractPointsFromEntities(validation.ValidEntities);

                // 3. 验证点集
                var pointValidation = _validationService.ValidatePoints(points);
                if (!pointValidation.IsValid)
                {
                    throw new ArgumentException($"点集验证失败: {pointValidation.ErrorMessage}");
                }

                // 4. 转换为NetTopologySuite坐标
                var coordinates = pointValidation.ValidPoints.Select(p => new Coordinate(p.X, p.Y)).ToArray();

                // 5. 创建几何对象并计算凸包
                var multiPoint = _geometryFactory.CreateMultiPointFromCoords(coordinates);
                var convexHull = multiPoint.ConvexHull();

                // 6. 转换回AutoCAD Point3d
                return ConvertGeometryToPoints(convexHull);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"凸包计算失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 从AutoCAD实体集合提取边界轮廓（非凸包）
        /// </summary>
        /// <param name="entities">AutoCAD实体集合</param>
        /// <param name="bufferDistance">缓冲距离，用于连接断开的几何</param>
        /// <returns>边界轮廓点集</returns>
        public List<Point3d> ExtractBoundary(IEnumerable<Entity> entities, double bufferDistance = 0.1)
        {
            try
            {
                // 1. 将AutoCAD实体转换为NetTopologySuite几何对象
                var geometries = ConvertEntitiesToGeometries(entities);
                if (!geometries.Any())
                {
                    return new List<Point3d>();
                }

                // 2. 合并所有几何对象
                var union = UnionGeometries(geometries);
                if (union == null)
                {
                    return new List<Point3d>();
                }

                // 3. 如果需要，应用缓冲区来连接断开的部分
                if (bufferDistance > 0)
                {
                    union = union.Buffer(bufferDistance);
                    union = union.Buffer(-bufferDistance); // 收缩回原始大小
                }

                // 4. 提取边界
                var boundary = union.Boundary;
                return ConvertGeometryToPoints(boundary);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"边界提取失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 计算Alpha Shape（凹包）
        /// </summary>
        /// <param name="entities">AutoCAD实体集合</param>
        /// <param name="alpha">Alpha参数，控制凹包的"凹度"</param>
        /// <returns>Alpha Shape轮廓点集</returns>
        public List<Point3d> ExtractAlphaShape(IEnumerable<Entity> entities, double alpha = 1.0)
        {
            try
            {
                // 注意：NetTopologySuite没有直接的Alpha Shape算法
                // 这里使用缓冲区操作来近似实现凹包效果
                var points = ExtractPointsFromEntities(entities);
                if (points.Count < 3)
                {
                    return points;
                }

                var coordinates = points.Select(p => new Coordinate(p.X, p.Y)).ToArray();
                var multiPoint = _geometryFactory.CreateMultiPointFromCoords(coordinates);

                // 使用负缓冲区创建凹包效果
                var buffered = multiPoint.Buffer(alpha);
                var eroded = buffered.Buffer(-alpha * 0.8); // 稍微小一点的收缩

                return ConvertGeometryToPoints(eroded.Boundary);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Alpha Shape计算失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 从AutoCAD实体提取所有关键点
        /// </summary>
        /// <param name="entities">实体集合</param>
        /// <returns>点集合</returns>
        private List<Point3d> ExtractPointsFromEntities(IEnumerable<Entity> entities)
        {
            var points = new List<Point3d>();

            foreach (var entity in entities)
            {
                switch (entity)
                {
                    case Line line:
                        points.Add(line.StartPoint);
                        points.Add(line.EndPoint);
                        break;

                    case Arc arc:
                        points.Add(arc.StartPoint);
                        points.Add(arc.EndPoint);
                        // 添加圆弧上的采样点
                        points.AddRange(SampleArcPoints(arc, 10));
                        break;

                    case Circle circle:
                        // 圆周上采样点
                        points.AddRange(SampleCirclePoints(circle, 16));
                        break;

                    case Polyline polyline:
                        for (int i = 0; i < polyline.NumberOfVertices; i++)
                        {
                            points.Add(polyline.GetPoint3dAt(i));
                        }
                        break;

                    case Spline spline:
                        // 样条曲线采样点
                        points.AddRange(SampleSplinePoints(spline, 20));
                        break;

                    default:
                        // 尝试获取实体的边界框角点
                        try
                        {
                            var extents = entity.GeometricExtents;
                            points.Add(extents.MinPoint);
                            points.Add(extents.MaxPoint);
                            points.Add(new Point3d(extents.MinPoint.X, extents.MaxPoint.Y, 0));
                            points.Add(new Point3d(extents.MaxPoint.X, extents.MinPoint.Y, 0));
                        }
                        catch
                        {
                            // 忽略无法获取边界的实体
                        }
                        break;
                }
            }

            // 去除重复点
            return RemoveDuplicatePoints(points);
        }

        /// <summary>
        /// 将AutoCAD实体转换为NetTopologySuite几何对象
        /// </summary>
        /// <param name="entities">实体集合</param>
        /// <returns>几何对象集合</returns>
        private List<Geometry> ConvertEntitiesToGeometries(IEnumerable<Entity> entities)
        {
            var geometries = new List<Geometry>();

            foreach (var entity in entities)
            {
                try
                {
                    var geometry = ConvertEntityToGeometry(entity);
                    if (geometry != null)
                    {
                        geometries.Add(geometry);
                    }
                }
                catch
                {
                    // 忽略转换失败的实体
                }
            }

            return geometries;
        }

        /// <summary>
        /// 将单个AutoCAD实体转换为NetTopologySuite几何对象
        /// </summary>
        /// <param name="entity">AutoCAD实体</param>
        /// <returns>几何对象</returns>
        private Geometry ConvertEntityToGeometry(Entity entity)
        {
            switch (entity)
            {
                case Line line:
                    var lineCoords = new[]
                    {
                        new Coordinate(line.StartPoint.X, line.StartPoint.Y),
                        new Coordinate(line.EndPoint.X, line.EndPoint.Y)
                    };
                    return _geometryFactory.CreateLineString(lineCoords);

                case Polyline polyline:
                    var polyCoords = new List<Coordinate>();
                    for (int i = 0; i < polyline.NumberOfVertices; i++)
                    {
                        var pt = polyline.GetPoint3dAt(i);
                        polyCoords.Add(new Coordinate(pt.X, pt.Y));
                    }
                    if (polyline.Closed && polyCoords.Count > 2)
                    {
                        polyCoords.Add(polyCoords[0]); // 闭合多边形
                        return _geometryFactory.CreatePolygon(polyCoords.ToArray());
                    }
                    return _geometryFactory.CreateLineString(polyCoords.ToArray());

                case Arc arc:
                    var arcPoints = SampleArcPoints(arc, 20);
                    var arcCoords = arcPoints.Select(p => new Coordinate(p.X, p.Y)).ToArray();
                    return _geometryFactory.CreateLineString(arcCoords);

                case Circle circle:
                    var circlePoints = SampleCirclePoints(circle, 32);
                    var circleCoords = circlePoints.Select(p => new Coordinate(p.X, p.Y)).ToList();
                    circleCoords.Add(circleCoords[0]); // 闭合圆
                    return _geometryFactory.CreatePolygon(circleCoords.ToArray());

                default:
                    return null;
            }
        }

        /// <summary>
        /// 合并多个几何对象
        /// </summary>
        /// <param name="geometries">几何对象集合</param>
        /// <returns>合并后的几何对象</returns>
        private Geometry UnionGeometries(List<Geometry> geometries)
        {
            if (!geometries.Any()) return null;
            if (geometries.Count == 1) return geometries[0];

            Geometry result = geometries[0];
            for (int i = 1; i < geometries.Count; i++)
            {
                result = result.Union(geometries[i]);
            }
            return result;
        }

        /// <summary>
        /// 将NetTopologySuite几何对象转换为AutoCAD点集
        /// </summary>
        /// <param name="geometry">几何对象</param>
        /// <returns>点集合</returns>
        private List<Point3d> ConvertGeometryToPoints(Geometry geometry)
        {
            var points = new List<Point3d>();

            if (geometry == null) return points;

            var coordinates = geometry.Coordinates;
            foreach (var coord in coordinates)
            {
                points.Add(new Point3d(coord.X, coord.Y, 0));
            }

            return RemoveDuplicatePoints(points);
        }

        /// <summary>
        /// 在圆弧上采样点
        /// </summary>
        /// <param name="arc">圆弧对象</param>
        /// <param name="sampleCount">采样点数量</param>
        /// <returns>采样点集合</returns>
        private List<Point3d> SampleArcPoints(Arc arc, int sampleCount)
        {
            var points = new List<Point3d>();
            var startAngle = arc.StartAngle;
            var endAngle = arc.EndAngle;
            var center = arc.Center;
            var radius = arc.Radius;

            // 处理跨越0度的情况
            if (endAngle < startAngle)
            {
                endAngle += 2 * Math.PI;
            }

            var angleStep = (endAngle - startAngle) / (sampleCount - 1);

            for (int i = 0; i < sampleCount; i++)
            {
                var angle = startAngle + i * angleStep;
                var x = center.X + radius * Math.Cos(angle);
                var y = center.Y + radius * Math.Sin(angle);
                points.Add(new Point3d(x, y, center.Z));
            }

            return points;
        }

        /// <summary>
        /// 在圆周上采样点
        /// </summary>
        /// <param name="circle">圆对象</param>
        /// <param name="sampleCount">采样点数量</param>
        /// <returns>采样点集合</returns>
        private List<Point3d> SampleCirclePoints(Circle circle, int sampleCount)
        {
            var points = new List<Point3d>();
            var center = circle.Center;
            var radius = circle.Radius;
            var angleStep = 2 * Math.PI / sampleCount;

            for (int i = 0; i < sampleCount; i++)
            {
                var angle = i * angleStep;
                var x = center.X + radius * Math.Cos(angle);
                var y = center.Y + radius * Math.Sin(angle);
                points.Add(new Point3d(x, y, center.Z));
            }

            return points;
        }



        /// <summary>
        /// 在圆周上采样点
        /// </summary>
        /// <param name="circle">圆对象</param>
        /// <param name="sampleCount">采样点数量</param>
        /// <returns>采样点集合</returns>
        private List<Point3d> SampleCirclePoints(Circle circle, int sampleCount)
        {
            var points = new List<Point3d>();
            var center = circle.Center;
            var radius = circle.Radius;
            var angleStep = 2 * Math.PI / sampleCount;

            for (int i = 0; i < sampleCount; i++)
            {
                var angle = i * angleStep;
                var x = center.X + radius * Math.Cos(angle);
                var y = center.Y + radius * Math.Sin(angle);
                points.Add(new Point3d(x, y, center.Z));
            }

            return points;
        }

        /// <summary>
        /// 在样条曲线上采样点
        /// </summary>
        /// <param name="spline">样条曲线对象</param>
        /// <param name="sampleCount">采样点数量</param>
        /// <returns>采样点集合</returns>
        private List<Point3d> SampleSplinePoints(Spline spline, int sampleCount)
        {
            var points = new List<Point3d>();

            try
            {
                var startParam = spline.StartParameter;
                var endParam = spline.EndParameter;
                var paramStep = (endParam - startParam) / (sampleCount - 1);

                for (int i = 0; i < sampleCount; i++)
                {
                    var param = startParam + i * paramStep;
                    var point = spline.GetPointAtParameter(param);
                    points.Add(point);
                }
            }
            catch
            {
                // 如果参数化采样失败，尝试使用控制点
                for (int i = 0; i < spline.NumControlPoints; i++)
                {
                    points.Add(spline.GetControlPointAt(i));
                }
            }

            return points;
        }

        /// <summary>
        /// 去除重复点
        /// </summary>
        /// <param name="points">点集合</param>
        /// <returns>去重后的点集合</returns>
        private List<Point3d> RemoveDuplicatePoints(List<Point3d> points)
        {
            var uniquePoints = new List<Point3d>();

            foreach (var point in points)
            {
                bool isDuplicate = false;
                foreach (var existing in uniquePoints)
                {
                    if (Math.Abs(point.X - existing.X) < _tolerance &&
                        Math.Abs(point.Y - existing.Y) < _tolerance)
                    {
                        isDuplicate = true;
                        break;
                    }
                }

                if (!isDuplicate)
                {
                    uniquePoints.Add(point);
                }
            }

            return uniquePoints;
        }

        /// <summary>
        /// 简化轮廓点集（Douglas-Peucker算法）
        /// </summary>
        /// <param name="points">原始点集</param>
        /// <param name="tolerance">简化容差</param>
        /// <returns>简化后的点集</returns>
        public List<Point3d> SimplifyOutline(List<Point3d> points, double tolerance)
        {
            if (points == null || points.Count < 3)
                return points ?? new List<Point3d>();

            // 使用NetTopologySuite的简化算法
            try
            {
                var coordinates = points.Select(p => new Coordinate(p.X, p.Y)).ToArray();
                var lineString = _geometryFactory.CreateLineString(coordinates);
                var simplified = NetTopologySuite.Simplify.DouglasPeuckerSimplifier.Simplify(lineString, tolerance);

                return ConvertGeometryToPoints(simplified);
            }
            catch
            {
                // 如果简化失败，返回原始点集
                return points;
            }
        }
    }
}
