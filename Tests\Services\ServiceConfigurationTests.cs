using Microsoft.VisualStudio.TestTools.UnitTesting;
using IECAD.Services;
using IECAD.Tests.TestHelpers;

namespace IECAD.Tests.Services
{
    [TestClass]
    public class ServiceConfigurationTests : TestBase
    {
        [TestMethod]
        public void Container_ShouldReturnValidContainer()
        {
            // Act
            var container = ServiceConfiguration.Container;

            // Assert
            Assert.IsNotNull(container);
        }

        [TestMethod]
        public void Resolve_RegisteredService_ShouldReturnInstance()
        {
            // Act
            var loggingService = ServiceConfiguration.Resolve<ILoggingService>();

            // Assert
            Assert.IsNotNull(loggingService);
        }

        [TestMethod]
        public void IsRegistered_RegisteredService_ShouldReturnTrue()
        {
            // Act & Assert
            Assert.IsTrue(ServiceConfiguration.IsRegistered<ILoggingService>());
        }

        [TestMethod]
        public void Reset_ShouldClearContainer()
        {
            // Arrange
            var originalContainer = ServiceConfiguration.Container;

            // Act
            ServiceConfiguration.Reset();
            var newContainer = ServiceConfiguration.Container;

            // Assert
            Assert.AreNotSame(originalContainer, newContainer);
        }
    }
}
