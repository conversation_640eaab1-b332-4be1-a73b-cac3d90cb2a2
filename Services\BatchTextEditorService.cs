using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.Windows;
using Autodesk.AutoCAD.GraphicsInterface;
using Autodesk.AutoCAD.Geometry;
using AcadApp = Autodesk.AutoCAD.ApplicationServices.Application;
using IECAD.Views;

namespace IECAD.Services
{
    public class BatchTextEditorService
    {
        private static PaletteSet _ps;
        private static BatchEditTextPalette _paletteCtrl;

        /// <summary>
        /// 执行批量文字编辑命令，弹出或更新编辑面板
        /// </summary>
        public void BatchEditTexts()
        {
            var doc = AcadApp.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;

            // 1. 选择多行文字或单行文字
            var pso = new PromptSelectionOptions { MessageForAdding = "\n请选择要批量编辑的文字对象（多行文字或文字）：" };
            var filter = new SelectionFilter(new TypedValue[]
            {
                new TypedValue((int)DxfCode.Start, "MTEXT,TEXT")
            });
            var psr = ed.GetSelection(pso, filter);
            if (psr.Status != PromptStatus.OK) return;

            var ids = psr.Value.GetObjectIds().ToArray();
            if (ids.Length == 0) return;

            // 2. 读取原文字内容
            var originals = new List<string>();
            using (var tr = doc.TransactionManager.StartTransaction())
            {
                foreach (var id in ids)
                {
                    var ent = tr.GetObject(id, OpenMode.ForRead);
                    if (ent is MText mtxt)
                        originals.Add(mtxt.Contents);
                    else if (ent is DBText txt)
                        originals.Add(txt.TextString);
                    else
                        originals.Add(string.Empty);
                }
                tr.Commit();
            }

            // 3. 在 AutoCAD Palette 窗口中显示或更新编辑控件，实现实时高亮
            if (_ps == null)
            {
                _ps = new PaletteSet("批量文字编辑") { Style = PaletteSetStyles.NameEditable };
                _paletteCtrl = new BatchEditTextPalette(ids, originals);
                _paletteCtrl.ParentPaletteSet = _ps;
                _ps.Add("编辑", _paletteCtrl);

                // 定时器监控窗口关闭
                var timer = new Timer { Interval = 500 };
                bool wasVisible = false;
                timer.Tick += (s, e) =>
                {
                    if (_ps == null) return;
                    if (!wasVisible && _ps.Visible) wasVisible = true;
                    if (wasVisible && !_ps.Visible)
                    {
                        _paletteCtrl.ClearHighlight();
                        wasVisible = false;
                    }
                };
                timer.Start();
            }
            else
            {
                _paletteCtrl.UpdateData(ids, originals);
            }

            _ps.Visible = true;
        }
    }
}
