using System;
using System.Windows.Forms;

namespace IECAD.Services
{
    /// <summary>
    /// 提供文件对话框服务的通用类
    /// </summary>
    public class FileDialogService
    {
        /// <summary>
        /// 显示Excel文件选择对话框
        /// </summary>
        /// <param name="allowLegacyFormat">是否允许选择旧版Excel格式(.xls)</param>
        /// <returns>选中的文件路径，如果取消则返回null</returns>
        public static string ShowExcelFileDialog(bool allowLegacyFormat = false)
        {
            string filter = allowLegacyFormat
                ? "Excel Files (*.xls;*.xlsx)|*.xls;*.xlsx"
                : "Excel files (*.xlsx)|*.xlsx|All files (*.*)|*.*";

            using (OpenFileDialog openFileDialog = new OpenFileDialog
            {
                InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                Title = "Select Excel File",
                CheckFileExists = true,
                CheckPathExists = true,
                DefaultExt = "xlsx",
                Filter = filter,
                FilterIndex = 1,
                RestoreDirectory = true,
                ReadOnlyChecked = true,
                ShowReadOnly = true
            })
            {
                return openFileDialog.ShowDialog() == DialogResult.OK ? openFileDialog.FileName : null;
            }
        }
    }
}
