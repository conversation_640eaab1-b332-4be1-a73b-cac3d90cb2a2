using System;

namespace IECAD.Services
{
    /// <summary>
    /// Interface for error handling service to provide consistent error handling across the application
    /// </summary>
    public interface IErrorHandlingService
    {
        /// <summary>
        /// Handle an exception with logging and user notification
        /// </summary>
        /// <param name="exception">The exception to handle</param>
        /// <param name="userMessage">User-friendly message to display (optional)</param>
        /// <param name="source">The source of the error (optional)</param>
        /// <param name="showToUser">Whether to show the error to the user (default: true)</param>
        void HandleException(Exception exception, string userMessage = null, string source = null, bool showToUser = true);

        /// <summary>
        /// Handle an error with logging and user notification
        /// </summary>
        /// <param name="errorMessage">The error message</param>
        /// <param name="source">The source of the error (optional)</param>
        /// <param name="showToUser">Whether to show the error to the user (default: true)</param>
        void HandleError(string errorMessage, string source = null, bool showToUser = true);

        /// <summary>
        /// Handle a warning with logging and optional user notification
        /// </summary>
        /// <param name="warningMessage">The warning message</param>
        /// <param name="source">The source of the warning (optional)</param>
        /// <param name="showToUser">Whether to show the warning to the user (default: false)</param>
        void HandleWarning(string warningMessage, string source = null, bool showToUser = false);

        /// <summary>
        /// Show an informational message to the user
        /// </summary>
        /// <param name="message">The message to show</param>
        /// <param name="title">The title of the message box (optional)</param>
        void ShowInfo(string message, string title = null);

        /// <summary>
        /// Show a success message to the user
        /// </summary>
        /// <param name="message">The success message to show</param>
        /// <param name="title">The title of the message box (optional)</param>
        void ShowSuccess(string message, string title = null);
    }
}
