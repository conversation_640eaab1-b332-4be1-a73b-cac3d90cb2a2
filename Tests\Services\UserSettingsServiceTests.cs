using Microsoft.VisualStudio.TestTools.UnitTesting;
using IECAD.Services;
using IECAD.Tests.TestHelpers;
using IECAD.Constants;

namespace IECAD.Tests.Services
{
    [TestClass]
    public class UserSettingsServiceTests : TestBase
    {
        private UserSettingsService _service;
        private const string TEST_KEY = "TestKey";
        private const string TEST_STRING_VALUE = "TestValue";
        private const double TEST_DOUBLE_VALUE = 123.45;
        private const int TEST_INT_VALUE = 42;
        private const bool TEST_BOOL_VALUE = true;

        [TestInitialize]
        public void Setup()
        {
            _service = new UserSettingsService();
            // Clean up any existing test values
            _service.RemoveSetting(TEST_KEY);
        }

        [TestCleanup]
        public void Cleanup()
        {
            // Clean up test values
            _service?.RemoveSetting(TEST_KEY);
        }

        [TestMethod]
        public void Constructor_WithNullParameters_ShouldNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => new UserSettingsService());
        }

        [TestMethod]
        public void Constructor_WithValidServices_ShouldNotThrow()
        {
            // Arrange
            var loggingService = ServiceConfiguration.Resolve<ILoggingService>();
            var errorService = ServiceConfiguration.Resolve<IErrorHandlingService>();

            // Act & Assert
            Assert.DoesNotThrow(() => new UserSettingsService(loggingService, errorService));
        }

        [TestMethod]
        public void SetDouble_ValidValue_ShouldReturnTrue()
        {
            // Act
            bool result = _service.SetDouble(TEST_KEY, TEST_DOUBLE_VALUE);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void GetDouble_ExistingValue_ShouldReturnCorrectValue()
        {
            // Arrange
            _service.SetDouble(TEST_KEY, TEST_DOUBLE_VALUE);

            // Act
            double result = _service.GetDouble(TEST_KEY, 0.0);

            // Assert
            Assert.AreEqual(TEST_DOUBLE_VALUE, result, 0.001);
        }

        [TestMethod]
        public void GetDouble_NonExistingValue_ShouldReturnDefault()
        {
            // Arrange
            const double defaultValue = 999.99;

            // Act
            double result = _service.GetDouble("NonExistentKey", defaultValue);

            // Assert
            Assert.AreEqual(defaultValue, result, 0.001);
        }

        [TestMethod]
        public void SetString_ValidValue_ShouldReturnTrue()
        {
            // Act
            bool result = _service.SetString(TEST_KEY, TEST_STRING_VALUE);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void GetString_ExistingValue_ShouldReturnCorrectValue()
        {
            // Arrange
            _service.SetString(TEST_KEY, TEST_STRING_VALUE);

            // Act
            string result = _service.GetString(TEST_KEY, "");

            // Assert
            Assert.AreEqual(TEST_STRING_VALUE, result);
        }

        [TestMethod]
        public void GetString_NonExistingValue_ShouldReturnDefault()
        {
            // Arrange
            const string defaultValue = "DefaultValue";

            // Act
            string result = _service.GetString("NonExistentKey", defaultValue);

            // Assert
            Assert.AreEqual(defaultValue, result);
        }

        [TestMethod]
        public void SetInt_ValidValue_ShouldReturnTrue()
        {
            // Act
            bool result = _service.SetInt(TEST_KEY, TEST_INT_VALUE);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void GetInt_ExistingValue_ShouldReturnCorrectValue()
        {
            // Arrange
            _service.SetInt(TEST_KEY, TEST_INT_VALUE);

            // Act
            int result = _service.GetInt(TEST_KEY, 0);

            // Assert
            Assert.AreEqual(TEST_INT_VALUE, result);
        }

        [TestMethod]
        public void SetBool_ValidValue_ShouldReturnTrue()
        {
            // Act
            bool result = _service.SetBool(TEST_KEY, TEST_BOOL_VALUE);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void GetBool_ExistingValue_ShouldReturnCorrectValue()
        {
            // Arrange
            _service.SetBool(TEST_KEY, TEST_BOOL_VALUE);

            // Act
            bool result = _service.GetBool(TEST_KEY, false);

            // Assert
            Assert.AreEqual(TEST_BOOL_VALUE, result);
        }

        [TestMethod]
        public void HasSetting_ExistingSetting_ShouldReturnTrue()
        {
            // Arrange
            _service.SetString(TEST_KEY, TEST_STRING_VALUE);

            // Act
            bool result = _service.HasSetting(TEST_KEY);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void HasSetting_NonExistingSetting_ShouldReturnFalse()
        {
            // Act
            bool result = _service.HasSetting("NonExistentKey");

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void RemoveSetting_ExistingSetting_ShouldReturnTrue()
        {
            // Arrange
            _service.SetString(TEST_KEY, TEST_STRING_VALUE);

            // Act
            bool result = _service.RemoveSetting(TEST_KEY);

            // Assert
            Assert.IsTrue(result);
            Assert.IsFalse(_service.HasSetting(TEST_KEY));
        }

        [TestMethod]
        public void BlockArrangementSpacing_DefaultValue_ShouldMatchConstant()
        {
            // Act
            double result = _service.GetDouble(ApplicationConstants.SETTING_BLOCK_ARRANGEMENT_SPACING, 
                ApplicationConstants.DEFAULT_BLOCK_ARRANGEMENT_SPACING);

            // Assert
            Assert.AreEqual(ApplicationConstants.DEFAULT_BLOCK_ARRANGEMENT_SPACING, result, 0.001);
        }

        [TestMethod]
        public void BlockArrangementSpacing_SetAndGet_ShouldPersist()
        {
            // Arrange
            const double testSpacing = 1500.0;

            // Act
            bool setResult = _service.SetDouble(ApplicationConstants.SETTING_BLOCK_ARRANGEMENT_SPACING, testSpacing);
            double getResult = _service.GetDouble(ApplicationConstants.SETTING_BLOCK_ARRANGEMENT_SPACING, 
                ApplicationConstants.DEFAULT_BLOCK_ARRANGEMENT_SPACING);

            // Assert
            Assert.IsTrue(setResult);
            Assert.AreEqual(testSpacing, getResult, 0.001);

            // Cleanup
            _service.RemoveSetting(ApplicationConstants.SETTING_BLOCK_ARRANGEMENT_SPACING);
        }
    }
}
