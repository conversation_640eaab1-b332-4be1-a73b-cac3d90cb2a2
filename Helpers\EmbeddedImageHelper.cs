﻿using System;
using System.IO;
using System.Windows.Media.Imaging;
using System.Reflection;
using System.Windows.Forms;

namespace IECAD.Helpers
{
    public static class EmbeddedImageHelper
    {
        public static BitmapImage LoadImage(string resourcePath)
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                using (var stream = assembly.GetManifestResourceStream(resourcePath))
                {
                    if (stream == null)
                    {
                        // Debug: List all available resources
                        var resources = assembly.GetManifestResourceNames();
                        string availableResources = string.Join("\n", resources);
                        System.Windows.Forms.MessageBox.Show($"Available resources:\n{availableResources}\n\nTrying to find: {resourcePath}");
                        return null;
                    }

                    var image = new BitmapImage();
                    image.BeginInit();
                    image.CacheOption = BitmapCacheOption.OnLoad;
                    image.StreamSource = stream;
                    image.EndInit();
                    image.Freeze(); // Makes the image usable across threads
                    return image;
                }
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"Error loading image: {ex.Message}");
                return null;
            }
        }
    }
}