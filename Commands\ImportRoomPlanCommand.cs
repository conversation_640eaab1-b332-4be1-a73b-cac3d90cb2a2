﻿using Autodesk.AutoCAD.Runtime;
using IECAD.Services;
[assembly: CommandClass(typeof(IECAD.Commands.ImportRoomPlanCommand))]
namespace IECAD.Commands
{
    public class ImportRoomPlanCommand
    {
        [CommandMethod("IMPORTROOMPLAN")]
        public void ImportRoomPlan()
        {
            var roomPlanImporter = new RoomPlanImporter();
            roomPlanImporter.ImportRoomPlan();
        }
    }
}