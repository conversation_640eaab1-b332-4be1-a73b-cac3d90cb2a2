using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace IECAD.Services
{
    /// <summary>
    /// Performance monitoring service implementation
    /// </summary>
    public class PerformanceMonitoringService : IPerformanceMonitoringService
    {
        private readonly ConcurrentDictionary<string, List<PerformanceMeasurement>> _measurements = 
            new ConcurrentDictionary<string, List<PerformanceMeasurement>>();
        private readonly ILoggingService _loggingService;

        public PerformanceMonitoringService(ILoggingService loggingService = null)
        {
            _loggingService = loggingService;
        }

        public IDisposable StartMeasurement(string operationName)
        {
            if (string.IsNullOrEmpty(operationName))
                throw new ArgumentNullException(nameof(operationName));

            return new PerformanceMeasurementToken(operationName, this);
        }

        public void RecordMeasurement(string operationName, TimeSpan duration)
        {
            if (string.IsNullOrEmpty(operationName)) return;

            var measurement = new PerformanceMeasurement
            {
                Duration = duration,
                Timestamp = DateTime.UtcNow
            };

            _measurements.AddOrUpdate(operationName,
                new List<PerformanceMeasurement> { measurement },
                (key, existing) =>
                {
                    lock (existing)
                    {
                        existing.Add(measurement);
                        return existing;
                    }
                });

            _loggingService?.LogDebug($"Performance: {operationName} took {duration.TotalMilliseconds:F2}ms", "PerformanceMonitoringService");
        }

        public PerformanceStatistics GetStatistics(string operationName)
        {
            if (string.IsNullOrEmpty(operationName)) return null;

            if (!_measurements.TryGetValue(operationName, out List<PerformanceMeasurement> measurements))
                return null;

            lock (measurements)
            {
                if (measurements.Count == 0) return null;

                var durations = measurements.Select(m => m.Duration).ToList();
                var timestamps = measurements.Select(m => m.Timestamp).ToList();

                return new PerformanceStatistics
                {
                    OperationName = operationName,
                    CallCount = measurements.Count,
                    TotalDuration = TimeSpan.FromTicks(durations.Sum(d => d.Ticks)),
                    AverageDuration = TimeSpan.FromTicks((long)durations.Average(d => d.Ticks)),
                    MinDuration = durations.Min(),
                    MaxDuration = durations.Max(),
                    FirstCall = timestamps.Min(),
                    LastCall = timestamps.Max()
                };
            }
        }

        public Dictionary<string, PerformanceStatistics> GetAllStatistics()
        {
            var result = new Dictionary<string, PerformanceStatistics>();

            foreach (var operationName in _measurements.Keys)
            {
                var stats = GetStatistics(operationName);
                if (stats != null)
                {
                    result[operationName] = stats;
                }
            }

            return result;
        }

        public void Clear()
        {
            _measurements.Clear();
            _loggingService?.LogDebug("All performance measurements cleared", "PerformanceMonitoringService");
        }

        public void Clear(string operationName)
        {
            if (string.IsNullOrEmpty(operationName)) return;

            if (_measurements.TryRemove(operationName, out _))
            {
                _loggingService?.LogDebug($"Performance measurements cleared for operation: {operationName}", "PerformanceMonitoringService");
            }
        }

        private class PerformanceMeasurement
        {
            public TimeSpan Duration { get; set; }
            public DateTime Timestamp { get; set; }
        }

        private class PerformanceMeasurementToken : IDisposable
        {
            private readonly string _operationName;
            private readonly PerformanceMonitoringService _service;
            private readonly Stopwatch _stopwatch;
            private bool _disposed = false;

            public PerformanceMeasurementToken(string operationName, PerformanceMonitoringService service)
            {
                _operationName = operationName;
                _service = service;
                _stopwatch = Stopwatch.StartNew();
            }

            public void Dispose()
            {
                if (!_disposed)
                {
                    _stopwatch.Stop();
                    _service.RecordMeasurement(_operationName, _stopwatch.Elapsed);
                    _disposed = true;
                }
            }
        }
    }
}
