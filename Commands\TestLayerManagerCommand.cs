using Autodesk.AutoCAD.Runtime;
using IECAD.Services;
using IECAD.ViewModels;

[assembly: CommandClass(typeof(IECAD.Commands.TestLayerManagerCommand))]

namespace IECAD.Commands
{
    /// <summary>
    /// Command to test the LayerManagerViewModel functionality
    /// </summary>
    public class TestLayerManagerCommand
    {
        [CommandMethod("TESTLAYERMANAGER")]
        public void TestLayerManager()
        {
            try
            {
                // Use service container to resolve services
                var autoCADService = ServiceConfiguration.Resolve<IAutoCADService>();
                var errorHandlingService = ServiceConfiguration.Resolve<IErrorHandlingService>();
                var loggingService = ServiceConfiguration.Resolve<ILoggingService>();

                loggingService?.LogInfo("Testing LayerManagerViewModel", "TestLayerManagerCommand");

                // Create LayerManagerViewModel
                var viewModel = new LayerManagerViewModel(autoCADService);

                // Test basic functionality
                int layerCount = viewModel.Layers?.Count ?? 0;
                
                errorHandlingService?.ShowInfo($"LayerManagerViewModel test completed successfully!\n\nLayers loaded: {layerCount}", "Layer Manager Test");
                
                loggingService?.LogInfo($"LayerManagerViewModel test completed. Layers: {layerCount}", "TestLayerManagerCommand");

                // Clean up
                viewModel?.Dispose();
            }
            catch (System.Exception ex)
            {
                // Fallback error handling
                System.Windows.MessageBox.Show($"LayerManagerViewModel test failed: {ex.Message}\n\nStack trace:\n{ex.StackTrace}", 
                    "Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }
    }
}
