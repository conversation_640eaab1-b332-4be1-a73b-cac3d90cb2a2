# Block Arrangement Feature Integration Test

## Overview
This document describes how to test the new Block Arrangement feature in the AutoCAD plugin.

## Prerequisites
1. AutoCAD 2024 installed
2. Plugin loaded and sidebar visible
3. Drawing with multiple block references

## Test Scenarios

### Scenario 1: Basic Block Arrangement
1. **Setup**: Create a drawing with 3-5 block references scattered randomly
2. **Execute**: 
   - Type `ARRANGEBLOCKS` in command line OR click "设备排列" button in sidebar
   - Select all blocks when prompted
   - Choose one block as reference when prompted
   - Enter spacing distance (e.g., 1000)
3. **Expected Result**: Blocks should be arranged in a line based on their bounding box direction

### Scenario 2: X-Direction Arrangement
1. **Setup**: Create blocks spread more horizontally than vertically
2. **Execute**: Run arrangement command
3. **Expected Result**: Blocks should be arranged along X-axis

### Scenario 3: Y-Direction Arrangement
1. **Setup**: Create blocks spread more vertically than horizontally
2. **Execute**: Run arrangement command
3. **Expected Result**: Blocks should be arranged along Y-axis

### Scenario 4: Error Handling
1. **Test**: Select only 1 block
   - **Expected**: Error message about minimum 2 blocks required
2. **Test**: Cancel reference block selection
   - **Expected**: Command cancels gracefully
3. **Test**: Enter invalid spacing (negative or zero)
   - **Expected**: Error message about invalid spacing

## Manual Testing Steps

### Step 1: Load Plugin
```
NETLOAD "path\to\AutoCAD 2024 Plugin_WPF.dll"
```

### Step 2: Show Sidebar
```
SHOWSIDEBAR
```

### Step 3: Create Test Blocks
```
CIRCLE 0,0 100
BLOCK TestBlock1 0,0 (select circle)
INSERT TestBlock1 1000,500
INSERT TestBlock1 2000,1000
INSERT TestBlock1 3000,800
INSERT TestBlock1 1500,1500
```

### Step 4: Test Arrangement
```
ARRANGEBLOCKS
```

## Expected Behavior

1. **Selection Phase**: User can select multiple blocks with standard AutoCAD selection
2. **Reference Selection**: Highlighted blocks help user identify which to choose as reference
3. **Spacing Input**: Standard AutoCAD distance input with remembered default value
4. **Arrangement**: Blocks move to new positions maintaining their original orientation
5. **Settings Persistence**: Successfully used spacing is saved for next session
6. **Feedback**: Success message shows number of blocks arranged and spacing used

## Settings Persistence Testing

### Test Scenario: First Time Use
1. **Setup**: Fresh AutoCAD session with no previous settings
2. **Execute**: Run ARRANGEBLOCKS command
3. **Expected**: Default spacing of 1000.0 is shown in prompt
4. **Verify**: Enter custom spacing (e.g., 1500) and complete arrangement
5. **Expected**: Spacing is saved to registry/AutoCAD variables

### Test Scenario: Subsequent Use
1. **Setup**: After completing first test scenario
2. **Execute**: Run ARRANGEBLOCKS command again
3. **Expected**: Prompt shows "last used: 1500" and uses 1500 as default
4. **Verify**: Press Enter to accept default or enter new value
5. **Expected**: New value becomes the remembered default

### Test Scenario: Cross-Session Persistence
1. **Setup**: Complete arrangement with custom spacing
2. **Execute**: Close and restart AutoCAD, reload plugin
3. **Execute**: Run ARRANGEBLOCKS command
4. **Expected**: Last used spacing is still remembered and displayed

## Error Cases to Test

1. No blocks selected
2. Only one block selected
3. Reference block not in original selection
4. Zero or negative spacing
5. No active document
6. Cancelled operations at any step

## Performance Considerations

- Test with 10+ blocks to ensure reasonable performance
- Verify undo functionality works correctly
- Check that highlighting is properly cleaned up

## UI Integration Test

1. Verify "设备排列" button appears in sidebar under "施工图设计" section
2. Verify button tooltip shows correct description
3. Verify button executes ARRANGEBLOCKS command when clicked
4. Verify command works same way whether called from UI or command line

## Registry Verification

### Manual Registry Check
1. **Location**: `HKEY_CURRENT_USER\SOFTWARE\IECAD\Settings`
2. **Key**: `BlockArrangementSpacing`
3. **Type**: String (stored as double in string format)
4. **Verification**: After successful arrangement, check registry contains the spacing value

### AutoCAD System Variable Check
1. **Variable Name**: `IECAD_BLOCKARRANGEMENTSPACING`
2. **Command**: `(getvar "IECAD_BLOCKARRANGEMENTSPACING")` in AutoCAD command line
3. **Expected**: Returns the last used spacing value
4. **Note**: May not work if AutoCAD doesn't support custom system variables

## Troubleshooting Settings Issues

### If Settings Don't Persist
1. Check Windows Registry permissions for current user
2. Verify AutoCAD has write access to system variables
3. Check plugin logs for settings-related errors
4. Test with different spacing values to confirm save/load cycle

### Registry Cleanup (for testing)
1. Delete `HKEY_CURRENT_USER\SOFTWARE\IECAD` key to reset all settings
2. Restart AutoCAD to test fresh installation behavior
