﻿using Autodesk.AutoCAD.Runtime;
using IECAD.Services;
[assembly: CommandClass(typeof(IECAD.Commands.CreateEquipmentCommand))]
namespace IECAD.Commands
{
    public class CreateEquipmentCommand
    {
        [CommandMethod("CREATEEQUIPMENT")]
        public void CreateEquipment()
        {
            // Use service container to resolve dependencies
            var equipmentCreator = ServiceConfiguration.Resolve<IEquipmentCreatorService>();

            // For backward compatibility, we still need to call the command method
            if (equipmentCreator is EquipmentCreator creator)
            {
                creator.CreateEquipment();
            }
        }
    }
}