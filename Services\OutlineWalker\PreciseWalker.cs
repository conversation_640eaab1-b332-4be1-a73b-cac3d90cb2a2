using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using System;
using System.Collections.Generic;
using System.Linq;

namespace IECAD.Services.OutlineWalker
{
    public class PreciseWalker : IOutlineWalker
    {
        private List<LineSegment3d> _segments; // 保留旧字段但不再使用全量交点模式
        private StepwiseIntersectionFinder _finder; // 按需交点扫描器
        private Curve _currentCurve;
        private List<Curve> _curves;
        private List<Point3d> _pathPoints;
        private Point3d _currentPosition;
        private Point3d _startPosition;
        private Vector3d _lastDirection;
        private bool _isCompleted;
        private double _tolerance;

        public bool IsCompleted => _isCompleted;
        public Point3d CurrentPosition => _currentPosition;
        public string StrategyName => "精确步行者";

        public void Initialize(IEnumerable<Entity> entities, double tolerance = 1e-6)
        {
            _tolerance = tolerance;
            // 仅记录曲线集合，不做全量交点求解
            _curves = entities.OfType<Curve>().ToList();
            _finder = new StepwiseIntersectionFinder(_curves, _tolerance);
            _pathPoints = new List<Point3d>();
            _isCompleted = false;

            if (_curves.Count == 0)
            {
                _isCompleted = true;
                return;
            }

            // 选择最低点作为起点
            _startPosition = FindStartPoint(_curves);
            _currentPosition = _startPosition;
            _currentCurve = _finder.GetCurveAtPoint(_currentPosition, null, _tolerance);
            _pathPoints.Add(_startPosition);

            // 初始方向：+X
            _lastDirection = Vector3d.XAxis;
        }

        public WalkerEventResult FindNextEvent()
        {
            if (_isCompleted)
                return new WalkerEventResult { EventType = WalkerEventType.Completed };

            var candidates = FindConnectedPoints(_currentPosition);

            if (candidates.Count == 0)
            {
                // No further points, might be a dead end or a closed loop on a single shape
                _isCompleted = true;
                return new WalkerEventResult { EventType = WalkerEventType.DeadEnd };
            }

            // Find the best next point using the right-hand turn rule
            Point3d nextPoint = FindBestTurn(candidates);
            
            // Check if we have returned to the start point
            if (nextPoint.IsEqualTo(_startPosition, new Tolerance(_tolerance, _tolerance)) && _pathPoints.Count > 1)
            {
                return new WalkerEventResult { EventType = WalkerEventType.OutlineClosed };
            }

            return new WalkerEventResult
            {
                EventType = WalkerEventType.IntersectionFound,
                IntersectionPoint = nextPoint
            };
        }

        public DirectionDecision MakeDecision(WalkerEventResult eventResult)
        {
            var decision = new DirectionDecision();
            if (eventResult.EventType == WalkerEventType.OutlineClosed || eventResult.EventType == WalkerEventType.Completed)
            {
                decision.NextPosition = _startPosition;
                decision.Reason = "轮廓闭合";
            }
            else if (eventResult.EventType == WalkerEventType.DeadEnd)
            {
                decision.RequiresBacktrack = true;
                decision.Reason = "死胡同";
            }
            else
            {
                decision.NextPosition = eventResult.IntersectionPoint;
                decision.Reason = "最右转规则";
            }
            return decision;
        }

        public void UpdateState(DirectionDecision decision)
        {
            if (decision.RequiresBacktrack)
            { 
                _isCompleted = true; // Simplified: stop at dead ends.
                return;
            }

            var nextPosition = decision.NextPosition;

            // Update direction *before* changing position.
            var newDirection = (nextPosition - _currentPosition).GetNormal();
            if (!newDirection.IsEqualTo(new Vector3d(0, 0, 0), new Tolerance(_tolerance, _tolerance)))
            {
                _lastDirection = newDirection;
            }

            _currentPosition = nextPosition;
            _pathPoints.Add(_currentPosition);

            // Update the current curve based on the new position.
            var newCurve = _finder.GetCurveAtPoint(_currentPosition, _currentCurve, _tolerance);
            if (newCurve != null)
            {
                _currentCurve = newCurve;
            }

            // Check for completion.
            if (_pathPoints.Count > 1 && _currentPosition.IsEqualTo(_startPosition, new Tolerance(_tolerance, _tolerance)))
            {
                _isCompleted = true;
            }
        }

        public List<Point3d> GetResultPath()
        {
            // Ensure the path is closed
            if (_pathPoints.Count > 1 && !_pathPoints.First().IsEqualTo(_pathPoints.Last(), new Tolerance(_tolerance, _tolerance)))
            {
                _pathPoints.Add(_pathPoints.First());
            }
            return new List<Point3d>(_pathPoints);
        }

        private Point3d FindBestTurn(List<Point3d> candidates)
        {
            if (!candidates.Any()) return _currentPosition;
            if (candidates.Count == 1) return candidates[0];

            // For outer boundary tracing: always make the LEFTMOST turn (largest clockwise angle)
            // This keeps all solid shapes to our right as we trace the outer boundary
            
            Vector3d incomingDir = _lastDirection;
            Point3d bestCandidate = candidates[0];
            double largestAngle = -1.0;

            foreach (var candidate in candidates)
            {
                var candidateDir = (candidate - _currentPosition).GetNormal();
                
                // Skip backward moves
                if (candidateDir.IsEqualTo(-incomingDir, new Tolerance(_tolerance, _tolerance)))
                    continue;

                // Calculate the clockwise angle from incoming direction to candidate direction
                double angle = ComputeClockwiseAngle(incomingDir, candidateDir);
                
                // Choose the candidate with the LARGEST clockwise angle (leftmost turn)
                if (angle > largestAngle)
                {
                    largestAngle = angle;
                    bestCandidate = candidate;
                }
            }

            return bestCandidate;
        }

        /// <summary>
        /// 计算从 <paramref name="from"/> 指向 <paramref name="to"/> 的顺时针夹角（弧度，范围 0~2π）。
        /// 不依赖 AutoCAD 的 GetAngleTo 方法，完全使用基础向量运算。
        /// </summary>
        private static double ComputeClockwiseAngle(Vector3d from, Vector3d to)
        {
            // 只考虑 XY 分量
            var f = new Vector3d(from.X, from.Y, 0).GetNormal();
            var t = new Vector3d(to.X, to.Y, 0).GetNormal();

            // 点积用于角度余弦，叉积 Z 分量用于方向
            double dot = f.X * t.X + f.Y * t.Y;
            // 2D 叉积（Z 分量）: f.x * t.y - f.y * t.x
            double crossZ = f.X * t.Y - f.Y * t.X;

            // 顺时针角度，右手规则：取 -crossZ
            double angle = Math.Atan2(-crossZ, dot); // 范围 (-π, π]
            if (angle < 0) angle += 2 * Math.PI;     // 映射到 [0, 2π)
            return angle;
        }

        private List<Point3d> FindConnectedPoints(Point3d currentPoint)
        {
            if (_currentCurve == null) return new List<Point3d>();
            var pts = _finder.GetConnectedPoints(currentPoint, _currentCurve);
            // 过滤掉回头方向
            return pts.Where(p => !IsSameDirection(currentPoint, p)).ToList();
        }

        private bool IsSameDirection(Point3d from, Point3d to)
        {
            if (_pathPoints.Count < 2) return false;
            var newDirection = (to - from).GetNormal();
            var prevDirection = (_currentPosition - _pathPoints[_pathPoints.Count - 2]).GetNormal();
            // Check if the new direction is the exact opposite of the previous one
            return newDirection.IsEqualTo(-prevDirection, new Tolerance(_tolerance, _tolerance));
        }

        private List<LineSegment3d> ExtractAllLineSegments(List<Entity> entities)
        {
            var segments = new List<LineSegment3d>();
            foreach (var entity in entities)
            {
                if (entity is Curve curve)
                {
                    try
                    {
                        // Explode complex curves into simpler segments (lines and arcs)
                        var explodedObjects = new DBObjectCollection();
                        curve.Explode(explodedObjects);

                        foreach (DBObject obj in explodedObjects)
                        {
                            if (obj is Curve explodedCurve)
                            {
                                AddCurveSegments(explodedCurve, segments);
                            }
                            obj.Dispose();
                        }
                    }
                    catch
                    {
                        // Fallback for curves that cannot be exploded
                        AddCurveSegments(curve, segments);
                    }
                }
            }
            return segments;
        }

        private static void AddCurveSegments(Curve curve, List<LineSegment3d> segments)
        {
            if (curve is Line line)
            {
                segments.Add(new LineSegment3d(line.StartPoint, line.EndPoint));
            }
            else if (curve is Polyline polyline)
            {
                for (int i = 0; i < polyline.NumberOfVertices; i++)
                {
                    SegmentType segType = polyline.GetSegmentType(i);
                    if (segType == SegmentType.Line)
                    {
                        var segment = polyline.GetLineSegmentAt(i);
                        if (segment != null)
                            segments.Add(new LineSegment3d(segment.StartPoint, segment.EndPoint));
                    }
                    else if (segType == SegmentType.Arc)
                    {
                        var arcSegment = polyline.GetArcSegmentAt(i);
                        if (arcSegment != null)
                            TessellateArc(arcSegment.Center, arcSegment.Radius, arcSegment.StartAngle, arcSegment.EndAngle, polyline.GetBulgeAt(i) < 0, segments);
                    }
                }
            }
            else if (curve is Arc arc)
            {
                TessellateArc(arc.Center, arc.Radius, arc.StartAngle, arc.EndAngle, arc.Normal.Z < 0, segments);
            }
            else if (curve is Circle circle)
            {
                TessellateCircle(circle.Center, circle.Radius, segments);
            }
        }

        private static void TessellateArc(Point3d center, double radius, double startAngle, double endAngle, bool isClockwise, List<LineSegment3d> segments)
        {
            // Normalize angles
            if (isClockwise)
            {
                if (endAngle > startAngle)
                    startAngle += 2 * Math.PI;
            }
            else
            {
                if (endAngle < startAngle)
                    endAngle += 2 * Math.PI;
            }

            double totalAngle = Math.Abs(endAngle - startAngle);
            int numSegments = Math.Max(1, (int)Math.Ceiling(totalAngle / (Math.PI / 18))); // Approx. 10 deg segments

            Point3d lastPoint = Point3d.Origin; // Will be set in first iteration
            for (int i = 0; i <= numSegments; i++)
            {
                double angle = startAngle + (endAngle - startAngle) * i / numSegments;
                Point3d currentPoint = new Point3d(
                    center.X + radius * Math.Cos(angle),
                    center.Y + radius * Math.Sin(angle),
                    center.Z
                );

                if (i > 0)
                {
                    segments.Add(new LineSegment3d(lastPoint, currentPoint));
                }
                lastPoint = currentPoint;
            }
        }

        private static void TessellateCircle(Point3d center, double radius, List<LineSegment3d> segments)
        {
            TessellateArc(center, radius, 0, 2 * Math.PI, false, segments);
        }

        #region Intersection Split Helpers
        /// <summary>
        /// 根据所有线段间的几何交点，把线段在交点处分割成更小的段，
        /// 使得所有相交关系都体现在端点重合上。
        /// </summary>
        private static List<LineSegment3d> SplitSegmentsAtIntersections(List<LineSegment3d> segments, double tol)
        {
            // 为每条线段维护一个交点集合
            var segPoints = new Dictionary<LineSegment3d, HashSet<Point3d>>();
            foreach (var seg in segments)
            {
                segPoints[seg] = new HashSet<Point3d>(new PointComparer(tol))
                {
                    seg.StartPoint,
                    seg.EndPoint
                };
            }

            // 两两检测相交
            for (int i = 0; i < segments.Count; i++)
            {
                for (int j = i + 1; j < segments.Count; j++)
                {
                    if (TryIntersectLines(segments[i], segments[j], out Point3d ip, tol))
                    {
                        segPoints[segments[i]].Add(ip);
                        segPoints[segments[j]].Add(ip);
                    }
                }
            }

            // 生成新的分割后线段
            var result = new List<LineSegment3d>();
            foreach (var kvp in segPoints)
            {
                var pts = kvp.Value.ToList();
                // 按在原线段上的投影参数排序
                pts.Sort((a, b) =>
                {
                    double ta = (a - kvp.Key.StartPoint).Length;
                    double tb = (b - kvp.Key.StartPoint).Length;
                    return ta.CompareTo(tb);
                });
                for (int k = 0; k < pts.Count - 1; k++)
                {
                    if (!pts[k].IsEqualTo(pts[k + 1], new Tolerance(tol, tol)))
                    {
                        result.Add(new LineSegment3d(pts[k], pts[k + 1]));
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// 计算两条线段是否在 2D (XY) 平面内相交，返回交点。
        /// </summary>
        private static bool TryIntersectLines(LineSegment3d a, LineSegment3d b, out Point3d intersection, double tol)
        {
            intersection = Point3d.Origin;

            // 向量表示
            Vector2d p = new Vector2d(a.StartPoint.X, a.StartPoint.Y);
            Vector2d r = new Vector2d(a.EndPoint.X - a.StartPoint.X, a.EndPoint.Y - a.StartPoint.Y);
            Vector2d q = new Vector2d(b.StartPoint.X, b.StartPoint.Y);
            Vector2d s = new Vector2d(b.EndPoint.X - b.StartPoint.X, b.EndPoint.Y - b.StartPoint.Y);

            double rxs = r.X * s.Y - r.Y * s.X;
            double qpxr = (q.X - p.X) * r.Y - (q.Y - p.Y) * r.X;

            if (Math.Abs(rxs) < tol)
            {
                // 平行或共线，当前忽略（可扩展覆盖共线重叠）
                return false;
            }

            double t = ((q.X - p.X) * s.Y - (q.Y - p.Y) * s.X) / rxs;
            double u = qpxr / rxs;
            if (t >= -tol && t <= 1 + tol && u >= -tol && u <= 1 + tol)
            {
                // 交点
                intersection = new Point3d(a.StartPoint.X + t * r.X, a.StartPoint.Y + t * r.Y, a.StartPoint.Z);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 2D 点比较器，用于基于容差的 HashSet
        /// </summary>
        private class PointComparer : IEqualityComparer<Point3d>
        {
            private readonly double _tol;
            public PointComparer(double tol) => _tol = tol;
            public bool Equals(Point3d x, Point3d y) => x.IsEqualTo(y, new Tolerance(_tol, _tol));
            public int GetHashCode(Point3d obj) => obj.GetHashCode();
        }
        #endregion

        #region IntersectWith based splitting
        private static bool DoExtentsOverlap(Extents3d a, Extents3d b)
        {
            // Simple AABB overlap check on the XY plane
            return a.MaxPoint.X >= b.MinPoint.X &&
                   a.MinPoint.X <= b.MaxPoint.X &&
                   a.MaxPoint.Y >= b.MinPoint.Y &&
                   a.MinPoint.Y <= b.MaxPoint.Y;
        }

        /// <summary>
        /// 使用 AutoCAD IntersectWith API 在曲线层面求交点，并在交点处分割，生成线段集合。
        /// 仅考虑 XY 平面投影（假设图纸主要在该平面）。
        /// </summary>
        private static List<LineSegment3d> SplitCurvesWithIntersectWith(List<Curve> curves, double tol)
        {
            var curvePoints = new Dictionary<Curve, HashSet<Point3d>>();
            foreach (var c in curves)
            {
                curvePoints[c] = new HashSet<Point3d>(new PointComparer(tol))
                {
                    c.StartPoint, c.EndPoint
                };
            }

            // 统一参考平面：世界 XY
            var refPlane = new Plane(Point3d.Origin, Vector3d.ZAxis);

            for (int i = 0; i < curves.Count; i++)
            {
                var a = curves[i];
                var boxA = a.GeometricExtents;
                for (int j = i + 1; j < curves.Count; j++)
                {
                    var b = curves[j];
                    if (!DoExtentsOverlap(boxA, b.GeometricExtents)) continue; // 粗筛

                    using var pts = new Point3dCollection();
                    // 先投影到统一平面
                    var aProj = a.GetProjectedCurve(refPlane, refPlane.Normal);
                    var bProj = b.GetProjectedCurve(refPlane, refPlane.Normal);

                    aProj.IntersectWith(bProj, Intersect.OnBothOperands, pts, IntPtr.Zero, IntPtr.Zero);
                    foreach (Point3d ip in pts)
                    {
                        var p2d = new Point3d(ip.X, ip.Y, 0); // 保持 XY
                        curvePoints[a].Add(p2d);
                        curvePoints[b].Add(p2d);
                    }
                    aProj.Dispose();
                    bProj.Dispose();
                }
            }

            // 生成线段
            var result = new List<LineSegment3d>();
            foreach (var kvp in curvePoints)
            {
                if (kvp.Key is Line ln)
                {
                    AppendLineSegments(ln, kvp.Value.ToList(), result, tol);
                }
                else
                {
                    // 其他曲线类型：爆破后走现有 AddCurveSegments
                    var exploded = new DBObjectCollection();
                    kvp.Key.Explode(exploded);
                    foreach (DBObject obj in exploded)
                    {
                        if (obj is Curve c)
                        {
                            var smallPts = new HashSet<Point3d>(new PointComparer(tol))
                            {
                                c.StartPoint, c.EndPoint
                            };
                            // 若有与原曲线相同交点，也加入
                            foreach (var p in kvp.Value) if (c.GetClosestPointTo(p, false).IsEqualTo(p, new Tolerance(tol, tol))) smallPts.Add(p);
                            if (c is Line lsmall)
                                AppendLineSegments(lsmall, smallPts.ToList(), result, tol);
                            else
                                AddCurveSegments(c, result);
                        }
                        obj.Dispose();
                    }
                }
            }
            return result;
        }

        private static void AppendLineSegments(Line line, List<Point3d> pts, List<LineSegment3d> dest, double tol)
        {
            // 把点按线内参数排序
            pts.Sort((a, b) =>
            {
                double ta = (a - line.StartPoint).Length;
                double tb = (b - line.StartPoint).Length;
                return ta.CompareTo(tb);
            });
            for (int i = 0; i < pts.Count - 1; i++)
            {
                if (!pts[i].IsEqualTo(pts[i + 1], new Tolerance(tol, tol)))
                    dest.Add(new LineSegment3d(pts[i], pts[i + 1]));
            }
        }
        #endregion

        private Point3d FindStartPoint(IEnumerable<Curve> curves)
        {
            var pts = curves.SelectMany(c => new[] { c.StartPoint, c.EndPoint });
            return pts.OrderBy(p => p.Y).ThenBy(p => p.X).First();
        }
    }
}
