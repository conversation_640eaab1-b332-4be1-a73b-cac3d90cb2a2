using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using System;
using System.Collections.Generic;
using System.Linq;

namespace IECAD.Services.OutlineWalker
{
    public class PointComparer : IEqualityComparer<Point3d>
    {
        private readonly double _tolerance;

        public PointComparer(double tolerance)
        {
            _tolerance = tolerance;
        }

        public bool Equals(Point3d p1, Point3d p2)
        {
            return p1.IsEqualTo(p2, new Tolerance(_tolerance, _tolerance));
        }

        public int GetHashCode(Point3d p)
        {
            // A simple hash code implementation that is tolerant to small differences.
            // This is not perfect but should be sufficient for many cases.
            int hashX = Math.Round(p.X / _tolerance).GetHashCode();
            int hashY = Math.Round(p.Y / _tolerance).GetHashCode();
            int hashZ = Math.Round(p.Z / _tolerance).GetHashCode();
            return hashX ^ hashY ^ hashZ;
        }
    }

    /// <summary>
    /// 按需计算交点并做缓存，避免一次性两两求交导致的性能问题。
    /// 每条曲线第一次被访问时，仅与其包围盒相交的曲线进行 IntersectWith，
    /// 结果写入缓存，后续重复利用。
    /// </summary>
    internal class StepwiseIntersectionFinder
    {
        private readonly List<Curve> _curves;
        private readonly double _tol;

        // 缓存：Curve -> 该曲线所有交点列表
        private readonly Dictionary<Curve, List<Point3d>> _cache = new();

        public StepwiseIntersectionFinder(List<Curve> curves, double tolerance)
        {
            _curves = curves;
            _tol = tolerance;
        }

        /// <summary>
        /// 获取 <paramref name="curve"/> 在 <paramref name="pointOnCurve"/> 处可到达的所有交点（不含自身点）。
        /// </summary>
        public List<Point3d> GetConnectedPoints(Point3d point, Curve currentCurve)
        {
            var connected = new HashSet<Point3d>(new PointComparer(_tol));
            var containingCurves = GetCurvesContainingPoint(point);

            foreach (var c in containingCurves)
            {
                EnsureIntersectionsCached(c);
                var eventPoints = new List<Point3d>();
                eventPoints.Add(c.StartPoint);
                eventPoints.Add(c.EndPoint);
                if (_cache.TryGetValue(c, out var ips))
                {
                    eventPoints.AddRange(ips);
                }

                var distinctPoints = eventPoints.Distinct(new PointComparer(_tol)).ToList();
                if (distinctPoints.Count < 2) continue;

                distinctPoints.Sort((p1, p2) => c.GetDistAtPoint(c.GetClosestPointTo(p1, false)).CompareTo(c.GetDistAtPoint(c.GetClosestPointTo(p2, false))));

                int pointIndex = -1;
                for (int i = 0; i < distinctPoints.Count; i++)
                {
                    if (distinctPoints[i].IsEqualTo(point, new Tolerance(_tol, _tol)))
                    {
                        pointIndex = i;
                        break;
                    }
                }

                if (pointIndex != -1)
                {
                    if (pointIndex > 0)
                        connected.Add(distinctPoints[pointIndex - 1]);
                    if (pointIndex < distinctPoints.Count - 1)
                        connected.Add(distinctPoints[pointIndex + 1]);

                    if (c.Closed && distinctPoints.Count > 1)
                    {
                        if (pointIndex == 0)
                            connected.Add(distinctPoints.Last());
                        if (pointIndex == distinctPoints.Count - 1)
                            connected.Add(distinctPoints.First());
                    }
                }
            }
            
            var result = connected.ToList();
            result.RemoveAll(p => p.IsEqualTo(point, new Tolerance(_tol, _tol)));

            return result;
        }

        /// <summary>
        /// 在给定点附近查找属于哪条曲线。优先返回当前曲线自身。
        /// </summary>
        public Curve GetCurveAtPoint(Point3d point, Curve current, double proximityTol)
        {
            if (current != null && IsPointOnCurve(point, current, proximityTol))
                return current;

            foreach (var c in _curves)
            {
                if (IsPointOnCurve(point, c, proximityTol))
                    return c;
            }
            return current; // fallback
        }

        #region Private helpers
        private void EnsureIntersectionsCached(Curve curve)
        {
            if (_cache.ContainsKey(curve)) return;

            var list = new List<Point3d>();
            Extents3d ext = curve.GeometricExtents;
            // 扩展一个容差，避免边缘漏检
            ext = new Extents3d(new Point3d(ext.MinPoint.X - _tol, ext.MinPoint.Y - _tol, ext.MinPoint.Z),
                                 new Point3d(ext.MaxPoint.X + _tol, ext.MaxPoint.Y + _tol, ext.MaxPoint.Z));

            foreach (var other in _curves)
            {
                if (other == curve) continue;
                if (!BoundingBoxesIntersect(ext, other.GeometricExtents)) continue;

                using var pts = new Point3dCollection();
                try
                {
                    curve.IntersectWith(other, Intersect.OnBothOperands, pts, IntPtr.Zero, IntPtr.Zero);
                    foreach (Point3d ip in pts)
                    {
                        bool exists = false;
                        foreach (var existing in list)
                        {
                            if (existing.IsEqualTo(ip, new Tolerance(_tol, _tol))) { exists = true; break; }
                        }
                        if (!exists) list.Add(ip);
                    }
                }
                catch { /* 某些曲线类型可能不支持 IntersectWith，忽略 */ }
            }
            _cache[curve] = list;
        }

        private List<Curve> GetCurvesContainingPoint(Point3d point)
        {
            var containingCurves = new List<Curve>();
            foreach (var c in _curves)
            {
                if (IsPointOnCurve(point, c, _tol))
                {
                    containingCurves.Add(c);
                }
            }
            return containingCurves;
        }

        private static bool BoundingBoxesIntersect(Extents3d a, Extents3d b)
        {
            return !(a.MaxPoint.X < b.MinPoint.X || a.MinPoint.X > b.MaxPoint.X ||
                     a.MaxPoint.Y < b.MinPoint.Y || a.MinPoint.Y > b.MaxPoint.Y);
        }

        private static bool IsPointOnCurve(Point3d p, Curve c, double tol)
        {
            try
            {
                var param = c.GetParameterAtPoint(p);
                var onCurve = c.GetPointAtParameter(param);
                return p.IsEqualTo(onCurve, new Tolerance(tol, tol));
            }
            catch
            {
                var dist = c.GetClosestPointTo(p, false).DistanceTo(p);
                return dist < tol;
            }
        }
        #endregion
    }
}
