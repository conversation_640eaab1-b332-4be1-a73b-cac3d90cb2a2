using System;
using System.Threading;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using IECAD.Services;
using IECAD.Tests.TestHelpers;

namespace IECAD.Tests.Services
{
    [TestClass]
    public class PerformanceMonitoringServiceTests : TestBase
    {
        private PerformanceMonitoringService _service;

        [TestInitialize]
        public override void TestInitialize()
        {
            base.TestInitialize();
            _service = new PerformanceMonitoringService(MockLoggingService.Object);
        }

        [TestCleanup]
        public override void TestCleanup()
        {
            _service?.Clear();
            base.TestCleanup();
        }

        [TestMethod]
        public void StartMeasurement_ShouldReturnDisposableToken()
        {
            // Act
            var token = _service.StartMeasurement("TestOperation");

            // Assert
            Assert.IsNotNull(token);
            Assert.IsInstanceOfType(token, typeof(IDisposable));
        }

        [TestMethod]
        public void StartMeasurement_WithNullOperationName_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.ThrowsException<ArgumentNullException>(() => _service.StartMeasurement(null));
        }

        [TestMethod]
        public void StartMeasurement_WithEmptyOperationName_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.ThrowsException<ArgumentNullException>(() => _service.StartMeasurement(string.Empty));
        }

        [TestMethod]
        public void RecordMeasurement_ShouldStorePerformanceData()
        {
            // Arrange
            var operationName = "TestOperation";
            var duration = TimeSpan.FromMilliseconds(100);

            // Act
            _service.RecordMeasurement(operationName, duration);

            // Assert
            var statistics = _service.GetStatistics(operationName);
            Assert.IsNotNull(statistics);
            Assert.AreEqual(operationName, statistics.OperationName);
            Assert.AreEqual(1, statistics.CallCount);
            Assert.AreEqual(duration, statistics.TotalDuration);
            Assert.AreEqual(duration, statistics.AverageDuration);
            Assert.AreEqual(duration, statistics.MinDuration);
            Assert.AreEqual(duration, statistics.MaxDuration);
        }

        [TestMethod]
        public void RecordMeasurement_MultipleCalls_ShouldCalculateCorrectStatistics()
        {
            // Arrange
            var operationName = "TestOperation";
            var duration1 = TimeSpan.FromMilliseconds(100);
            var duration2 = TimeSpan.FromMilliseconds(200);
            var duration3 = TimeSpan.FromMilliseconds(150);

            // Act
            _service.RecordMeasurement(operationName, duration1);
            _service.RecordMeasurement(operationName, duration2);
            _service.RecordMeasurement(operationName, duration3);

            // Assert
            var statistics = _service.GetStatistics(operationName);
            Assert.IsNotNull(statistics);
            Assert.AreEqual(3, statistics.CallCount);
            Assert.AreEqual(TimeSpan.FromMilliseconds(450), statistics.TotalDuration);
            Assert.AreEqual(TimeSpan.FromMilliseconds(150), statistics.AverageDuration);
            Assert.AreEqual(duration1, statistics.MinDuration);
            Assert.AreEqual(duration2, statistics.MaxDuration);
        }

        [TestMethod]
        public void GetStatistics_NonExistentOperation_ShouldReturnNull()
        {
            // Act
            var statistics = _service.GetStatistics("NonExistentOperation");

            // Assert
            Assert.IsNull(statistics);
        }

        [TestMethod]
        public void GetAllStatistics_ShouldReturnAllOperations()
        {
            // Arrange
            _service.RecordMeasurement("Operation1", TimeSpan.FromMilliseconds(100));
            _service.RecordMeasurement("Operation2", TimeSpan.FromMilliseconds(200));

            // Act
            var allStatistics = _service.GetAllStatistics();

            // Assert
            Assert.AreEqual(2, allStatistics.Count);
            Assert.IsTrue(allStatistics.ContainsKey("Operation1"));
            Assert.IsTrue(allStatistics.ContainsKey("Operation2"));
        }

        [TestMethod]
        public void Clear_ShouldRemoveAllStatistics()
        {
            // Arrange
            _service.RecordMeasurement("Operation1", TimeSpan.FromMilliseconds(100));
            _service.RecordMeasurement("Operation2", TimeSpan.FromMilliseconds(200));

            // Act
            _service.Clear();

            // Assert
            var allStatistics = _service.GetAllStatistics();
            Assert.AreEqual(0, allStatistics.Count);
        }

        [TestMethod]
        public void Clear_WithOperationName_ShouldRemoveSpecificOperation()
        {
            // Arrange
            _service.RecordMeasurement("Operation1", TimeSpan.FromMilliseconds(100));
            _service.RecordMeasurement("Operation2", TimeSpan.FromMilliseconds(200));

            // Act
            _service.Clear("Operation1");

            // Assert
            var allStatistics = _service.GetAllStatistics();
            Assert.AreEqual(1, allStatistics.Count);
            Assert.IsTrue(allStatistics.ContainsKey("Operation2"));
            Assert.IsFalse(allStatistics.ContainsKey("Operation1"));
        }

        [TestMethod]
        public void StartMeasurement_DisposingToken_ShouldRecordMeasurement()
        {
            // Arrange
            var operationName = "TestOperation";

            // Act
            using (var token = _service.StartMeasurement(operationName))
            {
                Thread.Sleep(50); // Simulate some work
            }

            // Assert
            var statistics = _service.GetStatistics(operationName);
            Assert.IsNotNull(statistics);
            Assert.AreEqual(1, statistics.CallCount);
            Assert.IsTrue(statistics.TotalDuration.TotalMilliseconds >= 40); // Allow some tolerance
        }

        [TestMethod]
        public void RecordMeasurement_WithNullOperationName_ShouldNotThrow()
        {
            // Act & Assert (should not throw)
            _service.RecordMeasurement(null, TimeSpan.FromMilliseconds(100));
            _service.RecordMeasurement(string.Empty, TimeSpan.FromMilliseconds(100));
        }
    }
}
