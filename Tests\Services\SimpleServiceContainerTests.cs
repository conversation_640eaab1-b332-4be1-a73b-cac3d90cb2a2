using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using IECAD.Services;
using IECAD.Tests.TestHelpers;

namespace IECAD.Tests.Services
{
    [TestClass]
    public class SimpleServiceContainerTests : TestBase
    {
        private SimpleServiceContainer _container;

        [TestInitialize]
        public override void TestInitialize()
        {
            base.TestInitialize();
            _container = new SimpleServiceContainer();
        }

        [TestCleanup]
        public override void TestCleanup()
        {
            _container?.Dispose();
            base.TestCleanup();
        }

        [TestMethod]
        public void RegisterSingleton_WithImplementation_ShouldRegisterService()
        {
            // Arrange & Act
            _container.RegisterSingleton<ILoggingService, FileLoggingService>();

            // Assert
            Assert.IsTrue(_container.IsRegistered<ILoggingService>());
        }

        [TestMethod]
        public void RegisterSingleton_WithInstance_ShouldRegisterService()
        {
            // Arrange
            var instance = MockLoggingService.Object;

            // Act
            _container.RegisterSingleton<ILoggingService>(instance);

            // Assert
            Assert.IsTrue(_container.IsRegistered<ILoggingService>());
        }

        [TestMethod]
        public void RegisterTransient_ShouldRegisterService()
        {
            // Arrange & Act
            _container.RegisterTransient<ILoggingService, FileLoggingService>();

            // Assert
            Assert.IsTrue(_container.IsRegistered<ILoggingService>());
        }

        [TestMethod]
        public void Resolve_RegisteredSingleton_ShouldReturnSameInstance()
        {
            // Arrange
            _container.RegisterSingleton<ILoggingService, FileLoggingService>();

            // Act
            var instance1 = _container.Resolve<ILoggingService>();
            var instance2 = _container.Resolve<ILoggingService>();

            // Assert
            Assert.IsNotNull(instance1);
            Assert.IsNotNull(instance2);
            Assert.AreSame(instance1, instance2);
        }

        [TestMethod]
        public void Resolve_RegisteredTransient_ShouldReturnDifferentInstances()
        {
            // Arrange
            _container.RegisterTransient<ILoggingService, FileLoggingService>();

            // Act
            var instance1 = _container.Resolve<ILoggingService>();
            var instance2 = _container.Resolve<ILoggingService>();

            // Assert
            Assert.IsNotNull(instance1);
            Assert.IsNotNull(instance2);
            Assert.AreNotSame(instance1, instance2);
        }

        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void Resolve_UnregisteredService_ShouldThrowException()
        {
            // Act
            _container.Resolve<ILoggingService>();
        }

        [TestMethod]
        public void TryResolve_RegisteredService_ShouldReturnTrueAndInstance()
        {
            // Arrange
            _container.RegisterSingleton<ILoggingService, FileLoggingService>();

            // Act
            bool result = _container.TryResolve<ILoggingService>(out ILoggingService service);

            // Assert
            Assert.IsTrue(result);
            Assert.IsNotNull(service);
        }

        [TestMethod]
        public void TryResolve_UnregisteredService_ShouldReturnFalseAndNull()
        {
            // Act
            bool result = _container.TryResolve<ILoggingService>(out ILoggingService service);

            // Assert
            Assert.IsFalse(result);
            Assert.IsNull(service);
        }

        [TestMethod]
        public void IsRegistered_RegisteredService_ShouldReturnTrue()
        {
            // Arrange
            _container.RegisterSingleton<ILoggingService, FileLoggingService>();

            // Act & Assert
            Assert.IsTrue(_container.IsRegistered<ILoggingService>());
        }

        [TestMethod]
        public void IsRegistered_UnregisteredService_ShouldReturnFalse()
        {
            // Act & Assert
            Assert.IsFalse(_container.IsRegistered<ILoggingService>());
        }

        [TestMethod]
        public void Dispose_ShouldDisposeRegisteredDisposableServices()
        {
            // Arrange
            var disposableService = new TestDisposableService();
            _container.RegisterSingleton<ITestDisposableService>(disposableService);
            _container.Resolve<ITestDisposableService>(); // Ensure it's created

            // Act
            _container.Dispose();

            // Assert
            Assert.IsTrue(disposableService.IsDisposed);
        }

        // Test interfaces and classes
        public interface ITestDisposableService : IDisposable
        {
            bool IsDisposed { get; }
        }

        public class TestDisposableService : ITestDisposableService
        {
            public bool IsDisposed { get; private set; }

            public void Dispose()
            {
                IsDisposed = true;
            }
        }
    }
}
