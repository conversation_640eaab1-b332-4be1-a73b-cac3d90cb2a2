using System;
using System.Diagnostics;
using System.Runtime;
using System.Runtime.CompilerServices;
using IECAD.Services;

namespace IECAD.Helpers
{
    /// <summary>
    /// Memory management utilities for optimizing application performance
    /// </summary>
    public static class MemoryManager
    {
        private static readonly ILoggingService _loggingService;

        static MemoryManager()
        {
            if (ServiceConfiguration.TryResolve<ILoggingService>(out var logging))
            {
                _loggingService = logging;
            }
        }

        /// <summary>
        /// Force garbage collection and compact the heap
        /// </summary>
        /// <param name="generation">GC generation to collect (default: all generations)</param>
        /// <param name="compactHeap">Whether to compact the heap</param>
        public static void ForceGarbageCollection(int generation = -1, bool compactHeap = true)
        {
            try
            {
                var beforeMemory = GC.GetTotalMemory(false);
                
                if (generation >= 0)
                {
                    GC.Collect(generation, GCCollectionMode.Forced);
                }
                else
                {
                    GC.Collect();
                }
                
                GC.WaitForPendingFinalizers();
                
                if (compactHeap)
                {
                    GCSettings.LargeObjectHeapCompactionMode = GCLargeObjectHeapCompactionMode.CompactOnce;
                    GC.Collect();
                }

                var afterMemory = GC.GetTotalMemory(false);
                var freedMemory = beforeMemory - afterMemory;
                
                _loggingService?.LogDebug($"Garbage collection completed. Freed {freedMemory / 1024 / 1024:F2} MB", "MemoryManager");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error during garbage collection: {ex.Message}", "MemoryManager");
            }
        }

        /// <summary>
        /// Get current memory usage information
        /// </summary>
        /// <returns>Memory usage information</returns>
        public static MemoryUsageInfo GetMemoryUsage()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var gcMemory = GC.GetTotalMemory(false);
                
                return new MemoryUsageInfo
                {
                    WorkingSet = process.WorkingSet64,
                    PrivateMemorySize = process.PrivateMemorySize64,
                    VirtualMemorySize = process.VirtualMemorySize64,
                    GCMemory = gcMemory,
                    Gen0Collections = GC.CollectionCount(0),
                    Gen1Collections = GC.CollectionCount(1),
                    Gen2Collections = GC.CollectionCount(2)
                };
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error getting memory usage: {ex.Message}", "MemoryManager");
                return new MemoryUsageInfo();
            }
        }

        /// <summary>
        /// Monitor memory usage and log if it exceeds threshold
        /// </summary>
        /// <param name="thresholdMB">Memory threshold in MB</param>
        /// <param name="operationName">Name of the operation being monitored</param>
        public static void MonitorMemoryUsage(long thresholdMB = 500, string operationName = "Unknown")
        {
            try
            {
                var memoryInfo = GetMemoryUsage();
                var workingSetMB = memoryInfo.WorkingSet / 1024 / 1024;
                
                if (workingSetMB > thresholdMB)
                {
                    _loggingService?.LogWarning($"High memory usage detected during {operationName}: {workingSetMB} MB (threshold: {thresholdMB} MB)", "MemoryManager");
                    
                    // Log detailed memory information
                    LogDetailedMemoryInfo(memoryInfo);
                    
                    // Suggest garbage collection if memory is very high
                    if (workingSetMB > thresholdMB * 2)
                    {
                        _loggingService?.LogInfo("Triggering garbage collection due to high memory usage", "MemoryManager");
                        ForceGarbageCollection();
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error monitoring memory usage: {ex.Message}", "MemoryManager");
            }
        }

        /// <summary>
        /// Log detailed memory information
        /// </summary>
        /// <param name="memoryInfo">Memory usage information</param>
        public static void LogDetailedMemoryInfo(MemoryUsageInfo memoryInfo = null)
        {
            try
            {
                memoryInfo ??= GetMemoryUsage();
                
                _loggingService?.LogInfo($"Memory Usage Details:", "MemoryManager");
                _loggingService?.LogInfo($"  Working Set: {memoryInfo.WorkingSet / 1024 / 1024:F2} MB", "MemoryManager");
                _loggingService?.LogInfo($"  Private Memory: {memoryInfo.PrivateMemorySize / 1024 / 1024:F2} MB", "MemoryManager");
                _loggingService?.LogInfo($"  Virtual Memory: {memoryInfo.VirtualMemorySize / 1024 / 1024:F2} MB", "MemoryManager");
                _loggingService?.LogInfo($"  GC Memory: {memoryInfo.GCMemory / 1024 / 1024:F2} MB", "MemoryManager");
                _loggingService?.LogInfo($"  GC Collections - Gen0: {memoryInfo.Gen0Collections}, Gen1: {memoryInfo.Gen1Collections}, Gen2: {memoryInfo.Gen2Collections}", "MemoryManager");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error logging memory info: {ex.Message}", "MemoryManager");
            }
        }

        /// <summary>
        /// Create a memory monitoring scope that tracks memory usage for a specific operation
        /// </summary>
        /// <param name="operationName">Name of the operation</param>
        /// <param name="thresholdMB">Memory threshold in MB</param>
        /// <returns>Disposable memory monitoring scope</returns>
        public static IDisposable CreateMemoryMonitoringScope(string operationName, long thresholdMB = 500)
        {
            return new MemoryMonitoringScope(operationName, thresholdMB);
        }

        /// <summary>
        /// Optimize memory settings for the application
        /// </summary>
        public static void OptimizeMemorySettings()
        {
            try
            {
                // Configure GC settings for better performance
                GCSettings.LatencyMode = GCLatencyMode.Interactive;
                
                // Enable server GC if available and beneficial
                if (Environment.ProcessorCount > 1 && !GCSettings.IsServerGC)
                {
                    _loggingService?.LogInfo("Consider enabling server GC for better performance on multi-core systems", "MemoryManager");
                }
                
                _loggingService?.LogInfo("Memory settings optimized", "MemoryManager");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error optimizing memory settings: {ex.Message}", "MemoryManager");
            }
        }

        /// <summary>
        /// Weak reference helper for preventing memory leaks
        /// </summary>
        /// <typeparam name="T">Type of object to hold weak reference to</typeparam>
        /// <param name="target">Target object</param>
        /// <returns>Weak reference to the target</returns>
        public static WeakReference<T> CreateWeakReference<T>(T target) where T : class
        {
            return new WeakReference<T>(target);
        }

        /// <summary>
        /// Try to get target from weak reference
        /// </summary>
        /// <typeparam name="T">Type of target object</typeparam>
        /// <param name="weakRef">Weak reference</param>
        /// <param name="target">Target object if still alive</param>
        /// <returns>True if target is still alive</returns>
        public static bool TryGetTarget<T>(WeakReference<T> weakRef, out T target) where T : class
        {
            target = null;
            return weakRef?.TryGetTarget(out target) == true;
        }
    }

    /// <summary>
    /// Memory usage information
    /// </summary>
    public class MemoryUsageInfo
    {
        public long WorkingSet { get; set; }
        public long PrivateMemorySize { get; set; }
        public long VirtualMemorySize { get; set; }
        public long GCMemory { get; set; }
        public int Gen0Collections { get; set; }
        public int Gen1Collections { get; set; }
        public int Gen2Collections { get; set; }
    }

    /// <summary>
    /// Memory monitoring scope for tracking memory usage during operations
    /// </summary>
    internal class MemoryMonitoringScope : IDisposable
    {
        private readonly string _operationName;
        private readonly long _thresholdMB;
        private readonly MemoryUsageInfo _startMemory;
        private bool _disposed = false;

        public MemoryMonitoringScope(string operationName, long thresholdMB)
        {
            _operationName = operationName;
            _thresholdMB = thresholdMB;
            _startMemory = MemoryManager.GetMemoryUsage();
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                var endMemory = MemoryManager.GetMemoryUsage();
                var memoryDelta = endMemory.WorkingSet - _startMemory.WorkingSet;
                var memoryDeltaMB = memoryDelta / 1024 / 1024;

                if (Math.Abs(memoryDeltaMB) > 10) // Log if memory changed by more than 10MB
                {
                    var direction = memoryDelta > 0 ? "increased" : "decreased";
                    ServiceConfiguration.TryResolve<ILoggingService>(out var logging);
                    logging?.LogDebug($"Memory {direction} by {Math.Abs(memoryDeltaMB)} MB during {_operationName}", "MemoryMonitoringScope");
                }

                MemoryManager.MonitorMemoryUsage(_thresholdMB, _operationName);
                _disposed = true;
            }
        }
    }
}
