using System;
using System.Collections.Generic;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using IECAD.Services;
using IECAD.Services.OutlineWalker;

namespace IECAD.Tests.Services
{
    /// <summary>
    /// 几何算法服务单元测试
    /// </summary>
    [TestClass]
    public class GeometryAlgorithmServiceTests
    {
        private GeometryAlgorithmService _geometryService;

        [TestInitialize]
        public void Setup()
        {
            _geometryService = new GeometryAlgorithmService();
        }

        [TestCleanup]
        public void Cleanup()
        {
            _geometryService?.Dispose();
        }

        /// <summary>
        /// 测试凸包计算 - 基本功能
        /// </summary>
        [TestMethod]
        public void ExtractConvexHull_WithValidEntities_ShouldReturnConvexHull()
        {
            // Arrange
            var entities = CreateTestEntities();

            // Act
            var result = _geometryService.ExtractConvexHull(entities);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Count >= 3, "凸包应至少包含3个点");
        }

        /// <summary>
        /// 测试空实体集合处理
        /// </summary>
        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public void ExtractConvexHull_WithEmptyEntities_ShouldThrowException()
        {
            // Arrange
            var entities = new List<Entity>();

            // Act
            _geometryService.ExtractConvexHull(entities);

            // Assert - 期望抛出异常
        }

        /// <summary>
        /// 测试null实体集合处理
        /// </summary>
        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public void ExtractConvexHull_WithNullEntities_ShouldThrowException()
        {
            // Act
            _geometryService.ExtractConvexHull(null);

            // Assert - 期望抛出异常
        }

        /// <summary>
        /// 测试边界提取功能
        /// </summary>
        [TestMethod]
        public void ExtractBoundary_WithValidEntities_ShouldReturnBoundary()
        {
            // Arrange
            var entities = CreateTestEntities();
            double bufferDistance = 0.1;

            // Act
            var result = _geometryService.ExtractBoundary(entities, bufferDistance);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Count >= 3, "边界应至少包含3个点");
        }

        /// <summary>
        /// 测试Alpha Shape功能
        /// </summary>
        [TestMethod]
        public void ExtractAlphaShape_WithValidEntities_ShouldReturnAlphaShape()
        {
            // Arrange
            var entities = CreateTestEntities();
            double alpha = 1.0;

            // Act
            var result = _geometryService.ExtractAlphaShape(entities, alpha);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Count >= 3, "Alpha Shape应至少包含3个点");
        }

        /// <summary>
        /// 测试轮廓简化功能
        /// </summary>
        [TestMethod]
        public void SimplifyOutline_WithValidPoints_ShouldReturnSimplifiedPoints()
        {
            // Arrange
            var points = CreateTestPoints();
            double tolerance = 0.1;

            // Act
            var result = _geometryService.SimplifyOutline(points, tolerance);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Count <= points.Count, "简化后的点数应不超过原始点数");
        }

        /// <summary>
        /// 测试性能限制
        /// </summary>
        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void ExtractConvexHull_WithTooManyEntities_ShouldThrowException()
        {
            // Arrange
            var geometryService = new GeometryAlgorithmService(1e-6, 5, 100); // 设置较小的限制
            var entities = CreateLargeEntitySet(10); // 创建超过限制的实体数量

            try
            {
                // Act
                geometryService.ExtractConvexHull(entities);

                // Assert - 期望抛出异常
            }
            finally
            {
                geometryService.Dispose();
            }
        }

        /// <summary>
        /// 测试资源释放
        /// </summary>
        [TestMethod]
        [ExpectedException(typeof(ObjectDisposedException))]
        public void ExtractConvexHull_AfterDispose_ShouldThrowException()
        {
            // Arrange
            var entities = CreateTestEntities();
            _geometryService.Dispose();

            // Act
            _geometryService.ExtractConvexHull(entities);

            // Assert - 期望抛出异常
        }

        /// <summary>
        /// 创建测试实体集合
        /// </summary>
        /// <returns>测试实体集合</returns>
        private List<Entity> CreateTestEntities()
        {
            var entities = new List<Entity>();

            // 创建一个矩形的四条边
            entities.Add(new Line(new Point3d(0, 0, 0), new Point3d(10, 0, 0)));
            entities.Add(new Line(new Point3d(10, 0, 0), new Point3d(10, 10, 0)));
            entities.Add(new Line(new Point3d(10, 10, 0), new Point3d(0, 10, 0)));
            entities.Add(new Line(new Point3d(0, 10, 0), new Point3d(0, 0, 0)));

            // 添加一个圆
            entities.Add(new Circle(new Point3d(5, 5, 0), Vector3d.ZAxis, 2));

            return entities;
        }

        /// <summary>
        /// 创建测试点集合
        /// </summary>
        /// <returns>测试点集合</returns>
        private List<Point3d> CreateTestPoints()
        {
            return new List<Point3d>
            {
                new Point3d(0, 0, 0),
                new Point3d(1, 0, 0),
                new Point3d(2, 0, 0),
                new Point3d(3, 0, 0),
                new Point3d(4, 0, 0),
                new Point3d(5, 0, 0),
                new Point3d(5, 1, 0),
                new Point3d(5, 2, 0),
                new Point3d(5, 3, 0),
                new Point3d(5, 4, 0),
                new Point3d(5, 5, 0),
                new Point3d(4, 5, 0),
                new Point3d(3, 5, 0),
                new Point3d(2, 5, 0),
                new Point3d(1, 5, 0),
                new Point3d(0, 5, 0),
                new Point3d(0, 4, 0),
                new Point3d(0, 3, 0),
                new Point3d(0, 2, 0),
                new Point3d(0, 1, 0)
            };
        }

        /// <summary>
        /// 创建大量实体集合（用于性能测试）
        /// </summary>
        /// <param name="count">实体数量</param>
        /// <returns>实体集合</returns>
        private List<Entity> CreateLargeEntitySet(int count)
        {
            var entities = new List<Entity>();
            var random = new Random();

            for (int i = 0; i < count; i++)
            {
                var x = random.NextDouble() * 100;
                var y = random.NextDouble() * 100;
                var radius = random.NextDouble() * 5 + 1;

                entities.Add(new Circle(new Point3d(x, y, 0), Vector3d.ZAxis, radius));
            }

            return entities;
        }
    }

    /// <summary>
    /// 几何算法步行者单元测试
    /// </summary>
    [TestClass]
    public class GeometryAlgorithmWalkerTests
    {
        /// <summary>
        /// 测试几何算法步行者初始化
        /// </summary>
        [TestMethod]
        public void Initialize_WithValidEntities_ShouldCompleteSuccessfully()
        {
            // Arrange
            var walker = new GeometryAlgorithmWalker(GeometryAlgorithmWalker.GeometryAlgorithmType.ConvexHull);
            var entities = CreateTestEntities();

            // Act
            walker.Initialize(entities, 1.0);

            // Assert
            Assert.IsTrue(walker.IsCompleted, "几何算法步行者应该立即完成");
            Assert.IsNotNull(walker.GetResultPath(), "应该返回有效的结果路径");
        }

        /// <summary>
        /// 测试不同算法类型
        /// </summary>
        [TestMethod]
        public void Initialize_WithDifferentAlgorithms_ShouldReturnDifferentResults()
        {
            // Arrange
            var entities = CreateTestEntities();
            var convexHullWalker = new GeometryAlgorithmWalker(GeometryAlgorithmWalker.GeometryAlgorithmType.ConvexHull);
            var boundaryWalker = new GeometryAlgorithmWalker(GeometryAlgorithmWalker.GeometryAlgorithmType.Boundary);

            // Act
            convexHullWalker.Initialize(entities, 1.0);
            boundaryWalker.Initialize(entities, 1.0);

            var convexHullResult = convexHullWalker.GetResultPath();
            var boundaryResult = boundaryWalker.GetResultPath();

            // Assert
            Assert.IsNotNull(convexHullResult);
            Assert.IsNotNull(boundaryResult);
            Assert.IsTrue(convexHullResult.Count >= 3);
            Assert.IsTrue(boundaryResult.Count >= 3);
        }

        /// <summary>
        /// 创建测试实体集合
        /// </summary>
        /// <returns>测试实体集合</returns>
        private List<Entity> CreateTestEntities()
        {
            var entities = new List<Entity>();

            // 创建一个简单的矩形
            entities.Add(new Line(new Point3d(0, 0, 0), new Point3d(10, 0, 0)));
            entities.Add(new Line(new Point3d(10, 0, 0), new Point3d(10, 10, 0)));
            entities.Add(new Line(new Point3d(10, 10, 0), new Point3d(0, 10, 0)));
            entities.Add(new Line(new Point3d(0, 10, 0), new Point3d(0, 0, 0)));

            return entities;
        }
    }
}
