using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using System;
using System.Collections.Generic;
using System.Linq;

namespace IECAD.Services.OutlineWalker
{
    /// <summary>
    /// 栅格步行者 - 基于栅格化的传统方法，兼容性最好
    /// 适用场景：复杂图形或当其他策略失败时的备用方案，使用网格采样方法
    /// </summary>
    public class GridWalker : IOutlineWalker
    {
        private List<Entity> _entities;
        private List<Point3d> _pathPoints;
        private Point3d _currentPosition;
        private Point3d _startPosition;
        private WalkDirection _currentDirection;
        private WalkDirection _fromDirection;
        private bool _isCompleted;
        private double _tolerance;
        private double _gridSize;
        private HashSet<(int x, int y)> _occupancySet;
        private double _minX, _minY, _maxX, _maxY;
        private int _columnCount, _rowCount;
        private bool[,] _visited;
        private Stack<Point3d> _backtrackStack;

        public bool IsCompleted => _isCompleted;
        public Point3d CurrentPosition => _currentPosition;
        public string StrategyName => "栅格步行者";

        /// <summary>
        /// 初始化栅格步行者
        /// </summary>
        public void Initialize(IEnumerable<Entity> entities, double tolerance = 1.0)
        {
            _entities = entities.ToList();
            _tolerance = tolerance;
            _gridSize = Math.Max(tolerance, 20.0); // 默认栅格大小
            _pathPoints = new List<Point3d>();
            _backtrackStack = new Stack<Point3d>();
            _isCompleted = false;

            // 计算边界
            if (!CalculateBounds(out double minX, out double minY, out double maxX, out double maxY))
            {
                _isCompleted = true;
                return;
            }

            // 设置栅格系统参数
            _minX = minX;
            _minY = minY;
            _maxX = maxX;
            _maxY = maxY;
            _columnCount = (int)Math.Ceiling((maxX - minX) / _gridSize) + 1;
            _rowCount = (int)Math.Ceiling((maxY - minY) / _gridSize) + 1;
            _visited = new bool[_columnCount, _rowCount];

            // 构建占用集合
            BuildOccupancySet();

            // 查找起始点
            if (!FindStartingCell(out int startX, out int startY))
            {
                _isCompleted = true;
                return;
            }

            _startPosition = GetCellCenter(startX, startY);
            _currentPosition = _startPosition;
            _pathPoints.Add(_startPosition);

            // 初始方向设为东（向右）
            _currentDirection = WalkDirection.East;
            _fromDirection = WalkDirection.West;
        }

        /// <summary>
        /// 计算所有实体的边界
        /// </summary>
        private bool CalculateBounds(out double minX, out double minY, out double maxX, out double maxY)
        {
            minX = double.MaxValue;
            minY = double.MaxValue;
            maxX = double.MinValue;
            maxY = double.MinValue;

            var hasValidBounds = false;

            foreach (var entity in _entities)
            {
                try
                {
                    var extent = entity.GeometricExtents;
                    minX = Math.Min(minX, extent.MinPoint.X);
                    minY = Math.Min(minY, extent.MinPoint.Y);
                    maxX = Math.Max(maxX, extent.MaxPoint.X);
                    maxY = Math.Max(maxY, extent.MaxPoint.Y);
                    hasValidBounds = true;
                }
                catch
                {
                    // 忽略无效实体
                }
            }

            return hasValidBounds;
        }

        /// <summary>
        /// 构建占用集合
        /// </summary>
        private void BuildOccupancySet()
        {
            _occupancySet = new HashSet<(int, int)>();

            foreach (var entity in _entities)
            {
                SampleEntity(entity);
            }
        }

        /// <summary>
        /// 采样实体到栅格
        /// </summary>
        private void SampleEntity(Entity entity)
        {
            switch (entity)
            {
                case Line line:
                    SampleLine(line.StartPoint, line.EndPoint);
                    break;

                case Polyline polyline:
                    SamplePolyline(polyline);
                    break;

                case Circle circle:
                    SampleCircle(circle);
                    break;

                case Arc arc:
                    SampleArc(arc);
                    break;
            }
        }

        /// <summary>
        /// 采样直线
        /// </summary>
        private void SampleLine(Point3d start, Point3d end)
        {
            var dx = end.X - start.X;
            var dy = end.Y - start.Y;
            var length = Math.Sqrt(dx * dx + dy * dy);
            var steps = Math.Max(1, (int)(length / (_gridSize * 0.5)));

            for (int i = 0; i <= steps; i++)
            {
                var t = (double)i / steps;
                var point = start + (end - start) * t;
                AddPointToOcc(point);
            }
        }

        /// <summary>
        /// 采样多段线
        /// </summary>
        private void SamplePolyline(Polyline polyline)
        {
            for (int i = 0; i < polyline.NumberOfVertices; i++)
            {
                var vertex = polyline.GetPoint3dAt(i);
                AddPointToOcc(vertex);
                
                // 采样段
                if (i < polyline.NumberOfVertices - 1)
                {
                    var nextVertex = polyline.GetPoint3dAt(i + 1);
                    SampleLine(vertex, nextVertex);
                }
            }
        }

        /// <summary>
        /// 采样圆
        /// </summary>
        private void SampleCircle(Circle circle)
        {
            var circumference = 2 * Math.PI * circle.Radius;
            var steps = Math.Max(16, (int)(circumference / (_gridSize * 0.5)));

            for (int i = 0; i < steps; i++)
            {
                var angle = 2 * Math.PI * i / steps;
                var x = circle.Center.X + circle.Radius * Math.Cos(angle);
                var y = circle.Center.Y + circle.Radius * Math.Sin(angle);
                AddPointToOcc(new Point3d(x, y, 0));
            }
        }

        /// <summary>
        /// 采样圆弧
        /// </summary>
        private void SampleArc(Arc arc)
        {
            var arcLength = arc.Length;
            var steps = Math.Max(8, (int)(arcLength / (_gridSize * 0.5)));

            for (int i = 0; i <= steps; i++)
            {
                var param = arc.StartParam + (arc.EndParam - arc.StartParam) * i / steps;
                var point = arc.GetPointAtParameter(param);
                AddPointToOcc(point);
            }
        }

        /// <summary>
        /// 添加点到占用集合
        /// </summary>
        private void AddPointToOcc(Point3d point)
        {
            var x = (int)Math.Floor((point.X - _minX) / _gridSize);
            var y = (int)Math.Floor((point.Y - _minY) / _gridSize);

            if (IsValidCell(x, y))
            {
                _occupancySet.Add((x, y));
            }
        }

        /// <summary>
        /// 查找起始格子
        /// </summary>
        private bool FindStartingCell(out int startX, out int startY)
        {
            startX = -1;
            startY = -1;

            for (int x = 0; x < _columnCount; x++)
            {
                for (int y = 0; y < _rowCount; y++)
                {
                    if (_occupancySet.Contains((x, y)))
                    {
                        startX = x;
                        startY = y;
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 寻找下一个事件
        /// </summary>
        public WalkerEventResult FindNextEvent()
        {
            var result = new WalkerEventResult();
            result.Position = _currentPosition;
            result.FromDirection = _fromDirection;

            // 获取当前格子坐标
            var currentX = (int)Math.Floor((_currentPosition.X - _minX) / _gridSize);
            var currentY = (int)Math.Floor((_currentPosition.Y - _minY) / _gridSize);

            // 检查8个方向的相邻格子
            var directions = new[]
            {
                (WalkDirection.North, 0, 1),
                (WalkDirection.NorthEast, 1, 1),
                (WalkDirection.East, 1, 0),
                (WalkDirection.SouthEast, 1, -1),
                (WalkDirection.South, 0, -1),
                (WalkDirection.SouthWest, -1, -1),
                (WalkDirection.West, -1, 0),
                (WalkDirection.NorthWest, -1, 1)
            };

            foreach (var (direction, dx, dy) in directions)
            {
                var newX = currentX + dx;
                var newY = currentY + dy;

                if (IsValidCell(newX, newY) && 
                    _occupancySet.Contains((newX, newY)) && 
                    !_visited[newX, newY])
                {
                    result.AvailableDirections.Add(direction);
                }
            }

            // 检查是否回到起点
            if (GeometryHelper.IsWithinTolerance(_currentPosition, _startPosition, _gridSize) && _pathPoints.Count > 3)
            {
                result.EventType = WalkerEventType.OutlineClosed;
            }
            else if (result.AvailableDirections.Count == 0)
            {
                result.EventType = WalkerEventType.DeadEnd;
            }
            else
            {
                result.EventType = WalkerEventType.ReachVertex;
            }

            return result;
        }

        /// <summary>
        /// 根据事件做出方向决策
        /// </summary>
        public DirectionDecision MakeDecision(WalkerEventResult eventResult)
        {
            var decision = new DirectionDecision();

            if (eventResult.EventType == WalkerEventType.OutlineClosed)
            {
                decision.NextPosition = _startPosition;
                decision.Reason = "栅格轮廓闭合";
                return decision;
            }

            if (eventResult.EventType == WalkerEventType.DeadEnd)
            {
                decision.RequiresBacktrack = true;
                decision.Reason = "栅格死胡同，需要回溯";
                return decision;
            }

            // 应用最右转规则
            var chosenDirection = GeometryHelper.GetRightmostDirection(eventResult.FromDirection, eventResult.AvailableDirections);
            decision.ChosenDirection = chosenDirection;

            // 计算下一个位置
            var currentX = (int)Math.Floor((_currentPosition.X - _minX) / _gridSize);
            var currentY = (int)Math.Floor((_currentPosition.Y - _minY) / _gridSize);

            var (dx, dy) = GetDirectionOffset(chosenDirection);
            var nextX = currentX + dx;
            var nextY = currentY + dy;

            decision.NextPosition = GetCellCenter(nextX, nextY);
            decision.StepDistance = _gridSize;
            decision.Reason = $"栅格最右转规则选择 {chosenDirection}";

            return decision;
        }

        /// <summary>
        /// 获取方向偏移
        /// </summary>
        private (int dx, int dy) GetDirectionOffset(WalkDirection direction)
        {
            if (direction == null)
                return (0, 0);

            // 基于角度计算方向偏移
            var angle = direction.Angle;
            
            // 将角度转换为8方向的索引（0-7）
            var directionIndex = (int)Math.Round(angle / (Math.PI / 4)) % 8;
            if (directionIndex < 0) directionIndex += 8;
            
            return directionIndex switch
            {
                0 => (1, 0),    // East
                1 => (1, 1),    // NorthEast  
                2 => (0, 1),    // North
                3 => (-1, 1),   // NorthWest
                4 => (-1, 0),   // West
                5 => (-1, -1),  // SouthWest
                6 => (0, -1),   // South
                7 => (1, -1),   // SouthEast
                _ => (0, 0)
            };
        }

        /// <summary>
        /// 更新步行者状态
        /// </summary>
        public void UpdateState(DirectionDecision decision)
        {
            if (decision.RequiresBacktrack)
            {
                if (_backtrackStack.Count > 0)
                {
                    _currentPosition = _backtrackStack.Pop();
                    if (_pathPoints.Count > 1)
                    {
                        _pathPoints.RemoveAt(_pathPoints.Count - 1);
                    }
                }
                else
                {
                    _isCompleted = true;
                }
                return;
            }

            // 保存当前位置
            _backtrackStack.Push(_currentPosition);

            // 更新位置和方向
            _fromDirection = GeometryHelper.GetOppositeDirection(decision.ChosenDirection);
            _currentDirection = decision.ChosenDirection;
            _currentPosition = decision.NextPosition;

            // 标记为已访问
            var x = (int)Math.Floor((_currentPosition.X - _minX) / _gridSize);
            var y = (int)Math.Floor((_currentPosition.Y - _minY) / _gridSize);
            if (IsValidCell(x, y))
            {
                _visited[x, y] = true;
            }

            // 添加到路径
            _pathPoints.Add(_currentPosition);

            // 检查完成条件
            if (GeometryHelper.IsWithinTolerance(_currentPosition, _startPosition, _gridSize) && _pathPoints.Count > 3)
            {
                _isCompleted = true;
            }
        }

        /// <summary>
        /// 检查格子坐标是否有效
        /// </summary>
        private bool IsValidCell(int x, int y)
        {
            return x >= 0 && x < _columnCount && y >= 0 && y < _rowCount;
        }

        /// <summary>
        /// 获取格子中心点
        /// </summary>
        private Point3d GetCellCenter(int x, int y)
        {
            var centerX = _minX + (x + 0.5) * _gridSize;
            var centerY = _minY + (y + 0.5) * _gridSize;
            return new Point3d(centerX, centerY, 0);
        }

        /// <summary>
        /// 获取最终路径
        /// </summary>
        public List<Point3d> GetResultPath()
        {
            var result = new List<Point3d>(_pathPoints);

            // 确保闭合
            if (result.Count > 2 && !GeometryHelper.IsWithinTolerance(result.First(), result.Last(), 1e-6))
            {
                result.Add(result.First());
            }

            return result;
        }
    }
}
