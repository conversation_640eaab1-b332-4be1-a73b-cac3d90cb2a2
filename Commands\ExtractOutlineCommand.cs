using Autodesk.AutoCAD.Runtime;
using IECAD.Services;
[assembly: CommandClass(typeof(IECAD.Commands.ExtractOutlineCommand))]
namespace IECAD.Commands
{
    public class ExtractOutlineCommand
    {
        [CommandMethod("EXTRACTOUTLINE")]
        public void ExtractOutline()
        {
            var outlineExtractor = new OutlineExtractor();
            outlineExtractor.ExtractOutline();
        }
    }
}