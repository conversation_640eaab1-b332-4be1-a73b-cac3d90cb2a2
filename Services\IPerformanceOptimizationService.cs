using System;
using System.Collections.Generic;

namespace IECAD.Services
{
    /// <summary>
    /// Interface for performance optimization service
    /// </summary>
    public interface IPerformanceOptimizationService
    {
        /// <summary>
        /// Optimize application performance settings
        /// </summary>
        void OptimizePerformance();

        /// <summary>
        /// Monitor and optimize memory usage
        /// </summary>
        void OptimizeMemory();

        /// <summary>
        /// Get performance recommendations based on current usage
        /// </summary>
        /// <returns>List of performance recommendations</returns>
        List<PerformanceRecommendation> GetPerformanceRecommendations();

        /// <summary>
        /// Apply automatic optimizations based on current performance metrics
        /// </summary>
        void ApplyAutomaticOptimizations();

        /// <summary>
        /// Get current performance metrics
        /// </summary>
        /// <returns>Performance metrics</returns>
        PerformanceMetrics GetPerformanceMetrics();

        /// <summary>
        /// Enable or disable performance monitoring
        /// </summary>
        /// <param name="enabled">Whether to enable monitoring</param>
        void SetPerformanceMonitoring(bool enabled);
    }

    /// <summary>
    /// Performance recommendation
    /// </summary>
    public class PerformanceRecommendation
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public PerformanceImpact Impact { get; set; }
        public string Category { get; set; }
        public Action ApplyAction { get; set; }
    }

    /// <summary>
    /// Performance impact level
    /// </summary>
    public enum PerformanceImpact
    {
        Low,
        Medium,
        High,
        Critical
    }

    /// <summary>
    /// Performance metrics
    /// </summary>
    public class PerformanceMetrics
    {
        public TimeSpan AverageResponseTime { get; set; }
        public long MemoryUsageMB { get; set; }
        public int ActiveObjects { get; set; }
        public int CacheHitRate { get; set; }
        public int GCCollections { get; set; }
        public DateTime LastOptimization { get; set; }
        public Dictionary<string, TimeSpan> OperationTimes { get; set; } = new Dictionary<string, TimeSpan>();
    }
}
