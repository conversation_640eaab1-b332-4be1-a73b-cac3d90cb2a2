using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using System;
using System.Collections.Generic;
using System.Linq;

namespace IECAD.Services.OutlineWalker
{
    /// <summary>
    /// 轮廓追踪主引擎 - 负责整个流程控制，调用策略模块的标准化接口
    /// </summary>
    public class OutlineWalkerEngine
    {
        private readonly Dictionary<WalkerStrategy, Type> _walkerTypes;

        public OutlineWalkerEngine()
        {
            _walkerTypes = new Dictionary<WalkerStrategy, Type>
            {
                { WalkerStrategy.Precise, typeof(PreciseWalker) },
                { WalkerStrategy.Tolerant, typeof(TolerantWalker) },
                { WalkerStrategy.Morphological, typeof(MorphologicalWalker) },
                { WalkerStrategy.Grid, typeof(GridWalker) }
            };
        }

        /// <summary>
        /// 执行轮廓提取的主流程
        /// </summary>
        /// <param name="strategy">选择的步行者策略</param>
        /// <param name="tolerance">容差参数</param>
        /// <returns>轮廓点集合</returns>
        public List<Point3d> ExtractOutline(WalkerStrategy strategy = WalkerStrategy.Precise, double tolerance = 1.0)
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            // 对文档加锁以避免 eLockViolation
            using (DocumentLock docLock = doc.LockDocument())
            // 使用单个事务管理整个流程
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    // 1. 启动与接收输入
                    var entities = PromptUserSelection(ed, tr);
                    if (entities == null || entities.Count == 0)
                    {
                        ed.WriteMessage("\n没有选择有效的实体。");
                        tr.Abort();
                        return new List<Point3d>();
                    }

                    // 2. 提供策略选项（如果没有预设策略）
                    if (strategy == WalkerStrategy.Precise) // 默认值，提示用户选择
                    {
                        strategy = PromptStrategySelection(ed);
                    }

                    // 3. 加载策略模块
                    var walker = CreateWalker(strategy);
                    if (walker == null)
                    {
                        ed.WriteMessage($"\n无法创建 {strategy} 策略的步行者。");
                        tr.Abort();
                        return new List<Point3d>();
                    }

                    ed.WriteMessage($"\n使用 {walker.StrategyName} 策略进行轮廓追踪...");

                    // 4. 执行追踪
                    var result = ExecuteWalking(walker, entities, tolerance, ed);

                    // 5. 生成输出
                    if (result != null && result.Count > 2)
                    {
                        GenerateOutput(result, tr, db);
                        ed.WriteMessage($"\n轮廓追踪完成！生成了 {result.Count} 个顶点的轮廓。");
                        tr.Commit();
                    }
                    else
                    {
                        ed.WriteMessage("\n轮廓追踪失败或结果无效。");
                        tr.Abort();
                    }

                    return result ?? new List<Point3d>();
                }
                catch (Exception ex)
                {
                    ed.WriteMessage($"\n轮廓提取过程中发生错误: {ex.Message}");
                    tr.Abort();
                    return new List<Point3d>();
                }
            }
        }

        /// <summary>
        /// 提示用户选择实体
        /// </summary>
        private List<Entity> PromptUserSelection(Editor ed, Transaction tr)
        {
            var psr = ed.GetSelection();
            if (psr.Status != PromptStatus.OK) return null;

            var entities = new List<Entity>();
            foreach (ObjectId id in psr.Value.GetObjectIds())
            {
                if (tr.GetObject(id, OpenMode.ForRead) is Entity ent)
                {
                    // 过滤掉文本和属性定义等不需要的实体
                    if (IsValidEntityForOutline(ent))
                    {
                        entities.Add(ent);
                    }
                }
            }

            return entities;
        }

        /// <summary>
        /// 检查实体是否适合进行轮廓提取
        /// </summary>
        private bool IsValidEntityForOutline(Entity entity)
        {
            // 排除文本、属性定义等
            return !(entity is DBText || 
                     entity is MText || 
                     entity is AttributeDefinition || 
                     entity is AttributeReference ||
                     entity is Dimension);
        }

        /// <summary>
        /// 提示用户选择策略
        /// </summary>
        private WalkerStrategy PromptStrategySelection(Editor ed)
        {
            var pko = new PromptKeywordOptions("\n选择轮廓追踪策略");
            pko.Keywords.Add("Precise", "精确", "精确步行者（P）");
            pko.Keywords.Add("Tolerant", "容差", "容差步行者（T）");
            pko.Keywords.Add("Morphological", "形态", "形态学步行者（M）");
            pko.Keywords.Add("Grid", "栅格", "栅格步行者（G）");
            pko.Keywords.Default = "Precise";
            pko.AllowNone = true;

            var pkr = ed.GetKeywords(pko);
            
            switch (pkr.StringResult)
            {
                case "Tolerant": return WalkerStrategy.Tolerant;
                case "Morphological": return WalkerStrategy.Morphological;
                case "Grid": return WalkerStrategy.Grid;
                default: return WalkerStrategy.Precise;
            }
        }

        /// <summary>
        /// 创建指定策略的步行者实例
        /// </summary>
        private IOutlineWalker CreateWalker(WalkerStrategy strategy)
        {
            if (!_walkerTypes.TryGetValue(strategy, out var walkerType))
                return null;

            try
            {
                return (IOutlineWalker)Activator.CreateInstance(walkerType);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 执行步行追踪的主循环
        /// </summary>
        private List<Point3d> ExecuteWalking(IOutlineWalker walker, List<Entity> entities, double tolerance, Editor ed)
        {
            try
            {
                // 初始化：调用策略对象的 Initialize() 方法
                walker.Initialize(entities, tolerance);

                var maxIterations = 10000; // 防止无限循环
                var iterations = 0;

                // 循环追踪：进入主循环，持续执行直到轮廓闭合
                while (!walker.IsCompleted && iterations < maxIterations)
                {
                    iterations++;

                    // a. 调用策略对象的 FindNextEvent() 方法
                    var eventResult = walker.FindNextEvent();

                    if (eventResult.EventType == WalkerEventType.OutlineClosed)
                    {
                        break;
                    }

                    if (eventResult.EventType == WalkerEventType.DeadEnd)
                    {
                        ed.WriteMessage("\n遇到死胡同，尝试回溯...");
                        // 这里可以实现回溯逻辑
                        break;
                    }

                    // b. 调用策略对象的 MakeDecision() 方法
                    var decision = walker.MakeDecision(eventResult);

                    // c. 调用策略对象的 UpdateState() 方法
                    walker.UpdateState(decision);

                    // 每100次迭代输出一次进度
                    if (iterations % 100 == 0)
                    {
                        ed.WriteMessage($"\n追踪进度: {iterations} 步，当前位置: ({walker.CurrentPosition.X:F2}, {walker.CurrentPosition.Y:F2})");
                    }
                }

                if (iterations >= maxIterations)
                {
                    ed.WriteMessage("\n达到最大迭代次数，追踪终止。");
                }

                // 获取结果：调用策略对象的 GetResultPath() 方法
                return walker.GetResultPath();
            }
            catch (Exception ex)
            {
                ed.WriteMessage($"\n步行追踪过程中发生错误: {ex.Message}");
                return new List<Point3d>();
            }
        }

        /// <summary>
        /// 生成输出到AutoCAD图纸
        /// </summary>
        private void GenerateOutput(List<Point3d> points, Transaction tr, Database db)
        {
            try
            {
                // 创建多段线
                using (var polyline = new Polyline())
                {
                    for (int i = 0; i < points.Count; i++)
                    {
                        polyline.AddVertexAt(i, new Point2d(points[i].X, points[i].Y), 0, 0, 0);
                    }

                    // 如果起点和终点接近，则闭合多段线
                    if (points.Count > 2)
                    {
                        var firstPoint = points.First();
                        var lastPoint = points.Last();
                        if (GeometryHelper.IsWithinTolerance(firstPoint, lastPoint, 1e-6))
                        {
                            polyline.Closed = true;
                        }
                    }

                    // 设置多段线属性
                    polyline.ColorIndex = 1; // 红色
                    polyline.LineWeight = LineWeight.LineWeight030;

                    // 添加到模型空间
                    var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                    var btr = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite);

                    btr.AppendEntity(polyline);
                    tr.AddNewlyCreatedDBObject(polyline, true);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"生成输出时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取可用策略列表
        /// </summary>
        public List<WalkerStrategy> GetAvailableStrategies()
        {
            return _walkerTypes.Keys.ToList();
        }

        /// <summary>
        /// 获取策略描述
        /// </summary>
        public string GetStrategyDescription(WalkerStrategy strategy)
        {
            switch (strategy)
            {
                case WalkerStrategy.Precise:
                    return "精确步行者 - 严格沿着几何对象的精确路径行走，适合精确工程图纸";
                case WalkerStrategy.Tolerant:
                    return "容差步行者 - 能感知附近线条并智能桥接缝隙，适合有微小误差的图纸";
                case WalkerStrategy.Morphological:
                    return "形态学步行者 - 圆盘滚动追踪圆心轨迹，用于形态学膨胀分析";
                case WalkerStrategy.Grid:
                    return "栅格步行者 - 基于栅格化的传统方法，兼容性最好";
                default:
                    return "未知策略";
            }
        }
    }
}
