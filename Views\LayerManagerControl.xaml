﻿<UserControl x:Class="IECAD.Views.LayerManagerControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IECAD.Views"
             xmlns:helpers="clr-namespace:IECAD.Helpers"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="300">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <helpers:BooleanToColorConverter x:Key="BooleanToColorConverter"/>
        <helpers:StringToBooleanConverter x:Key="StringToBooleanConverter"/>

        <!-- Layer item style -->
        <Style x:Key="LayerItemStyle" TargetType="ListBoxItem">
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="0,1"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ListBoxItem">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F1F5F9"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="#E3F2FD"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0"
                   Text="Layer Manager"
                   FontSize="14"
                   FontWeight="SemiBold"
                   Margin="8,8,8,4"
                   Foreground="#2C3E50"/>

        <!-- Search Box -->
        <Grid Grid.Row="1" Margin="8,4">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBox Grid.Column="0"
                     Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                     FontSize="11"
                     Padding="8,4"
                     BorderBrush="#E9ECEF"
                     BorderThickness="1">
                <TextBox.Style>
                    <Style TargetType="TextBox">
                        <Style.Triggers>
                            <Trigger Property="Text" Value="">
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <VisualBrush AlignmentX="Left" AlignmentY="Center" Stretch="None">
                                            <VisualBrush.Visual>
                                                <TextBlock Text="Search layers..."
                                                          Foreground="#6C7A89"
                                                          FontSize="11"
                                                          Margin="8,0"/>
                                            </VisualBrush.Visual>
                                        </VisualBrush>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </TextBox.Style>
            </TextBox>

            <Button Grid.Column="1"
                    Content="✕"
                    Command="{Binding ClearSearchCommand}"
                    Width="24" Height="24"
                    FontSize="10"
                    Margin="4,0,0,0"
                    Background="Transparent"
                    BorderThickness="0"
                    Foreground="#6C7A89"
                    Visibility="{Binding SearchText, Converter={StaticResource BooleanToVisibilityConverter}}"/>
        </Grid>

        <!-- Toolbar -->
        <StackPanel Grid.Row="2"
                   Orientation="Horizontal"
                   Margin="8,4">
            <Button Content="Refresh"
                    Command="{Binding RefreshCommand}"
                    FontSize="10"
                    Padding="8,4"
                    Margin="0,0,4,0"
                    Background="#F8F9FA"
                    BorderBrush="#E9ECEF"
                    BorderThickness="1"/>
            <Button Content="Toggle All"
                    Command="{Binding ToggleAllVisibilityCommand}"
                    FontSize="10"
                    Padding="8,4"
                    Background="#F8F9FA"
                    BorderBrush="#E9ECEF"
                    BorderThickness="1"/>
        </StackPanel>

        <!-- Layer List -->
        <ListBox Grid.Row="3"
                 ItemsSource="{Binding Layers}"
                 SelectedItem="{Binding SelectedLayer}"
                 ItemContainerStyle="{StaticResource LayerItemStyle}"
                 BorderThickness="0"
                 Background="Transparent"
                 ScrollViewer.HorizontalScrollBarVisibility="Disabled">
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Visibility Toggle -->
                        <CheckBox Grid.Column="0"
                                  IsChecked="{Binding IsVisible}"
                                  Command="{Binding DataContext.ToggleVisibilityCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                  CommandParameter="{Binding}"
                                  Margin="0,0,8,0"
                                  VerticalAlignment="Center"/>

                        <!-- Layer Name -->
                        <TextBlock Grid.Column="1"
                                   Text="{Binding Name}"
                                   FontSize="11"
                                   VerticalAlignment="Center"
                                   Foreground="#2C3E50"/>

                        <!-- Status Indicator -->
                        <Ellipse Grid.Column="2"
                                Width="8" Height="8"
                                Fill="{Binding IsVisible, Converter={StaticResource BooleanToColorConverter}}"
                                Margin="8,0,0,0"/>
                    </Grid>
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>

        <!-- Progress Indicator -->
        <local:ProgressIndicator Grid.Row="4"
                               Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}"
                               Message="{Binding BusyMessage}"
                               Margin="8"/>
    </Grid>
</UserControl>