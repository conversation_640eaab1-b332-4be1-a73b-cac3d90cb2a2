using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.GraphicsInterface;
using Autodesk.AutoCAD.Runtime;
using System;
using System.Collections.Generic;
using System.Linq;

namespace IECAD.Services
{
    public class AlignService
    {
        public void AlignBlockToLine()
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;

            // 1. 选择基准块
            var peoBase = new PromptEntityOptions("\n请选择基准图块：");
            peoBase.SetRejectMessage("\n必须选择图块实体。");
            peoBase.AddAllowedClass(typeof(BlockReference), false);
            var perBase = ed.GetEntity(peoBase);
            if (perBase.Status != PromptStatus.OK) return;
            var baseId = perBase.ObjectId;

            // 2. 选择其他图块（可多选，回车结束）
            var psoOther = new PromptSelectionOptions();
            psoOther.MessageForAdding = "\n请选择其他要对齐的图块（可多选，回车结束）：";
            var filterOther = new SelectionFilter(
                new TypedValue[] { new TypedValue((int)DxfCode.Start, "INSERT") }
            );
            var psrOther = ed.GetSelection(psoOther, filterOther);
            var blkIds = new List<ObjectId> { baseId };
            if (psrOther.Status == PromptStatus.OK)
                blkIds.AddRange(psrOther.Value.GetObjectIds().Where(id => id != baseId));

            // 3. 选择直线或多段线（支持块内实体）
            if (!TrySelectLineOrPolyline(ed, out LineSegment3d selSeg, out Matrix3d xform)) return;

            // 高亮选中线段
            HighlightSegment(selSeg);

            using (var tr = doc.TransactionManager.StartTransaction())
            {
                // 打开并获取所有选中图块
                var blkRefs = new List<BlockReference>();
                foreach (var id in blkIds)
                {
                    var br = tr.GetObject(id, OpenMode.ForWrite) as BlockReference;
                    if (br != null) blkRefs.Add(br);
                }
                if (blkRefs.Count == 0) return;


                // 计算块包围盒
                //Extents3d ext = blkRefs[0].GeometricExtents;
                Extents3d ext = GetBlockExtents(blkRefs[0], tr);

                // 计算线的法向量（单位）
                Vector3d dir = (selSeg.EndPoint - selSeg.StartPoint).GetNormal();
                var normal = new Vector3d(-dir.Y, dir.X, 0.0).GetNormal();

                // 计算包围盒四角点到线的有符号距离
                var corners = new List<Point3d>
                {
                    new Point3d(ext.MinPoint.X, ext.MinPoint.Y, ext.MinPoint.Z),
                    new Point3d(ext.MinPoint.X, ext.MaxPoint.Y, ext.MinPoint.Z),
                    new Point3d(ext.MaxPoint.X, ext.MinPoint.Y, ext.MinPoint.Z),
                    new Point3d(ext.MaxPoint.X, ext.MaxPoint.Y, ext.MinPoint.Z)
                };
                double nearVal = 0, farVal = 0;
                bool init = false;
                foreach (var pt in corners)
                {
                    double d = (pt - selSeg.StartPoint).DotProduct(normal);
                    if (!init || Math.Abs(d) < Math.Abs(nearVal)) { nearVal = d; }
                    if (!init || Math.Abs(d) > Math.Abs(farVal)) { farVal = d; init = true; }
                }

                // 选择近边或远边
                var pko = new PromptKeywordOptions("\n选择对齐边 [近边(N)/远边(F)]:", "N F");
                pko.AllowNone = false;
                var pkr = ed.GetKeywords(pko);
                if (pkr.Status != PromptStatus.OK) return;
                bool useNear = pkr.StringResult == "N";
                double sideVal = useNear ? nearVal : farVal;
                int signSide = Math.Sign(sideVal);
                if (signSide == 0) signSide = 1;

                // 输入对齐距离
                var pdo = new PromptDistanceOptions("\n输入对齐距离(默认0):")
                {
                    AllowNegative = true,
                    UseDefaultValue = true,
                    DefaultValue = 0
                };
                var prd = ed.GetDistance(pdo);
                if (prd.Status != PromptStatus.OK) return;
                double inputDist = prd.Value;

                // 计算并应用垂直对齐（逐个块）
                foreach (var br in blkRefs)
                {
                    // 计算该块包围盒角点与线的投影
                    Extents3d briExt = br.GeometricExtents;
                    var briCorners = new[]
                    {
                        new Point3d(briExt.MinPoint.X, briExt.MinPoint.Y, briExt.MinPoint.Z),
                        new Point3d(briExt.MinPoint.X, briExt.MaxPoint.Y, briExt.MinPoint.Z),
                        new Point3d(briExt.MaxPoint.X, briExt.MinPoint.Y, briExt.MinPoint.Z),
                        new Point3d(briExt.MaxPoint.X, briExt.MaxPoint.Y, briExt.MinPoint.Z)
                    };
                    double nearValI = 0, farValI = 0; bool initI = false;
                    foreach (var pt in briCorners)
                    {
                        double d = (pt - selSeg.StartPoint).DotProduct(normal);
                        if (!initI || Math.Abs(d) < Math.Abs(nearValI)) { nearValI = d; }
                        if (!initI || Math.Abs(d) > Math.Abs(farValI)) { farValI = d; initI = true; }
                    }
                    // 计算对齐位移（沿垂直方向）
                    double sideValI = useNear ? nearValI : farValI;
                    double transDistI = -sideValI + inputDist;
                    var dispI = normal * transDistI;
                    br.TransformBy(Matrix3d.Displacement(dispI));
                }

                // 6. 块沿线方向分布并调整间距（基于第一个选中块）
                var spacingOpts = new PromptDistanceOptions("\n输入块间距离(默认600):")
                {
                    AllowNegative = false,
                    UseDefaultValue = true,
                    DefaultValue = 600
                };
                var spd2 = ed.GetDistance(spacingOpts);
                if (spd2.Status != PromptStatus.OK) { tr.Commit(); return; }
                double spacing = spd2.Value;

                // 7. 按块边净距分布，以选定块为基准
                var sortedRefs = blkRefs.OrderBy(b => (b.Position - selSeg.StartPoint).DotProduct(dir)).ToList();
                int baseIndex = sortedRefs.FindIndex(b => b.ObjectId == baseId);
                // 向前分布
                for (int idx = baseIndex + 1; idx < sortedRefs.Count; idx++)
                {
                    var prevRef = sortedRefs[idx - 1];
                    var currRef = sortedRefs[idx];
                    var prevExt = prevRef.GeometricExtents;
                    var prevCorners = new[] { new Point3d(prevExt.MinPoint.X, prevExt.MinPoint.Y, prevExt.MinPoint.Z), new Point3d(prevExt.MinPoint.X, prevExt.MaxPoint.Y, prevExt.MinPoint.Z), new Point3d(prevExt.MaxPoint.X, prevExt.MinPoint.Y, prevExt.MinPoint.Z), new Point3d(prevExt.MaxPoint.X, prevExt.MaxPoint.Y, prevExt.MinPoint.Z) };
                    double prevMaxProj = prevCorners.Max(pt => (pt - selSeg.StartPoint).DotProduct(dir));
                    var currExt = currRef.GeometricExtents;
                    var currCorners = new[] { new Point3d(currExt.MinPoint.X, currExt.MinPoint.Y, currExt.MinPoint.Z), new Point3d(currExt.MinPoint.X, currExt.MaxPoint.Y, currExt.MinPoint.Z), new Point3d(currExt.MaxPoint.X, currExt.MinPoint.Y, currExt.MinPoint.Z), new Point3d(currExt.MaxPoint.X, currExt.MaxPoint.Y, currExt.MinPoint.Z) };
                    double currMinProj = currCorners.Min(pt => (pt - selSeg.StartPoint).DotProduct(dir));
                    double deltaF = prevMaxProj + spacing - currMinProj;
                    currRef.TransformBy(Matrix3d.Displacement(dir * deltaF));
                }
                // 向后分布
                for (int idx = baseIndex - 1; idx >= 0; idx--)
                {
                    var nextRef = sortedRefs[idx + 1];
                    var currRef = sortedRefs[idx];
                    var nextExt = nextRef.GeometricExtents;
                    var nextCorners = new[] { new Point3d(nextExt.MinPoint.X, nextExt.MinPoint.Y, nextExt.MinPoint.Z), new Point3d(nextExt.MinPoint.X, nextExt.MaxPoint.Y, nextExt.MinPoint.Z), new Point3d(nextExt.MaxPoint.X, nextExt.MinPoint.Y, nextExt.MinPoint.Z), new Point3d(nextExt.MaxPoint.X, nextExt.MaxPoint.Y, nextExt.MinPoint.Z) };
                    double nextMinProj = nextCorners.Min(pt => (pt - selSeg.StartPoint).DotProduct(dir));
                    var currExt2 = currRef.GeometricExtents;
                    var currCorners2 = new[] { new Point3d(currExt2.MinPoint.X, currExt2.MinPoint.Y, currExt2.MinPoint.Z), new Point3d(currExt2.MinPoint.X, currExt2.MaxPoint.Y, currExt2.MinPoint.Z), new Point3d(currExt2.MaxPoint.X, currExt2.MinPoint.Y, currExt2.MinPoint.Z), new Point3d(currExt2.MaxPoint.X, currExt2.MaxPoint.Y, currExt2.MinPoint.Z) };
                    double currMaxProj2 = currCorners2.Max(pt => (pt - selSeg.StartPoint).DotProduct(dir));
                    double deltaB = nextMinProj - spacing - currMaxProj2;
                    currRef.TransformBy(Matrix3d.Displacement(dir * deltaB));
                }
                tr.Commit();
            }
        }


        /// <summary>
        /// 选择直线、多段线段或天正墙体（支持块内实体），直接返回参考线段
        /// </summary>
        private bool TrySelectLineOrPolyline(Editor ed, out LineSegment3d refSeg, out Matrix3d xform)
        {
            refSeg = null;
            xform = Matrix3d.Identity;
            var pno = new PromptNestedEntityOptions("\n请选择直线、多段线或天正墙体：");
            var pnr = ed.GetNestedEntity(pno);
            if (pnr.Status != PromptStatus.OK) return false;
            var lineId = pnr.ObjectId;
            var pickPt = pnr.PickedPoint;
            xform = pnr.Transform;
            using (var tr = ed.Document.Database.TransactionManager.StartTransaction())
            {
                var ent = tr.GetObject(lineId, OpenMode.ForRead) as Entity;
                if (ent == null) return false;
                if (ent is Line line)
                {
                    refSeg = new LineSegment3d(line.StartPoint, line.EndPoint);
                    refSeg.TransformBy(xform);
                    return true;
                }
                else if (ent is Autodesk.AutoCAD.DatabaseServices.Polyline poly)
                {
                    int numVert = poly.NumberOfVertices;
                    int segCount = poly.Closed ? numVert : numVert - 1;
                    double minDist = double.MaxValue;
                    LineSegment3d bestSeg = null;
                    for (int i = 0; i < segCount; i++)
                    {
                        var p1 = poly.GetPoint3dAt(i);
                        var p2 = poly.GetPoint3dAt((i + 1) % numVert);
                        var seg = new LineSegment3d(p1, p2);
                        seg.TransformBy(xform);
                        double d = seg.GetDistanceTo(pickPt);
                        if (d < minDist)
                        {
                            minDist = d;
                            bestSeg = seg;
                        }
                    }
                    if (bestSeg != null)
                    {
                        refSeg = bestSeg;
                        return true;
                    }
                }
                else if (ent.GetType().Name == "ImpCurve")
                {
                    var type = ent.GetType();
                    var startProp = type.GetProperty("StartPoint");
                    var endProp = type.GetProperty("EndPoint");
                    if (startProp != null && endProp != null)
                    {
                        var startPt = (Point3d)startProp.GetValue(ent);
                        var endPt = (Point3d)endProp.GetValue(ent);
                        refSeg = new LineSegment3d(startPt.TransformBy(xform), endPt.TransformBy(xform));
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 临时高亮指定线段
        /// </summary>
        private void HighlightSegment(LineSegment3d seg)
        {
            // 创建临时线实体并设置颜色
            var tempLine = new Line(seg.StartPoint, seg.EndPoint)
            {
                ColorIndex = 1
            };
            tempLine.SetDatabaseDefaults();
            // 添加瞬时图形，仅短期显示
            TransientManager.CurrentTransientManager.AddTransient(
                tempLine,
                TransientDrawingMode.DirectShortTerm,
                128,
                new IntegerCollection()
            );
        }
        /// <summary>
        /// 计算块的包围盒，排除文字、MText和属性
        /// </summary>
        private Extents3d GetBlockExtents(BlockReference br, Transaction tr)
        {
            var btr = tr.GetObject(br.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
            Point3d minPt = new Point3d(double.MaxValue, double.MaxValue, double.MaxValue);
            Point3d maxPt = new Point3d(double.MinValue, double.MinValue, double.MinValue);
            foreach (ObjectId entId in btr)
            {
                var ent = tr.GetObject(entId, OpenMode.ForRead) as Entity;
                if (ent == null || ent is DBText || ent is MText || ent is AttributeDefinition || ent is AttributeReference)
                    continue;
                try
                {
                    var entExt = ent.GeometricExtents;
                    var pts = new[]
                    {
                        entExt.MinPoint,
                        new Point3d(entExt.MinPoint.X, entExt.MinPoint.Y, entExt.MaxPoint.Z),
                        new Point3d(entExt.MinPoint.X, entExt.MaxPoint.Y, entExt.MinPoint.Z),
                        new Point3d(entExt.MaxPoint.X, entExt.MinPoint.Y, entExt.MinPoint.Z),
                        new Point3d(entExt.MinPoint.X, entExt.MaxPoint.Y, entExt.MaxPoint.Z),
                        new Point3d(entExt.MaxPoint.X, entExt.MinPoint.Y, entExt.MaxPoint.Z),
                        new Point3d(entExt.MaxPoint.X, entExt.MaxPoint.Y, entExt.MinPoint.Z),
                        entExt.MaxPoint
                    };
                    foreach (var pt in pts)
                    {
                        var wpt = pt.TransformBy(br.BlockTransform);
                        minPt = new Point3d(Math.Min(minPt.X, wpt.X), Math.Min(minPt.Y, wpt.Y), Math.Min(minPt.Z, wpt.Z));
                        maxPt = new Point3d(Math.Max(maxPt.X, wpt.X), Math.Max(maxPt.Y, wpt.Y), Math.Max(maxPt.Z, wpt.Z));
                    }
                }
                catch { }
            }
            if (minPt.X > maxPt.X)
                return new Extents3d(br.Position, br.Position);
            return new Extents3d(minPt, maxPt);
        }
    }
}