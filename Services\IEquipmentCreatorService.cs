using System.Collections.Generic;

namespace IECAD.Services
{
    /// <summary>
    /// Interface for equipment creation service
    /// </summary>
    public interface IEquipmentCreatorService
    {
        /// <summary>
        /// Create equipment blocks from an Excel file
        /// </summary>
        /// <param name="filePath">Path to the Excel file containing equipment data</param>
        /// <returns>True if the operation was successful</returns>
        bool CreateEquipmentFromExcel(string filePath);

        /// <summary>
        /// Create equipment blocks from a list of equipment data
        /// </summary>
        /// <param name="equipmentList">List of equipment to create</param>
        /// <returns>True if the operation was successful</returns>
        bool CreateEquipmentBlocks(List<Equipment> equipmentList);

        /// <summary>
        /// Read equipment data from an Excel file
        /// </summary>
        /// <param name="filePath">Path to the Excel file</param>
        /// <returns>List of equipment data</returns>
        List<Equipment> ReadEquipmentDataFromExcel(string filePath);

        /// <summary>
        /// Validate equipment data
        /// </summary>
        /// <param name="equipment">Equipment to validate</param>
        /// <returns>True if the equipment data is valid</returns>
        bool ValidateEquipment(Equipment equipment);
    }

    /// <summary>
    /// Equipment data model
    /// </summary>
    public class Equipment
    {
        public string Name { get; set; } = string.Empty;
        public string Model { get; set; } = string.Empty;
        public int Amount { get; set; }
        public double Width { get; set; }
        public double Length { get; set; }
    }
}
