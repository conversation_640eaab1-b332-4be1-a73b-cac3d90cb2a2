using System;
using System.Collections.Concurrent;
using System.Threading;
using IECAD.Services;

namespace IECAD.Helpers
{
    /// <summary>
    /// Generic object pool for reusing expensive objects to reduce garbage collection pressure
    /// </summary>
    /// <typeparam name="T">Type of objects to pool</typeparam>
    public class ObjectPool<T> : IDisposable where T : class
    {
        private readonly ConcurrentQueue<T> _objects = new ConcurrentQueue<T>();
        private readonly Func<T> _objectFactory;
        private readonly Action<T> _resetAction;
        private readonly int _maxSize;
        private readonly ILoggingService _loggingService;
        private int _currentCount = 0;
        private bool _disposed = false;

        /// <summary>
        /// Initialize object pool
        /// </summary>
        /// <param name="objectFactory">Factory function to create new objects</param>
        /// <param name="resetAction">Action to reset objects before returning to pool</param>
        /// <param name="maxSize">Maximum number of objects to keep in pool</param>
        /// <param name="loggingService">Optional logging service</param>
        public ObjectPool(Func<T> objectFactory, Action<T> resetAction = null, int maxSize = 100, ILoggingService loggingService = null)
        {
            _objectFactory = objectFactory ?? throw new ArgumentNullException(nameof(objectFactory));
            _resetAction = resetAction;
            _maxSize = maxSize;
            _loggingService = loggingService;
        }

        /// <summary>
        /// Get an object from the pool or create a new one
        /// </summary>
        /// <returns>Object from pool or newly created</returns>
        public T Get()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(ObjectPool<T>));

            if (_objects.TryDequeue(out T item))
            {
                Interlocked.Decrement(ref _currentCount);
                _loggingService?.LogDebug($"Retrieved {typeof(T).Name} from pool. Pool size: {_currentCount}", "ObjectPool");
                return item;
            }

            // Create new object if pool is empty
            var newItem = _objectFactory();
            _loggingService?.LogDebug($"Created new {typeof(T).Name}. Pool size: {_currentCount}", "ObjectPool");
            return newItem;
        }

        /// <summary>
        /// Return an object to the pool
        /// </summary>
        /// <param name="item">Object to return to pool</param>
        public void Return(T item)
        {
            if (_disposed || item == null)
                return;

            // Don't exceed max pool size
            if (_currentCount >= _maxSize)
            {
                _loggingService?.LogDebug($"Pool full, discarding {typeof(T).Name}. Pool size: {_currentCount}", "ObjectPool");
                
                // Dispose if the object implements IDisposable
                if (item is IDisposable disposable)
                {
                    disposable.Dispose();
                }
                return;
            }

            try
            {
                // Reset object state if reset action is provided
                _resetAction?.Invoke(item);

                _objects.Enqueue(item);
                Interlocked.Increment(ref _currentCount);
                _loggingService?.LogDebug($"Returned {typeof(T).Name} to pool. Pool size: {_currentCount}", "ObjectPool");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error returning {typeof(T).Name} to pool: {ex.Message}", "ObjectPool");
                
                // Dispose if the object implements IDisposable
                if (item is IDisposable disposable)
                {
                    disposable.Dispose();
                }
            }
        }

        /// <summary>
        /// Get current pool size
        /// </summary>
        public int Count => _currentCount;

        /// <summary>
        /// Get maximum pool size
        /// </summary>
        public int MaxSize => _maxSize;

        /// <summary>
        /// Clear all objects from the pool
        /// </summary>
        public void Clear()
        {
            while (_objects.TryDequeue(out T item))
            {
                if (item is IDisposable disposable)
                {
                    disposable.Dispose();
                }
            }
            
            Interlocked.Exchange(ref _currentCount, 0);
            _loggingService?.LogDebug($"Cleared {typeof(T).Name} pool", "ObjectPool");
        }

        /// <summary>
        /// Create a pooled object scope that automatically returns the object to the pool when disposed
        /// </summary>
        /// <returns>Pooled object scope</returns>
        public PooledObjectScope<T> GetScoped()
        {
            return new PooledObjectScope<T>(this);
        }

        /// <summary>
        /// Dispose the object pool and all contained objects
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                Clear();
                _disposed = true;
                _loggingService?.LogDebug($"Disposed {typeof(T).Name} pool", "ObjectPool");
            }
        }
    }

    /// <summary>
    /// Scoped wrapper for pooled objects that automatically returns the object to the pool when disposed
    /// </summary>
    /// <typeparam name="T">Type of pooled object</typeparam>
    public class PooledObjectScope<T> : IDisposable where T : class
    {
        private readonly ObjectPool<T> _pool;
        private bool _disposed = false;

        /// <summary>
        /// The pooled object
        /// </summary>
        public T Object { get; private set; }

        internal PooledObjectScope(ObjectPool<T> pool)
        {
            _pool = pool;
            Object = pool.Get();
        }

        /// <summary>
        /// Return the object to the pool
        /// </summary>
        public void Dispose()
        {
            if (!_disposed && Object != null)
            {
                _pool.Return(Object);
                Object = null;
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Static factory for common object pools
    /// </summary>
    public static class ObjectPools
    {
        private static readonly Lazy<ObjectPool<System.Text.StringBuilder>> _stringBuilderPool = 
            new Lazy<ObjectPool<System.Text.StringBuilder>>(() => 
                new ObjectPool<System.Text.StringBuilder>(
                    () => new System.Text.StringBuilder(256),
                    sb => sb.Clear(),
                    50));

        private static readonly Lazy<ObjectPool<System.Collections.Generic.List<string>>> _stringListPool = 
            new Lazy<ObjectPool<System.Collections.Generic.List<string>>>(() => 
                new ObjectPool<System.Collections.Generic.List<string>>(
                    () => new System.Collections.Generic.List<string>(),
                    list => list.Clear(),
                    20));

        /// <summary>
        /// Shared StringBuilder pool
        /// </summary>
        public static ObjectPool<System.Text.StringBuilder> StringBuilder => _stringBuilderPool.Value;

        /// <summary>
        /// Shared string list pool
        /// </summary>
        public static ObjectPool<System.Collections.Generic.List<string>> StringList => _stringListPool.Value;

        /// <summary>
        /// Dispose all shared pools
        /// </summary>
        public static void DisposeAll()
        {
            if (_stringBuilderPool.IsValueCreated)
                _stringBuilderPool.Value.Dispose();
            
            if (_stringListPool.IsValueCreated)
                _stringListPool.Value.Dispose();
        }
    }
}
