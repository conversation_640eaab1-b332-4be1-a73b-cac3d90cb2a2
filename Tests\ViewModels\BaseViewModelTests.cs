using System;
using System.ComponentModel;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using IECAD.ViewModels;
using IECAD.Services;
using IECAD.Tests.TestHelpers;

namespace IECAD.Tests.ViewModels
{
    [TestClass]
    public class BaseViewModelTests : TestBase
    {
        private TestViewModel _viewModel;

        [TestInitialize]
        public override void TestInitialize()
        {
            base.TestInitialize();
            _viewModel = new TestViewModel(MockLoggingService.Object, MockErrorHandlingService.Object);
        }

        [TestCleanup]
        public override void TestCleanup()
        {
            _viewModel?.Dispose();
            base.TestCleanup();
        }

        [TestMethod]
        public void IsBusy_WhenSet_ShouldRaisePropertyChanged()
        {
            // Arrange
            bool propertyChangedRaised = false;
            _viewModel.PropertyChanged += (sender, e) =>
            {
                if (e.PropertyName == nameof(_viewModel.IsBusy))
                    propertyChangedRaised = true;
            };

            // Act
            _viewModel.IsBusy = true;

            // Assert
            Assert.IsTrue(propertyChangedRaised);
            Assert.IsTrue(_viewModel.IsBusy);
        }

        [TestMethod]
        public void BusyMessage_WhenSet_ShouldRaisePropertyChanged()
        {
            // Arrange
            bool propertyChangedRaised = false;
            string testMessage = "Test message";
            _viewModel.PropertyChanged += (sender, e) =>
            {
                if (e.PropertyName == nameof(_viewModel.BusyMessage))
                    propertyChangedRaised = true;
            };

            // Act
            _viewModel.BusyMessage = testMessage;

            // Assert
            Assert.IsTrue(propertyChangedRaised);
            Assert.AreEqual(testMessage, _viewModel.BusyMessage);
        }

        [TestMethod]
        public async Task ExecuteAsync_ShouldSetBusyStateCorrectly()
        {
            // Arrange
            bool wasBusy = false;
            var task = Task.Run(async () =>
            {
                await Task.Delay(100);
                wasBusy = _viewModel.IsBusy;
            });

            // Act
            await _viewModel.TestExecuteAsync(() => task);

            // Assert
            Assert.IsTrue(wasBusy); // Should have been busy during execution
            Assert.IsFalse(_viewModel.IsBusy); // Should not be busy after completion
        }

        [TestMethod]
        public async Task ExecuteAsync_WithException_ShouldHandleErrorAndResetBusyState()
        {
            // Arrange
            var testException = CreateTestException();

            // Act
            await _viewModel.TestExecuteAsync(() => throw testException);

            // Assert
            Assert.IsFalse(_viewModel.IsBusy);
            AssertMethodCalled(MockErrorHandlingService, x => x.HandleException(
                It.IsAny<Exception>(), 
                It.IsAny<string>(), 
                It.IsAny<string>(), 
                It.IsAny<bool>()));
        }

        [TestMethod]
        public async Task ExecuteAsync_WithResult_ShouldReturnCorrectValue()
        {
            // Arrange
            const int expectedResult = 42;

            // Act
            var result = await _viewModel.TestExecuteAsync(() => Task.FromResult(expectedResult));

            // Assert
            Assert.AreEqual(expectedResult, result);
            Assert.IsFalse(_viewModel.IsBusy);
        }

        [TestMethod]
        public async Task ExecuteAsync_WithResultAndException_ShouldReturnDefaultValue()
        {
            // Arrange
            var testException = CreateTestException();
            const int defaultValue = -1;

            // Act
            var result = await _viewModel.TestExecuteAsync<int>(() => throw testException, defaultValue);

            // Assert
            Assert.AreEqual(defaultValue, result);
            Assert.IsFalse(_viewModel.IsBusy);
        }

        [TestMethod]
        public void SetProperty_WithDifferentValue_ShouldReturnTrueAndRaisePropertyChanged()
        {
            // Arrange
            bool propertyChangedRaised = false;
            _viewModel.PropertyChanged += (sender, e) =>
            {
                if (e.PropertyName == nameof(_viewModel.TestProperty))
                    propertyChangedRaised = true;
            };

            // Act
            var result = _viewModel.SetTestProperty("New Value");

            // Assert
            Assert.IsTrue(result);
            Assert.IsTrue(propertyChangedRaised);
            Assert.AreEqual("New Value", _viewModel.TestProperty);
        }

        [TestMethod]
        public void SetProperty_WithSameValue_ShouldReturnFalseAndNotRaisePropertyChanged()
        {
            // Arrange
            _viewModel.SetTestProperty("Initial Value");
            bool propertyChangedRaised = false;
            _viewModel.PropertyChanged += (sender, e) =>
            {
                if (e.PropertyName == nameof(_viewModel.TestProperty))
                    propertyChangedRaised = true;
            };

            // Act
            var result = _viewModel.SetTestProperty("Initial Value");

            // Assert
            Assert.IsFalse(result);
            Assert.IsFalse(propertyChangedRaised);
        }

        [TestMethod]
        public void Dispose_ShouldNotThrow()
        {
            // Act & Assert (should not throw)
            _viewModel.Dispose();
        }

        // Test implementation of BaseViewModel
        private class TestViewModel : BaseViewModel
        {
            private string _testProperty = "Initial Value";

            public TestViewModel(ILoggingService loggingService, IErrorHandlingService errorHandlingService)
                : base(loggingService, errorHandlingService)
            {
            }

            public string TestProperty
            {
                get => _testProperty;
                private set => SetProperty(ref _testProperty, value);
            }

            public bool SetTestProperty(string value)
            {
                return SetProperty(ref _testProperty, value);
            }

            public async Task TestExecuteAsync(Func<Task> operation, string busyMessage = "Testing...", string operationName = "Test")
            {
                await ExecuteAsync(operation, busyMessage, operationName);
            }

            public async Task<T> TestExecuteAsync<T>(Func<Task<T>> operation, T defaultValue = default(T), string busyMessage = "Testing...", string operationName = "Test")
            {
                return await ExecuteAsync(operation, defaultValue, busyMessage, operationName);
            }
        }
    }
}
