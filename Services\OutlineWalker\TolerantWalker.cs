using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using System;
using System.Collections.Generic;
using System.Linq;

namespace IECAD.Services.OutlineWalker
{
    /// <summary>
    /// 容差步行者 - 步行者的中心踩在线上，但身体是一个半径为r的圆，能感知到附近的其它线条
    /// 适用场景：处理有微小误差或缝隙的图纸，智能地桥接缝隙，形成逻辑上连续的轮廓
    /// </summary>
    public class TolerantWalker : IOutlineWalker
    {
        private List<LineSegment3d> _segments;
        private List<Point3d> _pathPoints;
        private Point3d _currentPosition;
        private Point3d _startPosition;
        private Vector3d _lastDirection;
        private bool _isCompleted;
        private double _tolerance;

        public bool IsCompleted => _isCompleted;
        public Point3d CurrentPosition => _currentPosition;
        public string StrategyName => "容差步行者";

        public void Initialize(IEnumerable<Entity> entities, double tolerance = 1.0)
        {
            _tolerance = tolerance;
            _segments = ExtractAllLineSegments(entities.ToList());
            _pathPoints = new List<Point3d>();
            _isCompleted = false;

            if (_segments.Count == 0)
            {
                _isCompleted = true;
                return;
            }

            _startPosition = FindStartPoint(_segments);
            _currentPosition = _startPosition;
            _pathPoints.Add(_startPosition);
            
            // Start by moving horizontally to the right.
            _lastDirection = Vector3d.XAxis;
        }

        public WalkerEventResult FindNextEvent()
        {
            if (_isCompleted)
                return new WalkerEventResult { EventType = WalkerEventType.Completed };

            var candidates = FindCandidatePoints(_currentPosition);

            if (candidates.Count == 0)
            {
                _isCompleted = true;
                return new WalkerEventResult { EventType = WalkerEventType.DeadEnd };
            }

            Point3d nextPoint = FindBestTurn(candidates);
            
            if (nextPoint.IsEqualTo(_startPosition, new Tolerance(_tolerance, _tolerance)) && _pathPoints.Count > 1)
            {
                return new WalkerEventResult { EventType = WalkerEventType.OutlineClosed };
            }

            return new WalkerEventResult
            {
                EventType = WalkerEventType.IntersectionFound,
                IntersectionPoint = nextPoint
            };
        }

        public DirectionDecision MakeDecision(WalkerEventResult eventResult)
        {
            var decision = new DirectionDecision();
            if (eventResult.EventType == WalkerEventType.OutlineClosed || eventResult.EventType == WalkerEventType.Completed)
            {
                decision.NextPosition = _startPosition;
                decision.Reason = "轮廓闭合";
            }
            else if (eventResult.EventType == WalkerEventType.DeadEnd)
            {
                decision.RequiresBacktrack = true;
                decision.Reason = "死胡同";
            }
            else
            {
                decision.NextPosition = eventResult.IntersectionPoint;
                decision.Reason = "最右转规则 (容差)";
            }
            return decision;
        }

        public void UpdateState(DirectionDecision decision)
        {
            if (decision.RequiresBacktrack)
            {
                _isCompleted = true; 
                return;
            }

            var nextPosition = decision.NextPosition;
            
            // Find the actual segment endpoint we are jumping to
            var jumpToPoint = FindClosestActualPoint(nextPosition);

            // If we are jumping a gap, the current position needs to move to the start of the new segment.
            // This ensures the path correctly represents the jump.
            if (_currentPosition.DistanceTo(jumpToPoint) > _tolerance)
            {
                 //This is a gap jump, we need to find which point of the segment is the start of the jump
                 var segment = _segments.FirstOrDefault(s => s.StartPoint.IsEqualTo(jumpToPoint) || s.EndPoint.IsEqualTo(jumpToPoint));
                 if(segment != null)
                 {
                    var p1 = segment.StartPoint;
                    var p2 = segment.EndPoint;
                    var jumpStart = p1.DistanceTo(_currentPosition) < p2.DistanceTo(_currentPosition) ? p1 : p2;
                    if(!_currentPosition.IsEqualTo(jumpStart))
                    {
                        _currentPosition = jumpStart;
                        _pathPoints.Add(_currentPosition);
                    }
                 }
            }

            _lastDirection = (nextPosition - _currentPosition).GetNormal();
            _currentPosition = nextPosition;
            _pathPoints.Add(_currentPosition);

            if (nextPosition.IsEqualTo(_startPosition, new Tolerance(_tolerance, _tolerance)))
            {
                _isCompleted = true;
            }
        }

        public List<Point3d> GetResultPath()
        {
            if (_pathPoints.Count > 1 && !_pathPoints.First().IsEqualTo(_pathPoints.Last(), new Tolerance(_tolerance, _tolerance)))
            {
                _pathPoints.Add(_pathPoints.First());
            }
            return new List<Point3d>(_pathPoints);
        }

        private Point3d FindClosestActualPoint(Point3d point)
        {
            return _segments.SelectMany(s => new[] { s.StartPoint, s.EndPoint })
                            .OrderBy(p => p.DistanceTo(point))
                            .First();
        }

        private Point3d FindBestTurn(List<Point3d> candidates)
        {
            double bestAngle = double.MaxValue;
            Point3d bestPoint = candidates[0];

            foreach (var candidate in candidates)
            {
                var vectorToCandidate = (candidate - _currentPosition).GetNormal();
                double angle = _lastDirection.GetAngleTo(vectorToCandidate, Vector3d.ZAxis.Negate());

                if (angle < bestAngle)
                {
                    bestAngle = angle;
                    bestPoint = candidate;
                }
            }
            return bestPoint;
        }

        private List<Point3d> FindCandidatePoints(Point3d currentPoint)
        {
            var candidatePoints = new HashSet<Point3d>();

            foreach (var segment in _segments)
            {
                if (segment.StartPoint.DistanceTo(currentPoint) <= _tolerance)
                {
                    if (!IsReverseDirection(currentPoint, segment.EndPoint))
                        candidatePoints.Add(segment.EndPoint);
                }
                else if (segment.EndPoint.DistanceTo(currentPoint) <= _tolerance)
                {
                    if (!IsReverseDirection(currentPoint, segment.StartPoint))
                        candidatePoints.Add(segment.StartPoint);
                }
            }
            return candidatePoints.ToList();
        }

        private bool IsReverseDirection(Point3d from, Point3d to)
        {
            if (_pathPoints.Count < 2) return false;
            var newDirection = (to - from).GetNormal();
            var prevDirection = (_currentPosition - _pathPoints[_pathPoints.Count - 2]).GetNormal();
            return newDirection.IsParallelTo(-prevDirection, new Tolerance(1e-3, 1e-3));
        }

        private List<LineSegment3d> ExtractAllLineSegments(List<Entity> entities)
        {
            var segments = new List<LineSegment3d>();
            foreach (var entity in entities)
            {
                if (entity is Curve curve)
                {
                    try
                    {
                        var explodedObjects = new DBObjectCollection();
                        curve.Explode(explodedObjects);

                        foreach (DBObject obj in explodedObjects)
                        {
                            if (obj is Curve explodedCurve)
                            {
                                AddCurveSegments(explodedCurve, segments);
                            }
                            obj.Dispose();
                        }
                    }
                    catch
                    {
                        AddCurveSegments(curve, segments);
                    }
                }
            }
            return segments;
        }

        private void AddCurveSegments(Curve curve, List<LineSegment3d> segments)
        {
            if (curve is Line line)
            {
                segments.Add(new LineSegment3d(line.StartPoint, line.EndPoint));
            }
            else if (curve is Polyline polyline)
            {
                for (int i = 0; i < polyline.NumberOfVertices; i++)
                {
                    SegmentType segType = polyline.GetSegmentType(i);
                    if (segType == SegmentType.Line)
                    {
                        var segment = polyline.GetLineSegmentAt(i);
                        if (segment != null)
                            segments.Add(new LineSegment3d(segment.StartPoint, segment.EndPoint));
                    }
                    else if (segType == SegmentType.Arc)
                    {
                        var arcSegment = polyline.GetArcSegmentAt(i);
                        if (arcSegment != null)
                            TessellateArc(arcSegment.Center, arcSegment.Radius, arcSegment.StartAngle, arcSegment.EndAngle, polyline.GetBulgeAt(i) < 0, segments);
                    }
                }
            }
            else if (curve is Arc arc)
            {
                TessellateArc(arc.Center, arc.Radius, arc.StartAngle, arc.EndAngle, arc.Normal.Z < 0, segments);
            }
            else if (curve is Circle circle)
            {
                TessellateCircle(circle.Center, circle.Radius, segments);
            }
        }

        private void TessellateArc(Point3d center, double radius, double startAngle, double endAngle, bool isClockwise, List<LineSegment3d> segments)
        {
            // Normalize angles
            if (isClockwise)
            {
                if (endAngle > startAngle)
                    startAngle += 2 * Math.PI;
            }
            else
            {
                if (endAngle < startAngle)
                    endAngle += 2 * Math.PI;
            }

            double totalAngle = Math.Abs(endAngle - startAngle);
            int numSegments = Math.Max(1, (int)Math.Ceiling(totalAngle / (Math.PI / 18))); // Approx. 10 deg segments

            Point3d lastPoint = Point3d.Origin; // Will be set in first iteration
            for (int i = 0; i <= numSegments; i++)
            {
                double angle = startAngle + (endAngle - startAngle) * i / numSegments;
                Point3d currentPoint = new Point3d(
                    center.X + radius * Math.Cos(angle),
                    center.Y + radius * Math.Sin(angle),
                    center.Z
                );

                if (i > 0)
                {
                    segments.Add(new LineSegment3d(lastPoint, currentPoint));
                }
                lastPoint = currentPoint;
            }
        }

        private void TessellateCircle(Point3d center, double radius, List<LineSegment3d> segments)
        {
            TessellateArc(center, radius, 0, 2 * Math.PI, false, segments);
        }

        private Point3d FindStartPoint(List<LineSegment3d> segments)
        {
            var allPoints = segments.SelectMany(s => new[] { s.StartPoint, s.EndPoint });
            return allPoints.OrderBy(p => p.Y).ThenBy(p => p.X).First();
        }
    }
}
