using Autodesk.AutoCAD.Runtime;
using IECAD.Services;

[assembly: CommandClass(typeof(IECAD.Commands.TestLoggingCommand))]

namespace IECAD.Commands
{
    /// <summary>
    /// Command to test the logging service functionality
    /// </summary>
    public class TestLoggingCommand
    {
        [CommandMethod("TESTLOGGING")]
        public void TestLogging()
        {
            try
            {
                // Use service container to resolve logging service
                var loggingService = ServiceConfiguration.Resolve<ILoggingService>();
                var errorHandlingService = ServiceConfiguration.Resolve<IErrorHandlingService>();

                // Test logging functionality
                loggingService.LogInfo("Test logging command executed", "TestLoggingCommand");
                
                // Get and display log file path
                string logPath = loggingService.GetLogFilePath();
                if (!string.IsNullOrEmpty(logPath))
                {
                    errorHandlingService.ShowInfo($"Logging test completed successfully!\n\nLog file location:\n{logPath}", "Logging Test");
                    loggingService.LogInfo($"Displayed log file path to user: {logPath}", "TestLoggingCommand");
                }
                else
                {
                    errorHandlingService.ShowInfo("Logging test completed, but log file path is not available.", "Logging Test");
                }
            }
            catch (System.Exception ex)
            {
                // Fallback error handling
                System.Windows.MessageBox.Show($"Logging test failed: {ex.Message}", "Error", 
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }
    }
}
