using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using IECAD.Services;
using IECAD.Tests.TestHelpers;

namespace IECAD.Tests.Services
{
    [TestClass]
    public class ErrorHandlingServiceTests : TestBase
    {
        private ErrorHandlingService _service;

        [TestInitialize]
        public override void TestInitialize()
        {
            base.TestInitialize();
            _service = new ErrorHandlingService(MockLoggingService.Object);
        }

        [TestMethod]
        public void HandleException_ShouldLogException()
        {
            // Arrange
            var testException = CreateTestException();
            var userMessage = "Test user message";
            var source = "TestSource";

            // Act
            _service.HandleException(testException, userMessage, source, false);

            // Assert
            AssertMethodCalled(MockLoggingService, x => x.LogException(testException, userMessage, source));
        }

        [TestMethod]
        public void HandleError_ShouldLogError()
        {
            // Arrange
            var errorMessage = "Test error message";
            var source = "TestSource";

            // Act
            _service.HandleError(errorMessage, source, false);

            // Assert
            AssertMethodCalled(MockLoggingService, x => x.LogError(errorMessage, source));
        }

        [TestMethod]
        public void HandleWarning_ShouldLogWarning()
        {
            // Arrange
            var warningMessage = "Test warning message";
            var source = "TestSource";

            // Act
            _service.HandleWarning(warningMessage, source, false);

            // Assert
            AssertMethodCalled(MockLoggingService, x => x.LogWarning(warningMessage, source));
        }

        [TestMethod]
        public void HandleException_WithNullException_ShouldNotThrow()
        {
            // Act & Assert (should not throw)
            _service.HandleException(null, "Test message", "TestSource", false);
        }

        [TestMethod]
        public void HandleError_WithNullMessage_ShouldNotThrow()
        {
            // Act & Assert (should not throw)
            _service.HandleError(null, "TestSource", false);
        }

        [TestMethod]
        public void HandleWarning_WithNullMessage_ShouldNotThrow()
        {
            // Act & Assert (should not throw)
            _service.HandleWarning(null, "TestSource", false);
        }

        [TestMethod]
        public void ShowInfo_ShouldLogMessage()
        {
            // Arrange
            var message = "Test info message";

            // Act
            _service.ShowInfo(message);

            // Assert
            AssertMethodCalled(MockLoggingService, x => x.LogInfo(It.IsAny<string>()));
        }

        [TestMethod]
        public void ShowSuccess_ShouldLogMessage()
        {
            // Arrange
            var message = "Test success message";

            // Act
            _service.ShowSuccess(message);

            // Assert
            AssertMethodCalled(MockLoggingService, x => x.LogInfo(It.IsAny<string>()));
        }
    }
}
