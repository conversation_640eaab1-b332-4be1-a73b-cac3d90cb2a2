using System;

namespace IECAD.Services
{
    /// <summary>
    /// Interface for user settings service to provide persistent storage of user preferences
    /// </summary>
    public interface IUserSettingsService
    {
        /// <summary>
        /// Get a double value from settings
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <param name="defaultValue">Default value if setting doesn't exist</param>
        /// <returns>The setting value or default value</returns>
        double GetDouble(string key, double defaultValue = 0.0);

        /// <summary>
        /// Set a double value in settings
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <param name="value">The value to store</param>
        /// <returns>True if successfully saved</returns>
        bool SetDouble(string key, double value);

        /// <summary>
        /// Get a string value from settings
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <param name="defaultValue">Default value if setting doesn't exist</param>
        /// <returns>The setting value or default value</returns>
        string GetString(string key, string defaultValue = "");

        /// <summary>
        /// Set a string value in settings
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <param name="value">The value to store</param>
        /// <returns>True if successfully saved</returns>
        bool SetString(string key, string value);

        /// <summary>
        /// Get an integer value from settings
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <param name="defaultValue">Default value if setting doesn't exist</param>
        /// <returns>The setting value or default value</returns>
        int GetInt(string key, int defaultValue = 0);

        /// <summary>
        /// Set an integer value in settings
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <param name="value">The value to store</param>
        /// <returns>True if successfully saved</returns>
        bool SetInt(string key, int value);

        /// <summary>
        /// Get a boolean value from settings
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <param name="defaultValue">Default value if setting doesn't exist</param>
        /// <returns>The setting value or default value</returns>
        bool GetBool(string key, bool defaultValue = false);

        /// <summary>
        /// Set a boolean value in settings
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <param name="value">The value to store</param>
        /// <returns>True if successfully saved</returns>
        bool SetBool(string key, bool value);

        /// <summary>
        /// Check if a setting exists
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <returns>True if the setting exists</returns>
        bool HasSetting(string key);

        /// <summary>
        /// Remove a setting
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <returns>True if successfully removed</returns>
        bool RemoveSetting(string key);

        /// <summary>
        /// Clear all settings
        /// </summary>
        /// <returns>True if successfully cleared</returns>
        bool ClearAllSettings();
    }
}
