using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.Windows;
using Autodesk.AutoCAD.GraphicsInterface;
using Autodesk.AutoCAD.Geometry;
using AcadApp = Autodesk.AutoCAD.ApplicationServices.Application;
using System.Drawing;

namespace IECAD.Views
{
    public class BatchEditTextPalette : UserControl
    {
        private DataGridView dgv;
        private Button btnApply;
        private Button btnClose;
        private ObjectId[] _ids;
        private readonly List<Entity> _highlightTransients = new List<Entity>();
        public PaletteSet ParentPaletteSet { get; set; }

        public BatchEditTextPalette(ObjectId[] ids, List<string> originals)
        {
            _ids = ids;
            InitializeComponent();
            PopulateGrid(originals);
            this.Disposed += (s, e) => ClearHighlight();
        }

        private void InitializeComponent()
        {
            dgv = new DataGridView
            {
                Dock = DockStyle.Top,
                Height = 300,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                AllowUserToAddRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                RowHeadersVisible = false,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.EnableResizing,
                ColumnHeadersHeight = 36
            };
            dgv.Columns.Add("Original", "原文字");
            dgv.Columns.Add("New", "新文字");
            dgv.Columns[0].ReadOnly = true;
            dgv.SelectionChanged += Dgv_SelectionChanged;

            btnApply = new Button
            {
                Text = "应用",
                Dock = DockStyle.Bottom,
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.LightGray
            };
            btnApply.Click += BtnApply_Click;

            btnClose = new Button
            {
                Text = "关闭",
                Dock = DockStyle.Bottom,
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.LightGray
            };
            btnClose.Click += (s, e) =>
            {
                ClearHighlight();
                if (ParentPaletteSet != null)
                    ParentPaletteSet.Visible = false;
            };

            Controls.Add(btnApply);
            Controls.Add(btnClose);
            Controls.Add(dgv);
        }

        private void PopulateGrid(List<string> originals)
        {
            dgv.Rows.Clear();
            foreach (var text in originals)
                dgv.Rows.Add(text, text);
        }

        private void Dgv_SelectionChanged(object sender, EventArgs e)
        {
            if (dgv.CurrentCell == null || _ids == null || _ids.Length == 0) return;
            int idx = dgv.CurrentCell.RowIndex;
            if (idx < 0 || idx >= _ids.Length) return;
            var doc = AcadApp.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;
            ed.SetImpliedSelection(new ObjectId[] { _ids[idx] });
            foreach (var trEnt in _highlightTransients)
                TransientManager.CurrentTransientManager.EraseTransient(trEnt, new IntegerCollection());
            _highlightTransients.Clear();
            using (var tr = doc.TransactionManager.StartTransaction())
            {
                var ent = tr.GetObject(_ids[idx], OpenMode.ForRead) as Entity;
                if (ent != null)
                {
                    var ext = ent.GeometricExtents;
                    var box = new Autodesk.AutoCAD.DatabaseServices.Polyline(4)
                    {
                        ColorIndex = 1,
                        Closed = true
                    };
                    box.AddVertexAt(0, new Point2d(ext.MinPoint.X, ext.MinPoint.Y), 0, 0, 0);
                    box.AddVertexAt(1, new Point2d(ext.MaxPoint.X, ext.MinPoint.Y), 0, 0, 0);
                    box.AddVertexAt(2, new Point2d(ext.MaxPoint.X, ext.MaxPoint.Y), 0, 0, 0);
                    box.AddVertexAt(3, new Point2d(ext.MinPoint.X, ext.MaxPoint.Y), 0, 0, 0);
                    box.SetDatabaseDefaults();
                    TransientManager.CurrentTransientManager.AddTransient(box, TransientDrawingMode.DirectShortTerm, 128, new IntegerCollection());
                    _highlightTransients.Add(box);
                }
                tr.Commit();
            }
            ed.UpdateScreen();
        }

        public void UpdateData(ObjectId[] ids, List<string> originals)
        {
            ClearHighlight();
            _ids = ids;
            PopulateGrid(originals);
            dgv.ClearSelection();
        }

        public void ClearHighlight()
        {
            var doc = AcadApp.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;
            ed.SetImpliedSelection(new ObjectId[0]);
            foreach (var trEnt in _highlightTransients)
                TransientManager.CurrentTransientManager.EraseTransient(trEnt, new IntegerCollection());
            _highlightTransients.Clear();
        }

        private void BtnApply_Click(object sender, EventArgs e)
        {
            var doc = AcadApp.DocumentManager.MdiActiveDocument;
            using (var lockDoc = doc.LockDocument())
            using (var tr = doc.TransactionManager.StartTransaction())
            {
                for (int i = 0; i < _ids.Length; i++)
                {
                    var ent = tr.GetObject(_ids[i], OpenMode.ForWrite) as Entity;
                    if (ent is MText mtxt)
                        mtxt.Contents = dgv.Rows[i].Cells[1].Value?.ToString() ?? string.Empty;
                    else if (ent is DBText txt)
                        txt.TextString = dgv.Rows[i].Cells[1].Value?.ToString() ?? string.Empty;
                }
                tr.Commit();
            }
            AcadApp.DocumentManager.MdiActiveDocument.Editor.UpdateScreen();
        }
    }
}
