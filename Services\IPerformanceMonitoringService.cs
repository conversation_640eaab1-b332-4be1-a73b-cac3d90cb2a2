using System;
using System.Collections.Generic;

namespace IECAD.Services
{
    /// <summary>
    /// Interface for performance monitoring service
    /// </summary>
    public interface IPerformanceMonitoringService
    {
        /// <summary>
        /// Start measuring performance for an operation
        /// </summary>
        /// <param name="operationName">Name of the operation</param>
        /// <returns>Performance measurement token</returns>
        IDisposable StartMeasurement(string operationName);

        /// <summary>
        /// Record a performance measurement
        /// </summary>
        /// <param name="operationName">Name of the operation</param>
        /// <param name="duration">Duration of the operation</param>
        void RecordMeasurement(string operationName, TimeSpan duration);

        /// <summary>
        /// Get performance statistics for an operation
        /// </summary>
        /// <param name="operationName">Name of the operation</param>
        /// <returns>Performance statistics or null if not found</returns>
        PerformanceStatistics GetStatistics(string operationName);

        /// <summary>
        /// Get all performance statistics
        /// </summary>
        /// <returns>Dictionary of operation names and their statistics</returns>
        Dictionary<string, PerformanceStatistics> GetAllStatistics();

        /// <summary>
        /// Clear all performance data
        /// </summary>
        void Clear();

        /// <summary>
        /// Clear performance data for a specific operation
        /// </summary>
        /// <param name="operationName">Name of the operation</param>
        void Clear(string operationName);
    }

    /// <summary>
    /// Performance statistics for an operation
    /// </summary>
    public class PerformanceStatistics
    {
        public string OperationName { get; set; }
        public int CallCount { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public TimeSpan AverageDuration { get; set; }
        public TimeSpan MinDuration { get; set; }
        public TimeSpan MaxDuration { get; set; }
        public DateTime FirstCall { get; set; }
        public DateTime LastCall { get; set; }
    }
}
