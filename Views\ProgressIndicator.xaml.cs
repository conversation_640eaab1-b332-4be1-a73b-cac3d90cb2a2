using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;

namespace IECAD.Views
{
    /// <summary>
    /// Progress indicator user control for showing busy state
    /// </summary>
    public partial class ProgressIndicator : UserControl
    {
        public static readonly DependencyProperty MessageProperty =
            DependencyProperty.Register("Message", typeof(string), typeof(ProgressIndicator),
                new PropertyMetadata("Processing..."));

        public string Message
        {
            get { return (string)GetValue(MessageProperty); }
            set { SetValue(MessageProperty, value); }
        }

        public ProgressIndicator()
        {
            InitializeComponent();
            Loaded += ProgressIndicator_Loaded;
        }

        private void ProgressIndicator_Loaded(object sender, RoutedEventArgs e)
        {
            // Start the spinner animation when the control is loaded
            var storyboard = (Storyboard)FindResource("SpinnerAnimation");
            storyboard?.Begin();
        }
    }
}
