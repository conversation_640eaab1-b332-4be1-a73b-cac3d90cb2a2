using System;

namespace IECAD.Constants
{
    /// <summary>
    /// Application-wide constants and configuration values
    /// </summary>
    public static class ApplicationConstants
    {
        #region Equipment Creation Constants
        /// <summary>
        /// Default vertical spacing between equipment blocks
        /// </summary>
        public const double EQUIPMENT_VERTICAL_SPACING = 500.0;

        /// <summary>
        /// Default horizontal spacing between equipment blocks
        /// </summary>
        public const double EQUIPMENT_HORIZONTAL_SPACING = 800.0;

        /// <summary>
        /// Default text height for equipment labels
        /// </summary>
        public const double DEFAULT_TEXT_HEIGHT = 350.0;

        /// <summary>
        /// Default spacing distance for block arrangement
        /// </summary>
        public const double DEFAULT_BLOCK_ARRANGEMENT_SPACING = 1000.0;
        #endregion

        #region UI Constants
        /// <summary>
        /// Application title displayed in the sidebar
        /// </summary>
        public const string APPLICATION_TITLE = "工艺设计工具箱";

        /// <summary>
        /// Sidebar palette name
        /// </summary>
        public const string SIDEBAR_PALETTE_NAME = "中电四设计";

        /// <summary>
        /// Minimum sidebar width
        /// </summary>
        public const int SIDEBAR_MIN_WIDTH = 300;

        /// <summary>
        /// Minimum sidebar height
        /// </summary>
        public const int SIDEBAR_MIN_HEIGHT = 300;
        #endregion

        #region AutoCAD Commands
        /// <summary>
        /// Command to show the main sidebar
        /// </summary>
        public const string COMMAND_SHOW_SIDEBAR = "SHOWSIDEBAR";

        /// <summary>
        /// Command to create equipment blocks
        /// </summary>
        public const string COMMAND_CREATE_EQUIPMENT = "CREATEEQUIPMENT";

        /// <summary>
        /// Command to import room plans
        /// </summary>
        public const string COMMAND_IMPORT_ROOM_PLAN = "IMPORTROOMPLAN";

        /// <summary>
        /// Command to extract outlines
        /// </summary>
        public const string COMMAND_EXTRACT_OUTLINE = "EXTRACTOUTLINE";

        /// <summary>
        /// Command to align blocks
        /// </summary>
        public const string COMMAND_ALIGN_BLOCK = "AlignBlockToLine";

        /// <summary>
        /// Command to batch edit text
        /// </summary>
        public const string COMMAND_BATCH_EDIT_TEXT = "BatchEditTexts";

        /// <summary>
        /// Command to flatten blocks
        /// </summary>
        public const string COMMAND_FLATTEN_BLOCKS = "FLATTENBLOCKS";

        /// <summary>
        /// Command to arrange blocks
        /// </summary>
        public const string COMMAND_ARRANGE_BLOCKS = "ARRANGEBLOCKS";
        #endregion

        #region File Extensions
        /// <summary>
        /// Excel file extensions filter for file dialogs
        /// </summary>
        public const string EXCEL_FILE_FILTER = "Excel Files|*.xlsx;*.xls";

        /// <summary>
        /// AutoCAD drawing file extension
        /// </summary>
        public const string AUTOCAD_FILE_EXTENSION = ".dwg";
        #endregion

        #region Error Messages
        /// <summary>
        /// Generic error message prefix
        /// </summary>
        public const string ERROR_PREFIX = "An error occurred: ";

        /// <summary>
        /// File not selected error message
        /// </summary>
        public const string ERROR_NO_FILE_SELECTED = "No file selected.";

        /// <summary>
        /// No valid data found error message
        /// </summary>
        public const string ERROR_NO_VALID_DATA = "No valid equipment data found.";

        /// <summary>
        /// Plugin initialization error message
        /// </summary>
        public const string ERROR_PLUGIN_INIT = "Plugin initialization failed: ";

        /// <summary>
        /// Command execution error message
        /// </summary>
        public const string ERROR_COMMAND_EXECUTION = "Failed to execute command: ";
        #endregion

        #region Resource Names
        /// <summary>
        /// Embedded logo resource name
        /// </summary>
        public const string LOGO_RESOURCE_NAME = "IECAD.Resources.logo.png";

        /// <summary>
        /// Embedded room plan resource name
        /// </summary>
        public const string ROOM_PLAN_RESOURCE_NAME = "IECAD.Resources.RoomPlan.dwg";
        #endregion

        #region Settings Keys
        /// <summary>
        /// Settings key for block arrangement spacing distance
        /// </summary>
        public const string SETTING_BLOCK_ARRANGEMENT_SPACING = "BlockArrangementSpacing";
        #endregion
    }
}
