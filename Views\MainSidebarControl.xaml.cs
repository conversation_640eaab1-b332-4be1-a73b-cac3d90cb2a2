using Autodesk.AutoCAD.ApplicationServices;
using System.Windows;
using System.Windows.Controls;
using IECAD.Services;
using IECAD.Services.OutlineWalker;
using IECAD.ViewModels;
using IECAD.Helpers;
using IECAD.Constants;
using System;

namespace IECAD.Views
{
    public partial class MainSidebarControl : UserControl
    {
        private IAutoCADService _autoCADService;
        private MainSidebarViewModel _viewModel;

        public MainSidebarControl()
        {
            InitializeComponent();

            // Initialize AutoCAD service using service container and bind data context
            _autoCADService = ServiceConfiguration.Resolve<IAutoCADService>();
            _viewModel = new MainSidebarViewModel(_autoCADService);
            DataContext = _viewModel;

            LoadLogo();
        }

        private void LoadLogo()
        {
            LogoImage.Source = EmbeddedImageHelper.LoadImage(ApplicationConstants.LOGO_RESOURCE_NAME);
        }

        private void FlattenBlocks_Click(object sender, RoutedEventArgs e)
        {
            AutoCADService.ExecuteAutoCADCommand(ApplicationConstants.COMMAND_FLATTEN_BLOCKS);
        }

        private void CreateBlockFromXLS_Click(object sender, RoutedEventArgs e)
        {
            AutoCADService.ExecuteAutoCADCommand(ApplicationConstants.COMMAND_CREATE_EQUIPMENT);
        }

        private void ImportRoomPlan_Click(object sender, RoutedEventArgs e)
        {
            AutoCADService.ExecuteAutoCADCommand(ApplicationConstants.COMMAND_IMPORT_ROOM_PLAN);
        }

        private void ExtractOutline_Click(object sender, RoutedEventArgs e)
        {
            AutoCADService.ExecuteAutoCADCommand(ApplicationConstants.COMMAND_EXTRACT_OUTLINE);
        }

        /// <summary>
        /// 使用选定策略提取轮廓的新方法
        /// </summary>
        private void ExtractOutlineWithStrategy_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 获取选定的策略
                WalkerStrategy selectedStrategy = GetSelectedStrategy();
                
                // 获取容差参数
                double tolerance = GetToleranceValue();
                
                // 创建轮廓提取器并执行
                var extractor = new OutlineExtractor();
                extractor.ExtractOutlineWithStrategy(selectedStrategy, tolerance);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"轮廓提取出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// 获取用户选定的步行者策略
        /// </summary>
        private WalkerStrategy GetSelectedStrategy()
        {
            if (PreciseStrategyRadio.IsChecked == true)
                return WalkerStrategy.Precise;
            else if (TolerantStrategyRadio.IsChecked == true)
                return WalkerStrategy.Tolerant;
            else if (MorphologicalStrategyRadio.IsChecked == true)
                return WalkerStrategy.Morphological;
            else if (GridStrategyRadio.IsChecked == true)
                return WalkerStrategy.Grid;
            else
                return WalkerStrategy.Precise; // 默认策略
        }
        
        /// <summary>
        /// 获取容差参数值
        /// </summary>
        private double GetToleranceValue()
        {
            if (double.TryParse(ToleranceTextBox.Text, out double tolerance))
            {
                return Math.Max(0.1, tolerance); // 最小容差0.1
            }
            return 1.0; // 默认容差
        }

        private void AlignBlock_Click(object sender, RoutedEventArgs e)
        {
            AutoCADService.ExecuteAutoCADCommand(ApplicationConstants.COMMAND_ALIGN_BLOCK);
        }

        private void ArrangeBlocks_Click(object sender, RoutedEventArgs e)
        {
            AutoCADService.ExecuteAutoCADCommand(ApplicationConstants.COMMAND_ARRANGE_BLOCKS);
        }

        private void BatchEditText_Click(object sender, RoutedEventArgs e)
        {
            AutoCADService.ExecuteAutoCADCommand(ApplicationConstants.COMMAND_BATCH_EDIT_TEXT);
        }
    }
}
