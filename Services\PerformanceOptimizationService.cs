using System;
using System.Collections.Generic;
using System.Linq;
using IECAD.Helpers;

namespace IECAD.Services
{
    /// <summary>
    /// Performance optimization service implementation
    /// </summary>
    public class PerformanceOptimizationService : IPerformanceOptimizationService
    {
        private readonly ILoggingService _loggingService;
        private readonly IPerformanceMonitoringService _performanceMonitoring;
        private readonly IAutoCADService _autoCADService;
        private bool _monitoringEnabled = true;
        private DateTime _lastOptimization = DateTime.MinValue;

        public PerformanceOptimizationService(ILoggingService loggingService, 
            IPerformanceMonitoringService performanceMonitoring,
            IAutoCADService autoCADService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _performanceMonitoring = performanceMonitoring ?? throw new ArgumentNullException(nameof(performanceMonitoring));
            _autoCADService = autoCADService ?? throw new ArgumentNullException(nameof(autoCADService));
        }

        public void OptimizePerformance()
        {
            try
            {
                _loggingService.LogInfo("Starting performance optimization", "PerformanceOptimizationService");

                // Optimize memory settings
                MemoryManager.OptimizeMemorySettings();

                // Clear caches if they're getting large
                if (_autoCADService is AutoCADService service)
                {
                    var cacheStats = service.GetCacheStatistics();
                    if (cacheStats.TotalEntries > 1000 || cacheStats.ExpiredEntries > 100)
                    {
                        service.ClearCache();
                        _loggingService.LogInfo("Cleared AutoCAD service cache due to size", "PerformanceOptimizationService");
                    }
                }

                // Force garbage collection if memory usage is high
                var memoryInfo = MemoryManager.GetMemoryUsage();
                if (memoryInfo.WorkingSet > 500 * 1024 * 1024) // 500MB
                {
                    MemoryManager.ForceGarbageCollection();
                    _loggingService.LogInfo("Performed garbage collection due to high memory usage", "PerformanceOptimizationService");
                }

                _lastOptimization = DateTime.Now;
                _loggingService.LogInfo("Performance optimization completed", "PerformanceOptimizationService");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Error during performance optimization: {ex.Message}", "PerformanceOptimizationService");
            }
        }

        public void OptimizeMemory()
        {
            try
            {
                _loggingService.LogInfo("Starting memory optimization", "PerformanceOptimizationService");

                var beforeMemory = MemoryManager.GetMemoryUsage();
                
                // Clear object pools
                ObjectPools.StringBuilder.Clear();
                ObjectPools.StringList.Clear();

                // Force garbage collection
                MemoryManager.ForceGarbageCollection(compactHeap: true);

                var afterMemory = MemoryManager.GetMemoryUsage();
                var freedMemory = (beforeMemory.WorkingSet - afterMemory.WorkingSet) / 1024 / 1024;

                _loggingService.LogInfo($"Memory optimization completed. Freed {freedMemory} MB", "PerformanceOptimizationService");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Error during memory optimization: {ex.Message}", "PerformanceOptimizationService");
            }
        }

        public List<PerformanceRecommendation> GetPerformanceRecommendations()
        {
            var recommendations = new List<PerformanceRecommendation>();

            try
            {
                var metrics = GetPerformanceMetrics();
                var allStats = _performanceMonitoring.GetAllStatistics();

                // Memory recommendations
                if (metrics.MemoryUsageMB > 500)
                {
                    recommendations.Add(new PerformanceRecommendation
                    {
                        Title = "High Memory Usage",
                        Description = $"Application is using {metrics.MemoryUsageMB} MB of memory. Consider optimizing memory usage.",
                        Impact = metrics.MemoryUsageMB > 1000 ? PerformanceImpact.Critical : PerformanceImpact.High,
                        Category = "Memory",
                        ApplyAction = OptimizeMemory
                    });
                }

                // Cache recommendations
                if (_autoCADService is AutoCADService service)
                {
                    var cacheStats = service.GetCacheStatistics();
                    if (cacheStats.ExpiredEntries > cacheStats.ActiveEntries)
                    {
                        recommendations.Add(new PerformanceRecommendation
                        {
                            Title = "Cache Cleanup Needed",
                            Description = "Cache contains many expired entries. Clearing cache will improve performance.",
                            Impact = PerformanceImpact.Medium,
                            Category = "Cache",
                            ApplyAction = () => service.ClearCache()
                        });
                    }
                }

                // Performance monitoring recommendations
                var slowOperations = allStats.Where(kvp => kvp.Value.AverageDuration.TotalSeconds > 2).ToList();
                if (slowOperations.Any())
                {
                    recommendations.Add(new PerformanceRecommendation
                    {
                        Title = "Slow Operations Detected",
                        Description = $"Found {slowOperations.Count} operations with average duration > 2 seconds.",
                        Impact = PerformanceImpact.Medium,
                        Category = "Performance",
                        ApplyAction = null // No automatic fix for this
                    });
                }

                // GC recommendations
                if (metrics.GCCollections > 100)
                {
                    recommendations.Add(new PerformanceRecommendation
                    {
                        Title = "Frequent Garbage Collection",
                        Description = "High number of garbage collections detected. Consider optimizing object allocation.",
                        Impact = PerformanceImpact.Medium,
                        Category = "Memory",
                        ApplyAction = OptimizeMemory
                    });
                }

                // Last optimization recommendation
                if ((DateTime.Now - _lastOptimization).TotalHours > 2)
                {
                    recommendations.Add(new PerformanceRecommendation
                    {
                        Title = "Optimization Overdue",
                        Description = "Performance optimization hasn't been run recently.",
                        Impact = PerformanceImpact.Low,
                        Category = "Maintenance",
                        ApplyAction = OptimizePerformance
                    });
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Error generating performance recommendations: {ex.Message}", "PerformanceOptimizationService");
            }

            return recommendations;
        }

        public void ApplyAutomaticOptimizations()
        {
            try
            {
                var recommendations = GetPerformanceRecommendations();
                var autoApplicable = recommendations.Where(r => r.ApplyAction != null && 
                    (r.Impact == PerformanceImpact.High || r.Impact == PerformanceImpact.Critical)).ToList();

                foreach (var recommendation in autoApplicable)
                {
                    try
                    {
                        _loggingService.LogInfo($"Applying automatic optimization: {recommendation.Title}", "PerformanceOptimizationService");
                        recommendation.ApplyAction();
                    }
                    catch (Exception ex)
                    {
                        _loggingService.LogError($"Error applying optimization '{recommendation.Title}': {ex.Message}", "PerformanceOptimizationService");
                    }
                }

                if (autoApplicable.Any())
                {
                    _loggingService.LogInfo($"Applied {autoApplicable.Count} automatic optimizations", "PerformanceOptimizationService");
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Error applying automatic optimizations: {ex.Message}", "PerformanceOptimizationService");
            }
        }

        public PerformanceMetrics GetPerformanceMetrics()
        {
            try
            {
                var memoryInfo = MemoryManager.GetMemoryUsage();
                var allStats = _performanceMonitoring.GetAllStatistics();

                var metrics = new PerformanceMetrics
                {
                    MemoryUsageMB = memoryInfo.WorkingSet / 1024 / 1024,
                    GCCollections = memoryInfo.Gen0Collections + memoryInfo.Gen1Collections + memoryInfo.Gen2Collections,
                    LastOptimization = _lastOptimization
                };

                if (allStats.Any())
                {
                    metrics.AverageResponseTime = TimeSpan.FromTicks((long)allStats.Values.Average(s => s.AverageDuration.Ticks));
                    metrics.OperationTimes = allStats.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.AverageDuration);
                }

                // Calculate cache hit rate if available
                if (_autoCADService is AutoCADService service)
                {
                    var cacheStats = service.GetCacheStatistics();
                    if (cacheStats.TotalEntries > 0)
                    {
                        metrics.CacheHitRate = (int)((double)cacheStats.ActiveEntries / cacheStats.TotalEntries * 100);
                    }
                }

                return metrics;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Error getting performance metrics: {ex.Message}", "PerformanceOptimizationService");
                return new PerformanceMetrics();
            }
        }

        public void SetPerformanceMonitoring(bool enabled)
        {
            _monitoringEnabled = enabled;
            _loggingService.LogInfo($"Performance monitoring {(enabled ? "enabled" : "disabled")}", "PerformanceOptimizationService");
        }
    }
}
