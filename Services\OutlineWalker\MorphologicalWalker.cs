using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using System;
using System.Collections.Generic;
using System.Linq;

namespace IECAD.Services.OutlineWalker
{
    /// <summary>
    /// 形态学步行者 - 半径为r的圆盘，其边缘沿着原始几何的边界滚动，追踪圆心的轨迹
    /// 适用场景：形态学膨胀/缓冲分析，生成泛化的、平滑的轮廓，用于GIS可达性分析、机器人避障等
    /// </summary>
    public class MorphologicalWalker : IOutlineWalker
    {
        private List<Entity> _entities;
        private List<Point3d> _pathPoints;
        private Point3d _currentPosition;
        private Point3d _startPosition;
        private WalkDirection _currentDirection;
        private WalkDirection _fromDirection;
        private bool _isCompleted;
        private double _radius; // 圆盘半径
        private List<MorphologySegment> _morphologySegments;
        private int _currentSegmentIndex;
        private double _currentSegmentParameter;
        private Stack<(Point3d position, int segmentIndex, double parameter)> _backtrackStack;

        public bool IsCompleted => _isCompleted;
        public Point3d CurrentPosition => _currentPosition;
        public string StrategyName => "形态学步行者";

        /// <summary>
        /// 形态学路径段
        /// </summary>
        private class MorphologySegment
        {
            public enum SegmentType
            {
                OffsetLine,     // 偏移直线
                TransitionArc   // 过渡圆弧
            }

            public SegmentType Type { get; set; }
            public Point3d StartPoint { get; set; }
            public Point3d EndPoint { get; set; }
            public Entity OriginalEntity { get; set; }
            public double StartParameter { get; set; }
            public double EndParameter { get; set; }
            public Point3d? CenterPoint { get; set; } // 用于圆弧段
            public double Radius { get; set; } // 用于圆弧段
            public bool IsClockwise { get; set; } // 圆弧方向

            /// <summary>
            /// 根据参数获取段上的点
            /// </summary>
            public Point3d GetPointAtParameter(double parameter)
            {
                parameter = Math.Max(0, Math.Min(1, parameter));

                if (Type == SegmentType.OffsetLine)
                {
                    return StartPoint + (EndPoint - StartPoint) * parameter;
                }
                else // TransitionArc
                {
                    if (!CenterPoint.HasValue) return StartPoint;

                    var startVector = StartPoint - CenterPoint.Value;
                    var endVector = EndPoint - CenterPoint.Value;
                    
                    var startAngle = Math.Atan2(startVector.Y, startVector.X);
                    var endAngle = Math.Atan2(endVector.Y, endVector.X);

                    // 处理角度跨越
                    if (!IsClockwise)
                    {
                        if (endAngle < startAngle) endAngle += 2 * Math.PI;
                    }
                    else
                    {
                        if (startAngle < endAngle) startAngle += 2 * Math.PI;
                    }

                    var currentAngle = startAngle + (endAngle - startAngle) * parameter;
                    
                    return CenterPoint.Value + new Vector3d(
                        Radius * Math.Cos(currentAngle),
                        Radius * Math.Sin(currentAngle),
                        0
                    );
                }
            }

            /// <summary>
            /// 获取段上指定参数处的切线方向
            /// </summary>
            public Vector3d GetTangentAtParameter(double parameter)
            {
                if (Type == SegmentType.OffsetLine)
                {
                    return (EndPoint - StartPoint).GetNormal();
                }
                else // TransitionArc
                {
                    if (!CenterPoint.HasValue) return Vector3d.XAxis;

                    var point = GetPointAtParameter(parameter);
                    var radiusVector = point - CenterPoint.Value;
                    
                    // 切线方向垂直于半径方向
                    var tangent = new Vector3d(-radiusVector.Y, radiusVector.X, 0).GetNormal();
                    return IsClockwise ? -tangent : tangent;
                }
            }
        }

        /// <summary>
        /// 初始化形态学步行者
        /// </summary>
        public void Initialize(IEnumerable<Entity> entities, double tolerance = 1.0)
        {
            _entities = entities.ToList();
            _radius = tolerance; // 对于形态学步行者，tolerance参数用作圆盘半径
            _pathPoints = new List<Point3d>();
            _backtrackStack = new Stack<(Point3d, int, double)>();
            _isCompleted = false;
            _currentSegmentIndex = 0;
            _currentSegmentParameter = 0;

            // 生成形态学路径段
            _morphologySegments = GenerateMorphologySegments(_entities, _radius);

            if (_morphologySegments.Count == 0)
            {
                _isCompleted = true;
                return;
            }

            // 找到虚拟形状的"Y最小，再X最小"点
            _startPosition = FindMorphologyStartPoint();
            _currentPosition = _startPosition;
            _pathPoints.Add(_startPosition);

            // 找到起始段
            FindStartingSegment();

            // 初始方向
            _currentDirection = WalkDirection.East;
            _fromDirection = WalkDirection.West;
        }

        /// <summary>
        /// 生成形态学路径段（正确的形态学膨胀算法）
        /// </summary>
        private List<MorphologySegment> GenerateMorphologySegments(List<Entity> entities, double radius)
        {
            // 第一步：提取所有原始几何的顶点和边
            var vertices = ExtractVertices(entities);
            var edges = ExtractEdges(entities);
            
            // 第二步：计算形态学膨胀的轮廓
            var morphologyPoints = ComputeMorphologicalDilation(vertices, edges, radius);
            
            // 第三步：将点序列转换为段
            return ConvertPointsToSegments(morphologyPoints, radius);
        }

        /// <summary>
        /// 提取所有几何实体的顶点
        /// </summary>
        private List<Point3d> ExtractVertices(List<Entity> entities)
        {
            var vertices = new List<Point3d>();
            
            foreach (var entity in entities)
            {
                switch (entity)
                {
                    case Line line:
                        vertices.Add(line.StartPoint);
                        vertices.Add(line.EndPoint);
                        break;
                        
                    case Polyline polyline:
                        for (int i = 0; i < polyline.NumberOfVertices; i++)
                        {
                            var pt = polyline.GetPoint2dAt(i);
                            vertices.Add(new Point3d(pt.X, pt.Y, 0));
                        }
                        break;
                        
                    case Circle circle:
                        // 圆形：采样关键点
                        for (int i = 0; i < 16; i++)
                        {
                            var angle = i * Math.PI * 2 / 16;
                            var x = circle.Center.X + circle.Radius * Math.Cos(angle);
                            var y = circle.Center.Y + circle.Radius * Math.Sin(angle);
                            vertices.Add(new Point3d(x, y, 0));
                        }
                        break;
                }
            }
            
            return vertices;
        }
        
        /// <summary>
        /// 提取所有几何实体的边
        /// </summary>
        private List<(Point3d start, Point3d end)> ExtractEdges(List<Entity> entities)
        {
            var edges = new List<(Point3d, Point3d)>();
            
            foreach (var entity in entities)
            {
                switch (entity)
                {
                    case Line line:
                        edges.Add((line.StartPoint, line.EndPoint));
                        break;
                        
                    case Polyline polyline:
                        for (int i = 0; i < polyline.NumberOfVertices - 1; i++)
                        {
                            var start = new Point3d(polyline.GetPoint2dAt(i).X, polyline.GetPoint2dAt(i).Y, 0);
                            var end = new Point3d(polyline.GetPoint2dAt(i + 1).X, polyline.GetPoint2dAt(i + 1).Y, 0);
                            edges.Add((start, end));
                        }
                        // 如果是闭合多段线，添加最后一条边
                        if (polyline.Closed)
                        {
                            var start = new Point3d(polyline.GetPoint2dAt(polyline.NumberOfVertices - 1).X, polyline.GetPoint2dAt(polyline.NumberOfVertices - 1).Y, 0);
                            var end = new Point3d(polyline.GetPoint2dAt(0).X, polyline.GetPoint2dAt(0).Y, 0);
                            edges.Add((start, end));
                        }
                        break;
                }
            }
            
            return edges;
        }
        
        /// <summary>
        /// 计算形态学膨胀的轮廓点（正确的凹多边形算法）
        /// </summary>
        private List<Point3d> ComputeMorphologicalDilation(List<Point3d> vertices, List<(Point3d start, Point3d end)> edges, double radius)
        {
            // 确保顶点按逆时针方向排序（外轮廓）
            var orderedVertices = EnsureCounterClockwise(vertices);
            
            var offsetLines = new List<OffsetLine>();
            
            // 为每条边创建偏移线
            for (int i = 0; i < orderedVertices.Count; i++)
            {
                var start = orderedVertices[i];
                var end = orderedVertices[(i + 1) % orderedVertices.Count];
                
                var direction = (end - start).GetNormal();
                var normal = new Vector3d(-direction.Y, direction.X, 0); // 左法向量（向外）
                
                var offsetStart = start + normal * radius;
                var offsetEnd = end + normal * radius;
                
                offsetLines.Add(new OffsetLine
                {
                    Start = offsetStart,
                    End = offsetEnd,
                    OriginalStart = start,
                    OriginalEnd = end,
                    EdgeIndex = i
                });
            }
            
            // 计算偏移线之间的交点和圆弧连接
            var boundaryElements = ConnectOffsetLines(offsetLines, radius);
            
            // 提取最终边界点
            return ExtractBoundaryPoints(boundaryElements);
        }
        
        /// <summary>
        /// 偏移线的定义
        /// </summary>
        private class OffsetLine
        {
            public Point3d Start { get; set; }
            public Point3d End { get; set; }
            public Point3d OriginalStart { get; set; }
            public Point3d OriginalEnd { get; set; }
            public int EdgeIndex { get; set; }
        }
        
        /// <summary>
        /// 边界元素（线段或圆弧）
        /// </summary>
        private abstract class BoundaryElement
        {
            public abstract List<Point3d> GetPoints(int numSamples = 1);
        }
        
        private class LineBoundary : BoundaryElement
        {
            public Point3d Start { get; set; }
            public Point3d End { get; set; }
            
            public override List<Point3d> GetPoints(int numSamples = 1)
            {
                return new List<Point3d> { Start, End };
            }
        }
        
        private class ArcBoundary : BoundaryElement
        {
            public Point3d Center { get; set; }
            public double Radius { get; set; }
            public double StartAngle { get; set; }
            public double EndAngle { get; set; }
            
            public override List<Point3d> GetPoints(int numSamples = 8)
            {
                var points = new List<Point3d>();
                for (int i = 0; i <= numSamples; i++)
                {
                    var t = (double)i / numSamples;
                    var angle = StartAngle + t * (EndAngle - StartAngle);
                    var x = Center.X + Radius * Math.Cos(angle);
                    var y = Center.Y + Radius * Math.Sin(angle);
                    points.Add(new Point3d(x, y, 0));
                }
                return points;
            }
        }
        
        /// <summary>
        /// Point3d比较器
        /// </summary>
        private class Point3dComparer : IEqualityComparer<Point3d>
        {
            public bool Equals(Point3d x, Point3d y)
            {
                return GeometryHelper.IsWithinTolerance(x, y, 1e-6);
            }
            
            public int GetHashCode(Point3d obj)
            {
                return obj.X.GetHashCode() ^ obj.Y.GetHashCode();
            }
        }
        
        /// <summary>
        /// 计算点集的凸包（Graham扫描算法）
        /// </summary>
        private List<Point3d> ComputeConvexHull(List<Point3d> points)
        {
            if (points.Count < 3) return points;
            
            // 去除重复点
            var uniquePoints = points.Distinct(new Point3dComparer()).ToList();
            if (uniquePoints.Count < 3) return uniquePoints;
            
            // 找到最左下角的点
            var start = uniquePoints.OrderBy(p => p.Y).ThenBy(p => p.X).First();
            
            // 按极角排序
            var sortedPoints = uniquePoints.Where(p => p != start)
                .OrderBy(p => Math.Atan2(p.Y - start.Y, p.X - start.X))
                .ToList();
            
            var hull = new List<Point3d> { start };
            
            foreach (var point in sortedPoints)
            {
                // 移除不在凸包上的点
                while (hull.Count > 1 && CrossProduct(hull[hull.Count - 2], hull[hull.Count - 1], point) <= 0)
                {
                    hull.RemoveAt(hull.Count - 1);
                }
                hull.Add(point);
            }
            
            return hull;
        }
        
        /// <summary>
        /// 计算叉积（用于判断点的方向）
        /// </summary>
        private double CrossProduct(Point3d o, Point3d a, Point3d b)
        {
            return (a.X - o.X) * (b.Y - o.Y) - (a.Y - o.Y) * (b.X - o.X);
        }
        
        /// <summary>
        /// 确保顶点按逆时针方向排序
        /// </summary>
        private List<Point3d> EnsureCounterClockwise(List<Point3d> vertices)
        {
            if (vertices.Count < 3) return vertices;
            
            // 计算多边形的面积（使用鞋带公式）
            double area = 0;
            for (int i = 0; i < vertices.Count; i++)
            {
                int j = (i + 1) % vertices.Count;
                area += vertices[i].X * vertices[j].Y - vertices[j].X * vertices[i].Y;
            }
            
            // 如果面积为负，说明是顺时针，需要反转
            if (area < 0)
            {
                var reversed = new List<Point3d>(vertices);
                reversed.Reverse();
                return reversed;
            }
            
            return vertices;
        }
        
        /// <summary>
        /// 连接偏移线并在顶点处添加圆弧
        /// </summary>
        private List<BoundaryElement> ConnectOffsetLines(List<OffsetLine> offsetLines, double radius)
        {
            var elements = new List<BoundaryElement>();
            
            for (int i = 0; i < offsetLines.Count; i++)
            {
                var currentLine = offsetLines[i];
                var nextLine = offsetLines[(i + 1) % offsetLines.Count];
                
                // 添加当前线段
                elements.Add(new LineBoundary
                {
                    Start = currentLine.Start,
                    End = currentLine.End
                });
                
                // 在相邻线段之间添加圆弧连接
                var originalVertex = currentLine.OriginalEnd;
                
                // 计算入边和出边的方向
                var inDirection = (currentLine.OriginalEnd - currentLine.OriginalStart).GetNormal();
                var outDirection = (nextLine.OriginalEnd - nextLine.OriginalStart).GetNormal();
                
                // 计算转角角度
                var inAngle = Math.Atan2(inDirection.Y, inDirection.X);
                var outAngle = Math.Atan2(outDirection.Y, outDirection.X);
                
                // 计算内角
                var turnAngle = outAngle - inAngle;
                if (turnAngle > Math.PI) turnAngle -= 2 * Math.PI;
                if (turnAngle < -Math.PI) turnAngle += 2 * Math.PI;
                
                // 只在左转（凸角）时添加圆弧
                if (turnAngle > 0)
                {
                    var startAngle = Math.Atan2(-inDirection.Y, -inDirection.X);
                    var endAngle = Math.Atan2(outDirection.Y, outDirection.X);
                    
                    // 确保角度范围正确
                    if (endAngle < startAngle) endAngle += 2 * Math.PI;
                    
                    elements.Add(new ArcBoundary
                    {
                        Center = originalVertex,
                        Radius = radius,
                        StartAngle = startAngle,
                        EndAngle = endAngle
                    });
                }
            }
            
            return elements;
        }
        
        /// <summary>
        /// 从边界元素中提取点
        /// </summary>
        private List<Point3d> ExtractBoundaryPoints(List<BoundaryElement> elements)
        {
            var points = new List<Point3d>();
            
            foreach (var element in elements)
            {
                var elementPoints = element.GetPoints();
                // 只取第一个点，避免重复
                if (elementPoints.Count > 0)
                {
                    points.AddRange(elementPoints.Take(elementPoints.Count - 1));
                }
            }
            
            return points;
        }
        
        /// <summary>
        /// 计算点集的质心
        /// </summary>
        private Point3d CalculateCentroid(List<Point3d> points)
        {
            if (points.Count == 0) return Point3d.Origin;
            
            var sumX = points.Sum(p => p.X);
            var sumY = points.Sum(p => p.Y);
            
            return new Point3d(sumX / points.Count, sumY / points.Count, 0);
        }
        
        /// <summary>
        /// 将点序列转换为形态学段
        /// </summary>
        private List<MorphologySegment> ConvertPointsToSegments(List<Point3d> points, double radius)
        {
            var segments = new List<MorphologySegment>();
            
            if (points.Count < 2) return segments;
            
            // 将连续的点连接成线段
            for (int i = 0; i < points.Count; i++)
            {
                var start = points[i];
                var end = points[(i + 1) % points.Count]; // 循环连接
                
                segments.Add(new MorphologySegment
                {
                    Type = MorphologySegment.SegmentType.OffsetLine,
                    StartPoint = start,
                    EndPoint = end,
                    OriginalEntity = null, // 这是计算出的结果，没有原始实体
                    StartParameter = 0,
                    EndParameter = 1
                });
            }
            
            return segments;
        }

        /// <summary>
        /// 创建直线的偏移段
        /// </summary>
        private MorphologySegment CreateOffsetLineSegment(Line line, double radius)
        {
            var direction = (line.EndPoint - line.StartPoint).GetNormal();
            // 形态学膨胀需要向外偏移，使用左手垂直向量
            var perpendicular = new Vector3d(-direction.Y, direction.X, 0) * radius;

            return new MorphologySegment
            {
                Type = MorphologySegment.SegmentType.OffsetLine,
                StartPoint = line.StartPoint + perpendicular,
                EndPoint = line.EndPoint + perpendicular,
                OriginalEntity = line,
                StartParameter = 0,
                EndParameter = 1
            };
        }

        /// <summary>
        /// 创建圆的偏移段
        /// </summary>
        private MorphologySegment CreateOffsetCircleSegment(Circle circle, double radius)
        {
            var expandedRadius = circle.Radius + radius;

            return new MorphologySegment
            {
                Type = MorphologySegment.SegmentType.TransitionArc,
                StartPoint = circle.Center + new Vector3d(expandedRadius, 0, 0),
                EndPoint = circle.Center + new Vector3d(expandedRadius, 0, 0), // 全圆
                OriginalEntity = circle,
                CenterPoint = circle.Center,
                Radius = expandedRadius,
                IsClockwise = false,
                StartParameter = 0,
                EndParameter = 1
            };
        }

        /// <summary>
        /// 创建圆弧的偏移段
        /// </summary>
        private MorphologySegment CreateOffsetArcSegment(Arc arc, double radius)
        {
            var expandedRadius = arc.Radius + radius;

            return new MorphologySegment
            {
                Type = MorphologySegment.SegmentType.TransitionArc,
                StartPoint = arc.Center + (arc.StartPoint - arc.Center).GetNormal() * expandedRadius,
                EndPoint = arc.Center + (arc.EndPoint - arc.Center).GetNormal() * expandedRadius,
                OriginalEntity = arc,
                CenterPoint = arc.Center,
                Radius = expandedRadius,
                IsClockwise = arc.StartParam > arc.EndParam,
                StartParameter = 0,
                EndParameter = 1
            };
        }

        /// <summary>
        /// 添加过渡圆弧
        /// </summary>
        private List<MorphologySegment> AddTransitionArcs(List<MorphologySegment> segments, double radius)
        {
            if (segments.Count < 2) return segments;

            var result = new List<MorphologySegment>();

            for (int i = 0; i < segments.Count; i++)
            {
                result.Add(segments[i]);

                // 在相邻段之间添加过渡圆弧
                var current = segments[i];
                var next = segments[(i + 1) % segments.Count];

                if (!GeometryHelper.IsWithinTolerance(current.EndPoint, next.StartPoint, 1e-6))
                {
                    var transitionArc = CreateTransitionArc(current, next, radius);
                    if (transitionArc != null)
                    {
                        result.Add(transitionArc);
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 创建两段之间的过渡圆弧（正确的形态学膨胀算法）
        /// </summary>
        private MorphologySegment CreateTransitionArc(MorphologySegment segment1, MorphologySegment segment2, double radius)
        {
            // 获取原始几何的连接点（两条线的交点或顶点）
            Point3d cornerPoint;
            
            if (segment1.OriginalEntity is Line line1 && segment2.OriginalEntity is Line line2)
            {
                // 计算两条线的交点作为圆弧中心
                cornerPoint = FindLinesIntersection(line1, line2);
            }
            else
            {
                // 回退到端点中点
                cornerPoint = new Point3d(
                    (segment1.EndPoint.X + segment2.StartPoint.X) * 0.5,
                    (segment1.EndPoint.Y + segment2.StartPoint.Y) * 0.5,
                    0);
            }

            // 形态学膨胀的过渡圆弧：以原始顶点为圆心，膨胀半径为半径
            var startVector = segment1.EndPoint - cornerPoint;
            var endVector = segment2.StartPoint - cornerPoint;
            
            // 确保圆弧方向正确（逆时针为外轮廓）
            var crossProduct = startVector.X * endVector.Y - startVector.Y * endVector.X;
            var isClockwise = crossProduct < 0;

            return new MorphologySegment
            {
                Type = MorphologySegment.SegmentType.TransitionArc,
                StartPoint = segment1.EndPoint,
                EndPoint = segment2.StartPoint,
                CenterPoint = cornerPoint,
                Radius = radius, // 使用膨胀半径
                IsClockwise = isClockwise,
                StartParameter = 0,
                EndParameter = 1
            };
        }

        /// <summary>
        /// 计算两条直线的交点
        /// </summary>
        private Point3d FindLinesIntersection(Line line1, Line line2)
        {
            try
            {
                var dir1 = line1.EndPoint - line1.StartPoint;
                var dir2 = line2.EndPoint - line2.StartPoint;
                var startDiff = line2.StartPoint - line1.StartPoint;
                
                var cross = dir1.X * dir2.Y - dir1.Y * dir2.X;
                if (Math.Abs(cross) < 1e-10) // 平行线
                {
                    return line1.EndPoint; // 返回第一条线的端点
                }
                
                var t = (startDiff.X * dir2.Y - startDiff.Y * dir2.X) / cross;
                return line1.StartPoint + dir1 * t;
            }
            catch
            {
                return line1.EndPoint;
            }
        }

        /// <summary>
        /// 找到形态学起点
        /// </summary>
        private Point3d FindMorphologyStartPoint()
        {
            var allPoints = _morphologySegments.SelectMany(s => new[] { s.StartPoint, s.EndPoint }).ToList();
            return allPoints.OrderBy(p => p.Y).ThenBy(p => p.X).First();
        }

        /// <summary>
        /// 找到起始段
        /// </summary>
        private void FindStartingSegment()
        {
            for (int i = 0; i < _morphologySegments.Count; i++)
            {
                var segment = _morphologySegments[i];
                if (GeometryHelper.IsWithinTolerance(segment.StartPoint, _startPosition, 1e-6))
                {
                    _currentSegmentIndex = i;
                    _currentSegmentParameter = 0;
                    return;
                }
                if (GeometryHelper.IsWithinTolerance(segment.EndPoint, _startPosition, 1e-6))
                {
                    _currentSegmentIndex = i;
                    _currentSegmentParameter = 1;
                    return;
                }
            }
        }

        /// <summary>
        /// 寻找下一个事件
        /// </summary>
        public WalkerEventResult FindNextEvent()
        {
            var eventResult = new WalkerEventResult
            {
                Position = _currentPosition,
                FromDirection = _fromDirection
            };

            // 检查是否回到起点
            if (_pathPoints.Count > 3 && 
                GeometryHelper.IsWithinTolerance(_currentPosition, _startPosition, _radius * 0.1))
            {
                eventResult.EventType = WalkerEventType.OutlineClosed;
                return eventResult;
            }

            // 检查是否需要切换到下一段
            if (_currentSegmentParameter >= 1.0)
            {
                eventResult.EventType = WalkerEventType.MorphologySwitch;
                eventResult.AvailableDirections = GetNextSegmentDirections();
                return eventResult;
            }

            // 正常前进事件
            eventResult.EventType = WalkerEventType.ReachVertex;
            eventResult.AvailableDirections = new List<WalkDirection> { _currentDirection };

            return eventResult;
        }

        /// <summary>
        /// 获取下一段的可能方向
        /// </summary>
        private List<WalkDirection> GetNextSegmentDirections()
        {
            var directions = new List<WalkDirection>();
            var nextSegmentIndex = (_currentSegmentIndex + 1) % _morphologySegments.Count;

            if (nextSegmentIndex < _morphologySegments.Count)
            {
                var nextSegment = _morphologySegments[nextSegmentIndex];
                var tangent = nextSegment.GetTangentAtParameter(0);
                var direction = VectorToDirection(tangent);
                directions.Add(direction);
            }

            return directions;
        }

        /// <summary>
        /// 向量转方向
        /// </summary>
        private WalkDirection VectorToDirection(Vector3d vector)
        {
            var angle = Math.Atan2(vector.Y, vector.X);
            if (angle < 0) angle += 2 * Math.PI;

            var octant = (int)Math.Round(angle / (Math.PI / 4)) % 8;
            
            // 根据八分圆索引返回对应的WalkDirection对象
            return octant switch
            {
                0 => WalkDirection.East,
                1 => WalkDirection.NorthEast,
                2 => WalkDirection.North,
                3 => WalkDirection.NorthWest,
                4 => WalkDirection.West,
                5 => WalkDirection.SouthWest,
                6 => WalkDirection.South,
                7 => WalkDirection.SouthEast,
                _ => WalkDirection.East
            };
        }

        /// <summary>
        /// 做出方向决策
        /// </summary>
        public DirectionDecision MakeDecision(WalkerEventResult eventResult)
        {
            var decision = new DirectionDecision();

            if (eventResult.EventType == WalkerEventType.OutlineClosed)
            {
                decision.ChosenDirection = _currentDirection;
                decision.NextPosition = _startPosition;
                decision.Reason = "形态学轮廓闭合";
                return decision;
            }

            if (eventResult.EventType == WalkerEventType.MorphologySwitch)
            {
                // 切换到下一段
                var nextSegmentIndex = (_currentSegmentIndex + 1) % _morphologySegments.Count;
                if (nextSegmentIndex < _morphologySegments.Count)
                {
                    var nextSegment = _morphologySegments[nextSegmentIndex];
                    decision.ChosenDirection = GetNextSegmentDirections().FirstOrDefault();
                    decision.NextPosition = nextSegment.StartPoint;
                    decision.Reason = $"切换到形态学段 {nextSegmentIndex}";
                    decision.AdditionalData["NextSegmentIndex"] = nextSegmentIndex;
                }
                else
                {
                    decision.RequiresBacktrack = true;
                    decision.Reason = "无下一段可切换";
                }
                return decision;
            }

            // 正常前进
            var currentSegment = _morphologySegments[_currentSegmentIndex];
            var stepSize = 0.1; // 步长参数
            var nextParameter = Math.Min(1.0, _currentSegmentParameter + stepSize);
            
            decision.ChosenDirection = _currentDirection;
            decision.NextPosition = currentSegment.GetPointAtParameter(nextParameter);
            decision.StepDistance = GeometryHelper.Distance2D(_currentPosition, decision.NextPosition);
            decision.Reason = $"沿形态学段前进到参数 {nextParameter:F2}";

            return decision;
        }

        /// <summary>
        /// 更新状态
        /// </summary>
        public void UpdateState(DirectionDecision decision)
        {
            if (decision.RequiresBacktrack)
            {
                if (_backtrackStack.Count > 0)
                {
                    var (prevPosition, prevSegmentIndex, prevParameter) = _backtrackStack.Pop();
                    _currentPosition = prevPosition;
                    _currentSegmentIndex = prevSegmentIndex;
                    _currentSegmentParameter = prevParameter;
                    
                    if (_pathPoints.Count > 1)
                    {
                        _pathPoints.RemoveAt(_pathPoints.Count - 1);
                    }
                }
                else
                {
                    _isCompleted = true;
                }
                return;
            }

            // 保存当前状态
            _backtrackStack.Push((_currentPosition, _currentSegmentIndex, _currentSegmentParameter));

            // 更新位置和方向
            _fromDirection = GeometryHelper.GetOppositeDirection(decision.ChosenDirection);
            _currentDirection = decision.ChosenDirection;
            _currentPosition = decision.NextPosition;

            // 更新段参数
            if (decision.AdditionalData.ContainsKey("NextSegmentIndex"))
            {
                _currentSegmentIndex = (int)decision.AdditionalData["NextSegmentIndex"];
                _currentSegmentParameter = 0;
            }
            else
            {
                // 计算新的参数
                var currentSegment = _morphologySegments[_currentSegmentIndex];
                var stepSize = 0.1;
                _currentSegmentParameter = Math.Min(1.0, _currentSegmentParameter + stepSize);
            }

            // 添加到路径
            _pathPoints.Add(_currentPosition);

            // 检查完成条件
            if (GeometryHelper.IsWithinTolerance(_currentPosition, _startPosition, _radius * 0.1) && _pathPoints.Count > 3)
            {
                _isCompleted = true;
            }
        }

        /// <summary>
        /// 获取结果路径
        /// </summary>
        public List<Point3d> GetResultPath()
        {
            var result = new List<Point3d>(_pathPoints);

            // 确保闭合
            if (result.Count > 2 && !GeometryHelper.IsWithinTolerance(result.First(), result.Last(), 1e-6))
            {
                result.Add(result.First());
            }

            return result;
        }
    }
}
