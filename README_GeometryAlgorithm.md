# 几何算法轮廓提取功能

## 概述

本功能为AutoCAD插件添加了基于NetTopologySuite的高级几何算法轮廓提取能力，提供了凸包、边界提取和Alpha Shape等多种算法选择，以替代原本计划的Boost.Geometry C++库集成方案。

## 功能特性

### 支持的算法类型

1. **凸包算法 (Convex Hull)**
   - 计算实体集合的最小凸多边形
   - 适用于获取最外层轮廓
   - 性能优异，适合大量实体处理

2. **边界提取 (Boundary Extraction)**
   - 提取实体集合的实际边界
   - 保持原始形状特征
   - 支持缓冲区连接断开的几何

3. **Alpha Shape (凹包算法)**
   - 可调节凹凸程度的轮廓算法
   - 通过Alpha参数控制形状复杂度
   - 适用于复杂形状的轮廓提取

### 支持的实体类型

- 直线 (Line)
- 圆弧 (Arc)
- 圆 (Circle)
- 多段线 (Polyline)
- 样条曲线 (Spline)
- 椭圆 (Ellipse)
- 区域 (Region)

## 使用方法

### 命令行接口

#### 基本命令

```
EXTRACTOUTLINE    - 通用轮廓提取命令（包含所有策略选择）
CONVEXHULL        - 直接提取凸包轮廓
EXTRACTBOUNDARY   - 提取边界轮廓
ALPHASHAPE        - 提取Alpha Shape凹包
GEOMETRYALGORITHM - 选择算法类型进行提取
GEOMETRYHELP      - 显示帮助信息
```

#### 使用步骤

1. 在AutoCAD中选择要处理的实体
2. 运行相应的命令
3. 根据提示输入参数（如缓冲距离、Alpha值等）
4. 查看生成的轮廓结果

### 编程接口

#### 基本用法

```csharp
// 创建几何算法服务
using (var geometryService = new GeometryAlgorithmService())
{
    // 提取凸包
    var convexHull = geometryService.ExtractConvexHull(entities);
    
    // 提取边界（带缓冲区）
    var boundary = geometryService.ExtractBoundary(entities, bufferDistance: 0.1);
    
    // 提取Alpha Shape
    var alphaShape = geometryService.ExtractAlphaShape(entities, alpha: 1.0);
    
    // 简化轮廓
    var simplified = geometryService.SimplifyOutline(points, tolerance: 0.1);
}
```

#### 使用步行者模式

```csharp
// 创建几何算法步行者
var walker = new GeometryAlgorithmWalker(GeometryAlgorithmWalker.GeometryAlgorithmType.ConvexHull);

// 初始化并执行
walker.Initialize(entities, tolerance: 1.0);

// 获取结果
var result = walker.GetResultPath();
```

## 性能优化

### 性能限制

- 默认最大实体数量：10,000个
- 默认最大点数量：50,000个
- 可通过构造函数参数调整限制

### 内存管理

- 实现了IDisposable接口，支持资源自动释放
- 使用using语句确保资源正确释放
- 内置内存使用监控和优化

### 性能建议

1. **大量实体处理**
   - 分批处理超大实体集合
   - 使用适当的容差参数减少计算复杂度
   - 考虑预先过滤不必要的实体

2. **算法选择**
   - 简单轮廓：使用凸包算法
   - 保持形状特征：使用边界提取
   - 复杂形状：使用Alpha Shape

## 错误处理

### 常见错误及解决方案

1. **"实体验证失败"**
   - 检查选择的实体是否为支持的类型
   - 确保实体几何有效（如线段长度不为零）

2. **"点集验证失败"**
   - 确保至少有3个非共线的点
   - 检查实体是否过于简单或退化

3. **"超过最大限制"**
   - 减少选择的实体数量
   - 分批处理大量实体
   - 调整服务构造参数

4. **"ObjectDisposedException"**
   - 确保在服务释放后不再使用
   - 使用using语句管理服务生命周期

## 技术架构

### 核心组件

1. **GeometryAlgorithmService**
   - 主要的几何算法服务类
   - 提供凸包、边界提取、Alpha Shape算法
   - 集成验证和性能优化功能

2. **GeometryAlgorithmWalker**
   - 实现IOutlineWalker接口
   - 集成到现有的轮廓追踪框架
   - 支持策略模式选择

3. **GeometryValidationService**
   - 提供几何数据验证功能
   - 支持实体和点集合验证
   - 包含错误诊断和修复建议

### 依赖库

- **NetTopologySuite 2.5.0**
  - 提供核心几何算法实现
  - 支持.NET Framework 4.8
  - 高性能的计算几何库

## 配置选项

### 服务配置

```csharp
// 自定义配置
var service = new GeometryAlgorithmService(
    tolerance: 1e-6,        // 几何运算容差
    maxEntityCount: 5000,   // 最大实体数量
    maxPointCount: 25000    // 最大点数量
);
```

### 算法参数

- **容差 (Tolerance)**: 控制几何运算精度
- **缓冲距离 (Buffer Distance)**: 边界提取时的连接距离
- **Alpha参数**: Alpha Shape算法的形状控制参数

## 测试

### 单元测试

项目包含完整的单元测试套件：

- `GeometryAlgorithmServiceTests`: 服务功能测试
- `GeometryAlgorithmWalkerTests`: 步行者模式测试
- 性能限制测试
- 错误处理测试
- 资源管理测试

### 运行测试

```bash
# 在Visual Studio中运行所有测试
Test -> Run All Tests

# 或使用命令行
dotnet test
```

## 故障排除

### 调试技巧

1. **启用详细日志**
   - 使用项目的日志服务记录详细信息
   - 检查几何验证结果

2. **性能分析**
   - 监控内存使用情况
   - 分析算法执行时间

3. **几何可视化**
   - 在AutoCAD中可视化中间结果
   - 检查输入实体的有效性

### 常见问题

**Q: 为什么选择NetTopologySuite而不是Boost.Geometry？**
A: NetTopologySuite是纯C#实现，避免了C++/CLI集成的复杂性，提供了相同的几何算法能力，并且与.NET生态系统完美集成。

**Q: 如何处理大量实体？**
A: 可以分批处理，或者调整性能限制参数。建议先使用简单算法（如凸包）进行初步筛选。

**Q: 算法结果不准确怎么办？**
A: 检查输入实体的质量，调整容差参数，或尝试不同的算法类型。

## 更新日志

### v1.0.0 (当前版本)
- 初始实现几何算法轮廓提取功能
- 支持凸包、边界提取、Alpha Shape算法
- 集成到现有轮廓追踪框架
- 完整的错误处理和性能优化
- 包含单元测试和文档

## 贡献

欢迎提交问题报告和功能请求。在提交代码更改之前，请确保：

1. 运行所有单元测试
2. 遵循现有的代码风格
3. 添加适当的文档和注释
4. 更新相关的测试用例

## 许可证

本功能遵循项目的整体许可证协议。NetTopologySuite使用BSD许可证。
