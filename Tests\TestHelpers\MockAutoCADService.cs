using System.Collections.Generic;
using IECAD.Models;
using IECAD.Services;

namespace IECAD.Tests.TestHelpers
{
    /// <summary>
    /// Mock implementation of IAutoCADService for testing
    /// </summary>
    public class MockAutoCADService : IAutoCADService
    {
        private readonly List<LayerModel> _layers;
        private readonly List<string> _executedCommands;
        private bool _isReady;
        private string _activeDocumentName;

        public MockAutoCADService()
        {
            _layers = new List<LayerModel>();
            _executedCommands = new List<string>();
            _isReady = true;
            _activeDocumentName = "TestDocument.dwg";

            // Add some default test layers
            _layers.Add(new LayerModel { Name = "0", IsVisible = true });
            _layers.Add(new LayerModel { Name = "Walls", IsVisible = true });
            _layers.Add(new LayerModel { Name = "Doors", IsVisible = false });
            _layers.Add(new LayerModel { Name = "Windows", IsVisible = true });
            _layers.Add(new LayerModel { Name = "Dimensions", IsVisible = false });
        }

        public List<LayerModel> GetLayers()
        {
            return new List<LayerModel>(_layers);
        }

        public void SetLayerVisibility(string layerName, bool isVisible)
        {
            var layer = _layers.Find(l => l.Name == layerName);
            if (layer != null)
            {
                layer.IsVisible = isVisible;
            }
        }

        public void ExecuteCommand(string command)
        {
            _executedCommands.Add(command);
        }

        public bool IsAutoCADReady()
        {
            return _isReady;
        }

        public string GetActiveDocumentName()
        {
            return _activeDocumentName;
        }

        // Test helper methods
        public void SetAutoCADReady(bool isReady)
        {
            _isReady = isReady;
        }

        public void SetActiveDocumentName(string documentName)
        {
            _activeDocumentName = documentName;
        }

        public void AddLayer(LayerModel layer)
        {
            _layers.Add(layer);
        }

        public void RemoveLayer(string layerName)
        {
            _layers.RemoveAll(l => l.Name == layerName);
        }

        public void ClearLayers()
        {
            _layers.Clear();
        }

        public List<string> GetExecutedCommands()
        {
            return new List<string>(_executedCommands);
        }

        public void ClearExecutedCommands()
        {
            _executedCommands.Clear();
        }

        public LayerModel GetLayer(string layerName)
        {
            return _layers.Find(l => l.Name == layerName);
        }

        public int LayerCount => _layers.Count;

        public bool HasLayer(string layerName)
        {
            return _layers.Exists(l => l.Name == layerName);
        }
    }
}
