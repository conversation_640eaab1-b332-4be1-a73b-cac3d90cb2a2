using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;
using System;
using System.Collections.Generic;
using System.Linq;
using IECAD.Services.OutlineWalker;

namespace IECAD.Services
{
    internal class OutlineExtractor
    {
        private readonly OutlineWalkerEngine _walkerEngine;

        public OutlineExtractor()
        {
            _walkerEngine = new OutlineWalkerEngine();
        }

        /// <summary>
        /// 提取轮廓 - 使用默认策略（兼容旧版本调用方式）
        /// </summary>
        public void ExtractOutline()
        {
            ExtractOutline(WalkerStrategy.Grid); // 使用栅格策略保持兼容性
        }

        /// <summary>
        /// 提取轮廓 - 指定策略
        /// </summary>
        /// <param name="strategy">步行者策略</param>
        /// <param name="tolerance">容差参数</param>
        public void ExtractOutline(WalkerStrategy strategy = WalkerStrategy.Precise, double tolerance = 1.0)
        {
            try
            {
                // 委托给新的模块化步行者引擎
                var result = _walkerEngine.ExtractOutline(strategy, tolerance);
            }
            catch (System.Exception ex)
            {
                Document doc = Application.DocumentManager.MdiActiveDocument;
                Editor ed = doc.Editor;
                ed.WriteMessage($"\n轮廓提取出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 直接调用步行者引擎的方法，支持策略选择
        /// </summary>
        /// <param name="strategy">步行者策略</param>
        /// <param name="tolerance">容差参数</param>
        public void ExtractOutlineWithStrategy(WalkerStrategy strategy, double tolerance = 1.0)
        {
            ExtractOutline(strategy, tolerance);
        }
    }

    public static class GeometryProcessor
    {
        public static List<Point3d> SimplifyOutline(List<Point3d> points, double tolerance)
        {
            if (points == null || points.Count < 3)
                return points;

            // Ensure points form a closed ring
            var workingPoints = new List<Point3d>(points);
            if (!PointsEqual2D(workingPoints.First(), workingPoints.Last()))
            {
                workingPoints.Add(workingPoints.First());
            }

            // Remove duplicate points
            workingPoints = workingPoints.Distinct().ToList();
            if (!PointsEqual2D(workingPoints.First(), workingPoints.Last()))
            {
                workingPoints.Add(workingPoints.First());
            }

            // Apply Douglas-Peucker simplification
            var simplified = DouglasPeuckerSimplify(workingPoints, tolerance);
            return simplified;
        }

        private static bool PointsEqual2D(Point3d p1, Point3d p2)
        {
            return Math.Abs(p1.X - p2.X) < 1e-10 && Math.Abs(p1.Y - p2.Y) < 1e-10;
        }

        private static List<Point3d> DouglasPeuckerSimplify(List<Point3d> points, double tolerance)
        {
            if (points.Count <= 2)
                return new List<Point3d>(points);

            // Find the point with the maximum distance
            int index = 0;
            double maxDistance = 0;

            for (int i = 1; i < points.Count - 1; i++)
            {
                double distance = PerpendicularDistance(points[i], points[0], points[points.Count - 1]);
                if (distance > maxDistance)
                {
                    index = i;
                    maxDistance = distance;
                }
            }

            // If max distance is greater than tolerance, recursively simplify
            List<Point3d> result = new List<Point3d>();
            if (maxDistance > tolerance)
            {
                // Recursive call
                var firstSegment = DouglasPeuckerSimplify(points.Take(index + 1).ToList(), tolerance);
                var secondSegment = DouglasPeuckerSimplify(points.Skip(index).ToList(), tolerance);

                // Build the result
                result.AddRange(firstSegment.Take(firstSegment.Count - 1));
                result.AddRange(secondSegment);
            }
            else
            {
                result.Add(points[0]);
                result.Add(points[points.Count - 1]);
            }

            return result;
        }

        private static double PerpendicularDistance(Point3d point, Point3d lineStart, Point3d lineEnd)
        {
            double dx = lineEnd.X - lineStart.X;
            double dy = lineEnd.Y - lineStart.Y;

            // Handle cases where line segment is a point
            if (Math.Abs(dx) < 1e-10 && Math.Abs(dy) < 1e-10)
                return Math.Sqrt(Math.Pow(point.X - lineStart.X, 2) + Math.Pow(point.Y - lineStart.Y, 2));

            // Calculate perpendicular distance
            double numerator = Math.Abs(dy * point.X - dx * point.Y + lineEnd.X * lineStart.Y - lineEnd.Y * lineStart.X);
            double denominator = Math.Sqrt(dx * dx + dy * dy);

            return numerator / denominator;
        }
        public static List<(int x, int y)> SimplifyGridCells(List<(int x, int y)> cells)
        {
            if (cells == null || cells.Count < 3)
                return cells;

            List<(int x, int y)> simplified = new List<(int x, int y)>(cells);

            bool removedAny;
            do
            {
                removedAny = false;
                List<int> removalIndices = new List<int>();

                for (int i = 0; i < simplified.Count; i++)
                {
                    int prevIndex = i - 1 >= 0 ? i - 1 : simplified.Count - 1;
                    int nextIndex = i + 1 < simplified.Count ? i + 1 : 0;

                    var prevCell = simplified[prevIndex];
                    var nextCell = simplified[nextIndex];
                    var currentCell = simplified[i];

                    if (prevCell.Equals(nextCell))
                    {
                        removalIndices.Add(i);
                    }
                }

                foreach (int index in removalIndices.OrderByDescending(i => i))
                {
                    simplified.RemoveAt(index);
                    removedAny = true;
                }
            } while (removedAny && simplified.Count >= 3);

            if (simplified.Count >= 3 && !simplified.First().Equals(simplified.Last()))
            {
                simplified.Add(simplified.First());
            }

            return simplified;
        }
    }
}