﻿using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.Windows;
using System.Windows.Forms.Integration;
using IECAD.Views;
using IECAD.Constants;
[assembly: CommandClass(typeof(IECAD.Commands.SidebarCommands))]
namespace IECAD.Commands
{
    public class SidebarCommands
    {
        private static PaletteSet _ps;

        [CommandMethod("SHOWSIDEBAR")]
        public void ShowSidebar()
        {
            if (_ps == null || !IsValidPaletteSet(_ps))
            {
                CreateAndShowPaletteSet();
            }
            else
            {
                _ps.Visible = true;
            }
        }

        private void CreateAndShowPaletteSet()
        {
            _ps = new PaletteSet(ApplicationConstants.SIDEBAR_PALETTE_NAME)
            {
                Style = PaletteSetStyles.ShowAutoHideButton |
                        PaletteSetStyles.ShowCloseButton |
                        PaletteSetStyles.ShowPropertiesMenu |
                        PaletteSetStyles.Snappable,
                MinimumSize = new System.Drawing.Size(ApplicationConstants.SIDEBAR_MIN_WIDTH, ApplicationConstants.SIDEBAR_MIN_HEIGHT)
            };

            var mainSidebarControl = new MainSidebarControl();
            ElementHost elementHost = new ElementHost
            {
                Dock = System.Windows.Forms.DockStyle.Fill,
                Child = mainSidebarControl
            };

            _ps.Add("Sidebar", elementHost);
            _ps.Visible = true;
        }

        private bool IsValidPaletteSet(PaletteSet ps)
        {
            if (ps == null)
                return false;

            try
            {
                var temp = ps.Visible;
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}