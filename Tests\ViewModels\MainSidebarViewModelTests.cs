using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using IECAD.ViewModels;
using IECAD.Tests.TestHelpers;

namespace IECAD.Tests.ViewModels
{
    [TestClass]
    public class MainSidebarViewModelTests : TestBase
    {
        private MainSidebarViewModel _viewModel;

        [TestInitialize]
        public override void TestInitialize()
        {
            base.TestInitialize();
            SetupTestServiceConfiguration();
            _viewModel = new MainSidebarViewModel(MockAutoCADService.Object);
        }

        [TestCleanup]
        public override void TestCleanup()
        {
            _viewModel?.Dispose();
            base.TestCleanup();
        }

        [TestMethod]
        public void Constructor_ShouldInitializeProperties()
        {
            // Assert
            Assert.IsNotNull(_viewModel.LayerManagerVM);
            Assert.IsNotNull(_viewModel.RefreshCommand);
            Assert.IsNotNull(_viewModel.ClearCacheCommand);
            Assert.IsNotNull(_viewModel.ApplicationTitle);
            Assert.IsNotNull(_viewModel.LogoPath);
        }

        [TestMethod]
        public void ApplicationTitle_ShouldBeSet()
        {
            // Assert
            Assert.IsFalse(string.IsNullOrEmpty(_viewModel.ApplicationTitle));
        }

        [TestMethod]
        public void LogoPath_ShouldBeSet()
        {
            // Assert
            Assert.IsFalse(string.IsNullOrEmpty(_viewModel.LogoPath));
        }

        [TestMethod]
        public void RefreshCommand_CanExecute_WhenNotBusy_ShouldReturnTrue()
        {
            // Act
            var canExecute = _viewModel.RefreshCommand.CanExecute(null);

            // Assert
            Assert.IsTrue(canExecute);
        }

        [TestMethod]
        public void ClearCacheCommand_CanExecute_WhenNotBusy_ShouldReturnTrue()
        {
            // Act
            var canExecute = _viewModel.ClearCacheCommand.CanExecute(null);

            // Assert
            Assert.IsTrue(canExecute);
        }

        [TestMethod]
        public void LayerManagerVM_ShouldNotBeNull()
        {
            // Assert
            Assert.IsNotNull(_viewModel.LayerManagerVM);
        }

        [TestMethod]
        public void Dispose_ShouldNotThrow()
        {
            // Act & Assert (should not throw)
            _viewModel.Dispose();
        }

        [TestMethod]
        public void Dispose_ShouldDisposeLayerManagerVM()
        {
            // Arrange
            var layerManagerVM = _viewModel.LayerManagerVM;

            // Act
            _viewModel.Dispose();

            // Assert
            // LayerManagerVM should be disposed (we can't directly test this without exposing internal state)
            Assert.IsNotNull(layerManagerVM); // Just ensure it was not null before disposal
        }
    }
}
