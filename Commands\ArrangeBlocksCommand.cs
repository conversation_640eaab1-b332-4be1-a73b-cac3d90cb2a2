using Autodesk.AutoCAD.Runtime;
using IECAD.Services;

[assembly: CommandClass(typeof(IECAD.Commands.ArrangeBlocksCommand))]

namespace IECAD.Commands
{
    /// <summary>
    /// Command class for arranging blocks based on their insertion points
    /// </summary>
    public class ArrangeBlocksCommand
    {
        /// <summary>
        /// AutoCAD command to arrange selected blocks in a line
        /// </summary>
        [CommandMethod("ARRANGEBLOCKS")]
        public void ArrangeBlocks()
        {
            var service = new BlockArrangementService();
            service.ArrangeBlocks();
        }
    }
}
