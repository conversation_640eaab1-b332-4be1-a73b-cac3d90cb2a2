using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using System;
using System.Collections.Generic;

namespace IECAD.Services.OutlineWalker
{
    /// <summary>
    /// 步行者策略类型枚举
    /// </summary>
    public enum WalkerStrategy
    {
        /// <summary>
        /// 精确步行者 - 严格沿着几何对象的精确路径行走
        /// </summary>
        Precise,

        /// <summary>
        /// 容差步行者 - 能感知附近线条并桥接缝隙
        /// </summary>
        Tolerant,

        /// <summary>
        /// 形态学步行者 - 圆盘滚动追踪圆心轨迹
        /// </summary>
        Morphological,

        /// <summary>
        /// 栅格步行者 - 基于栅格化的传统方法
        /// </summary>
        Grid
    }

    /// <summary>
    /// 事件类型枚举
    /// </summary>
    public enum WalkerEventType
    {
        /// <summary>
        /// 到达顶点
        /// </summary>
        ReachVertex,

        /// <summary>
        /// 发现交点
        /// </summary>
        Intersection,

        /// <summary>
        /// 容差事件（附近感知到其他实体）
        /// </summary>
        ToleranceEvent,

        /// <summary>
        /// 形态学切换事件
        /// </summary>
        MorphologySwitch,

        /// <summary>
        /// 轮廓闭合
        /// </summary>
        OutlineClosed,

        /// <summary>
        /// 死胡同
        /// </summary>
        DeadEnd,

        /// <summary>
        /// 找到交点（精确步行者使用）
        /// </summary>
        IntersectionFound,

        /// <summary>
        /// 完成
        /// </summary>
        Completed
    }

    /// <summary>
    /// 基于几何的方向类
    /// </summary>
    public class WalkDirection
    {
        /// <summary>
        /// 方向向量（单位向量）
        /// </summary>
        public Vector3d DirectionVector { get; set; }

        /// <summary>
        /// 方向角度（弧度，相对于X轴正方向）
        /// </summary>
        public double Angle { get; set; }

        /// <summary>
        /// 方向来源的实体
        /// </summary>
        public Entity SourceEntity { get; set; }

        /// <summary>
        /// 参考点（方向的起始点）
        /// </summary>
        public Point3d ReferencePoint { get; set; }

        /// <summary>
        /// 方向类型（直线、切线、法线等）
        /// </summary>
        public DirectionType Type { get; set; }

        /// <summary>
        /// 精度容差
        /// </summary>
        public double Tolerance { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public WalkDirection()
        {
            DirectionVector = Vector3d.XAxis;
            Angle = 0.0;
            Type = DirectionType.Geometric;
            Tolerance = 1e-6;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="directionVector">方向向量</param>
        /// <param name="referencePoint">参考点</param>
        /// <param name="sourceEntity">来源实体</param>
        public WalkDirection(Vector3d directionVector, Point3d referencePoint, Entity sourceEntity = null)
        {
            DirectionVector = directionVector.GetNormal();
            Angle = Math.Atan2(DirectionVector.Y, DirectionVector.X);
            ReferencePoint = referencePoint;
            SourceEntity = sourceEntity;
            Type = DirectionType.Geometric;
            Tolerance = 1e-6;
        }

        /// <summary>
        /// 从角度创建方向
        /// </summary>
        /// <param name="angle">角度（弧度）</param>
        /// <param name="referencePoint">参考点</param>
        /// <returns>方向对象</returns>
        public static WalkDirection FromAngle(double angle, Point3d referencePoint)
        {
            var direction = new WalkDirection
            {
                Angle = angle,
                DirectionVector = new Vector3d(Math.Cos(angle), Math.Sin(angle), 0),
                ReferencePoint = referencePoint,
                Type = DirectionType.Angular
            };
            return direction;
        }

        /// <summary>
        /// 从两点创建方向
        /// </summary>
        /// <param name="from">起点</param>
        /// <param name="to">终点</param>
        /// <returns>方向对象</returns>
        public static WalkDirection FromPoints(Point3d from, Point3d to)
        {
            var vector = (to - from).GetNormal();
            return new WalkDirection(vector, from);
        }

        /// <summary>
        /// 获取相反方向
        /// </summary>
        /// <returns>相反的方向</returns>
        public WalkDirection GetOpposite()
        {
            return new WalkDirection
            {
                DirectionVector = -DirectionVector,
                Angle = Angle + Math.PI,
                ReferencePoint = ReferencePoint,
                SourceEntity = SourceEntity,
                Type = Type,
                Tolerance = Tolerance
            };
        }

        /// <summary>
        /// 计算与另一个方向的夹角
        /// </summary>
        /// <param name="other">另一个方向</param>
        /// <returns>夹角（弧度）</returns>
        public double AngleTo(WalkDirection other)
        {
            return DirectionVector.GetAngleTo(other.DirectionVector);
        }

        /// <summary>
        /// 检查是否与另一个方向平行
        /// </summary>
        /// <param name="other">另一个方向</param>
        /// <returns>是否平行</returns>
        public bool IsParallelTo(WalkDirection other)
        {
            var angle = AngleTo(other);
            return Math.Abs(angle) < Tolerance || Math.Abs(angle - Math.PI) < Tolerance;
        }

        /// <summary>
        /// 检查是否与另一个方向垂直
        /// </summary>
        /// <param name="other">另一个方向</param>
        /// <returns>是否垂直</returns>
        public bool IsPerpendicularTo(WalkDirection other)
        {
            var angle = AngleTo(other);
            return Math.Abs(angle - Math.PI / 2) < Tolerance;
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串描述</returns>
        public override string ToString()
        {
            return $"Direction: ({DirectionVector.X:F3}, {DirectionVector.Y:F3}), Angle: {Angle * 180 / Math.PI:F1}°";
        }

        // 静态属性：兼容传统8方向枚举
        /// <summary>
        /// 北方向（0°）
        /// </summary>
        public static WalkDirection North => FromAngle(Math.PI / 2, Point3d.Origin);

        /// <summary>
        /// 东北方向（45°）
        /// </summary>
        public static WalkDirection NorthEast => FromAngle(Math.PI / 4, Point3d.Origin);

        /// <summary>
        /// 东方向（90°）
        /// </summary>
        public static WalkDirection East => FromAngle(0, Point3d.Origin);

        /// <summary>
        /// 东南方向（315°）
        /// </summary>
        public static WalkDirection SouthEast => FromAngle(-Math.PI / 4, Point3d.Origin);

        /// <summary>
        /// 南方向（270°）
        /// </summary>
        public static WalkDirection South => FromAngle(-Math.PI / 2, Point3d.Origin);

        /// <summary>
        /// 西南方向（225°）
        /// </summary>
        public static WalkDirection SouthWest => FromAngle(-3 * Math.PI / 4, Point3d.Origin);

        /// <summary>
        /// 西方向（180°）
        /// </summary>
        public static WalkDirection West => FromAngle(Math.PI, Point3d.Origin);

        /// <summary>
        /// 西北方向（135°）
        /// </summary>
        public static WalkDirection NorthWest => FromAngle(3 * Math.PI / 4, Point3d.Origin);

        /// <summary>
        /// 获取传统8方向列表
        /// </summary>
        /// <returns>8个基本方向的列表</returns>
        public static List<WalkDirection> GetEightDirections()
        {
            return new List<WalkDirection>
            {
                North, NorthEast, East, SouthEast,
                South, SouthWest, West, NorthWest
            };
        }

        /// <summary>
        /// 检查是否等于另一个方向（在容差范围内）
        /// </summary>
        /// <param name="other">另一个方向</param>
        /// <returns>是否相等</returns>
        public bool EqualsDirection(WalkDirection other)
        {
            if (other == null) return false;
            return Math.Abs(AngleTo(other)) < Tolerance;
        }
    }

    /// <summary>
    /// 方向类型枚举
    /// </summary>
    public enum DirectionType
    {
        /// <summary>
        /// 几何方向（基于实体几何）
        /// </summary>
        Geometric,

        /// <summary>
        /// 角度方向（基于角度计算）
        /// </summary>
        Angular,

        /// <summary>
        /// 切线方向
        /// </summary>
        Tangent,

        /// <summary>
        /// 法线方向
        /// </summary>
        Normal,

        /// <summary>
        /// 连接方向（容差步行者使用）
        /// </summary>
        Connection,

        /// <summary>
        /// 形态学方向（形态学步行者使用）
        /// </summary>
        Morphological
    }

    /// <summary>
    /// 步行者事件结果
    /// </summary>
    public class WalkerEventResult
    {
        /// <summary>
        /// 事件类型
        /// </summary>
        public WalkerEventType EventType { get; set; }

        /// <summary>
        /// 事件发生位置
        /// </summary>
        public Point3d Position { get; set; }

        /// <summary>
        /// 交点位置 (for IntersectionFound event)
        /// </summary>
        public Point3d IntersectionPoint { get; set; }

        /// <summary>
        /// 相关实体
        /// </summary>
        public Entity RelatedEntity { get; set; }

        /// <summary>
        /// 可选择的方向列表
        /// </summary>
        public List<WalkDirection> AvailableDirections { get; set; }

        /// <summary>
        /// 来时方向
        /// </summary>
        public WalkDirection FromDirection { get; set; }

        /// <summary>
        /// 距离信息（用于容差模式）
        /// </summary>
        public double Distance { get; set; }

        /// <summary>
        /// 额外数据
        /// </summary>
        public Dictionary<string, object> AdditionalData { get; set; }

        public WalkerEventResult()
        {
            AvailableDirections = new List<WalkDirection>();
            AdditionalData = new Dictionary<string, object>();
        }
    }

    /// <summary>
    /// 方向决策结果
    /// </summary>
    public class DirectionDecision
    {
        /// <summary>
        /// 选择的方向
        /// </summary>
        public WalkDirection ChosenDirection { get; set; }

        /// <summary>
        /// 下一个目标位置
        /// </summary>
        public Point3d NextPosition { get; set; }

        /// <summary>
        /// 目标实体
        /// </summary>
        public Entity TargetEntity { get; set; }

        /// <summary>
        /// 是否需要回溯
        /// </summary>
        public bool RequiresBacktrack { get; set; }

        /// <summary>
        /// 决策理由（用于调试）
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 步长或距离
        /// </summary>
        public double StepDistance { get; set; }

        /// <summary>
        /// 额外数据
        /// </summary>
        public Dictionary<string, object> AdditionalData { get; set; }

        public DirectionDecision()
        {
            AdditionalData = new Dictionary<string, object>();
        }
    }

    /// <summary>
    /// 几何工具辅助类
    /// </summary>
    public static class GeometryHelper
    {
        /// <summary>
        /// 计算最右转方向（基于来时方向和可选方向列表）
        /// </summary>
        /// <param name="fromDirection">来时方向</param>
        /// <param name="availableDirections">可选方向列表</param>
        /// <returns>最右转的方向</returns>
        public static WalkDirection GetRightmostDirection(WalkDirection fromDirection, List<WalkDirection> availableDirections)
        {
            if (availableDirections == null || availableDirections.Count == 0)
                return fromDirection?.GetOpposite() ?? new WalkDirection();

            if (availableDirections.Count == 1)
                return availableDirections[0];

            // 获取来时方向的相反方向作为基准
            var baseDirection = fromDirection?.GetOpposite();
            if (baseDirection == null)
                return availableDirections[0];

            // 找到相对于基准方向最右（顺时针）的方向
            WalkDirection rightmostDirection = availableDirections[0];
            double minAngle = GetClockwiseAngle(baseDirection, availableDirections[0]);

            foreach (var direction in availableDirections)
            {
                double angle = GetClockwiseAngle(baseDirection, direction);
                if (angle < minAngle)
                {
                    minAngle = angle;
                    rightmostDirection = direction;
                }
            }

            return rightmostDirection;
        }

        /// <summary>
        /// 计算从基准方向到目标方向的顺时针角度
        /// </summary>
        /// <param name="baseDirection">基准方向</param>
        /// <param name="targetDirection">目标方向</param>
        /// <returns>顺时针角度（0-2π）</returns>
        public static double GetClockwiseAngle(WalkDirection baseDirection, WalkDirection targetDirection)
        {
            if (baseDirection == null || targetDirection == null)
                return 0;

            double angleDiff = targetDirection.Angle - baseDirection.Angle;
            
            // 标准化到 [0, 2π] 范围
            while (angleDiff < 0)
                angleDiff += 2 * Math.PI;
            while (angleDiff >= 2 * Math.PI)
                angleDiff -= 2 * Math.PI;

            return angleDiff;
        }

        /// <summary>
        /// 获取相反方向（兼容旧版接口）
        /// </summary>
        /// <param name="direction">原方向</param>
        /// <returns>相反的方向</returns>
        public static WalkDirection GetOppositeDirection(WalkDirection direction)
        {
            if (direction == null)
                return new WalkDirection();
            
            return direction.GetOpposite();
        }

        /// <summary>
        /// 从实体获取在指定点的切线方向
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="point">指定点</param>
        /// <returns>切线方向</returns>
        public static WalkDirection GetTangentDirection(Entity entity, Point3d point)
        {
            try
            {
                Vector3d tangent = Vector3d.XAxis;
                
                switch (entity)
                {
                    case Line line:
                        tangent = (line.EndPoint - line.StartPoint).GetNormal();
                        break;
                        
                    case Arc arc:
                        // 计算圆弧在指定点的切线
                        var center = arc.Center;
                        var radius = new Vector3d(point.X - center.X, point.Y - center.Y, 0);
                        tangent = radius.GetPerpendicularVector().GetNormal();
                        // 根据圆弧方向调整切线方向
                        // AutoCAD中检查圆弧方向的方法
                        var isClockwise = IsArcClockwise(arc);
                        if (!isClockwise)
                            tangent = -tangent;
                        break;
                        
                    case Circle circle:
                        // 圆在指定点的切线
                        var circleCenter = circle.Center;
                        var radiusVec = new Vector3d(point.X - circleCenter.X, point.Y - circleCenter.Y, 0);
                        tangent = radiusVec.GetPerpendicularVector().GetNormal();
                        break;
                        
                    case Polyline polyline:
                        // 多段线需要找到最接近的线段
                        tangent = GetPolylineTangentAt(polyline, point);
                        break;
                        
                    case Spline spline:
                        // 样条曲线的切线需要特殊处理
                        tangent = GetSplineTangentAt(spline, point);
                        break;
                        
                    default:
                        // 默认情况，尝试通过差分计算切线
                        tangent = EstimateTangentByDifference(entity, point);
                        break;
                }
                
                return new WalkDirection(tangent, point, entity)
                {
                    Type = DirectionType.Tangent
                };
            }
            catch (Exception)
            {
                // 如果无法计算切线，返回默认方向
                return new WalkDirection(Vector3d.XAxis, point, entity);
            }
        }

        /// <summary>
        /// 获取多段线在指定点的切线方向
        /// </summary>
        private static Vector3d GetPolylineTangentAt(Polyline polyline, Point3d point)
        {
            // 找到最接近指定点的线段
            double minDistance = double.MaxValue;
            Vector3d tangent = Vector3d.XAxis;
            
            for (int i = 0; i < polyline.NumberOfVertices - 1; i++)
            {
                var start = polyline.GetPoint3dAt(i);
                var end = polyline.GetPoint3dAt(i + 1);
                var segmentVector = end - start;
                
                // 计算点到线段的距离
                var pointToStart = point - start;
                var projection = pointToStart.DotProduct(segmentVector) / segmentVector.LengthSqrd;
                projection = Math.Max(0, Math.Min(1, projection));
                var closestPoint = start + segmentVector * projection;
                var distance = Distance2D(point, closestPoint);
                
                if (distance < minDistance)
                {
                    minDistance = distance;
                    tangent = segmentVector.GetNormal();
                }
            }
            
            return tangent;
        }

        /// <summary>
        /// 获取样条曲线在指定点的切线方向
        /// </summary>
        private static Vector3d GetSplineTangentAt(Spline spline, Point3d point)
        {
            try
            {
                // 尝试获取最近参数
                var param = spline.GetParameterAtPoint(point);
                var derivative = spline.GetFirstDerivative(param);
                return derivative.GetNormal();
            }
            catch
            {
                // 如果失败，使用差分方法
                return EstimateTangentByDifference(spline, point);
            }
        }

        /// <summary>
        /// 通过差分估算切线方向
        /// </summary>
        private static Vector3d EstimateTangentByDifference(Entity entity, Point3d point)
        {
            const double delta = 0.001; // 小的增量
            
            try
            {
                // 尝试在附近找两个点来计算切线
                var p1 = point + new Vector3d(delta, 0, 0);
                var p2 = point - new Vector3d(delta, 0, 0);
                
                // 这里需要更复杂的逻辑来在曲线上找到实际的点
                // 简化处理，返回水平方向
                return Vector3d.XAxis;
            }
            catch
            {
                return Vector3d.XAxis;
            }
        }

        /// <summary>
        /// 计算两点距离
        /// </summary>
        public static double Distance2D(Point3d p1, Point3d p2)
        {
            var dx = p1.X - p2.X;
            var dy = p1.Y - p2.Y;
            return Math.Sqrt(dx * dx + dy * dy);
        }

        /// <summary>
        /// 检查点是否在容差范围内
        /// </summary>
        public static bool IsWithinTolerance(Point3d p1, Point3d p2, double tolerance)
        {
            return Distance2D(p1, p2) <= tolerance;
        }

        /// <summary>
        /// 在指定实体上寻找最近的点
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="point">参考点</param>
        /// <returns>实体上最近的点</returns>
        public static Point3d GetClosestPointOnEntity(Entity entity, Point3d point)
        {
            try
            {
                switch (entity)
                {
                    case Curve curve:
                        return curve.GetClosestPointTo(point, false);
                    default:
                        return point; // 如果不是曲线，返回原点
                }
            }
            catch
            {
                return point;
            }
        }

        /// <summary>
        /// 创建连接两点的方向
        /// </summary>
        /// <param name="from">起点</param>
        /// <param name="to">终点</param>
        /// <param name="tolerance">容差</param>
        /// <returns>连接方向</returns>
        public static WalkDirection CreateConnectionDirection(Point3d from, Point3d to, double tolerance = 1e-6)
        {
            var vector = (to - from);
            if (vector.Length < tolerance)
                return new WalkDirection(Vector3d.XAxis, from) { Type = DirectionType.Connection };
                
            return new WalkDirection(vector.GetNormal(), from)
            {
                Type = DirectionType.Connection,
                Tolerance = tolerance
            };
        }

        /// <summary>
        /// 获取实体的端点方向列表
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>端点方向列表</returns>
        public static List<WalkDirection> GetEndpointDirections(Entity entity)
        {
            var directions = new List<WalkDirection>();
            
            try
            {
                switch (entity)
                {
                    case Line line:
                        directions.Add(GetTangentDirection(line, line.StartPoint));
                        directions.Add(GetTangentDirection(line, line.EndPoint));
                        break;
                        
                    case Arc arc:
                        directions.Add(GetTangentDirection(arc, arc.StartPoint));
                        directions.Add(GetTangentDirection(arc, arc.EndPoint));
                        break;
                        
                    case Polyline polyline:
                        for (int i = 0; i < polyline.NumberOfVertices; i++)
                        {
                            var point = polyline.GetPoint3dAt(i);
                            directions.Add(GetTangentDirection(polyline, point));
                        }
                        break;
                }
            }
            catch (Exception)
            {
                // 如果获取失败，至少返回一个默认方向
                if (directions.Count == 0)
                {
                    directions.Add(new WalkDirection());
                }
            }
            
            return directions;
        }

        /// <summary>
        /// 检查圆弧是否为顺时针方向
        /// </summary>
        /// <param name="arc">圆弧对象</param>
        /// <returns>是否为顺时针</returns>
        private static bool IsArcClockwise(Arc arc)
        {
            try
            {
                // 通过起始角度和结束角度判断方向
                var startAngle = arc.StartAngle;
                var endAngle = arc.EndAngle;
                
                // 标准化角度到 [0, 2π] 范围
                while (startAngle < 0) startAngle += 2 * Math.PI;
                while (endAngle < 0) endAngle += 2 * Math.PI;
                while (startAngle >= 2 * Math.PI) startAngle -= 2 * Math.PI;
                while (endAngle >= 2 * Math.PI) endAngle -= 2 * Math.PI;
                
                // 如果结束角度小于起始角度，通常表示顺时针
                // 但这只是一个简化的判断，实际情况可能更复杂
                if (Math.Abs(endAngle - startAngle) > Math.PI)
                {
                    return startAngle > endAngle;
                }
                else
                {
                    return endAngle < startAngle;
                }
            }
            catch
            {
                // 默认返回顺时针
                return true;
            }
        }
    }
}
