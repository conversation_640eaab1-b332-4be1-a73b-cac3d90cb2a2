﻿using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using IECAD.Helpers;
using IECAD.Models;
using IECAD.Services;

namespace IECAD.ViewModels
{
    public class LayerManagerViewModel : BaseViewModel
    {
        private readonly IAutoCADService _autoCADService;
        private ObservableCollection<LayerModel> _layers;
        private LayerModel _selectedLayer;
        private string _searchText;

        public ObservableCollection<LayerModel> Layers
        {
            get => _layers;
            private set => SetProperty(ref _layers, value);
        }

        public LayerModel SelectedLayer
        {
            get => _selectedLayer;
            set => SetProperty(ref _selectedLayer, value);
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    FilterLayers();
                }
            }
        }

        // Commands
        public ICommand RefreshCommand { get; private set; }
        public ICommand ToggleVisibilityCommand { get; private set; }
        public ICommand ToggleAllVisibilityCommand { get; private set; }
        public ICommand ClearSearchCommand { get; private set; }

        public LayerManagerViewModel(IAutoCADService autoCADService)
            : base(ServiceConfiguration.TryResolve<ILoggingService>(out var logging) ? logging : null,
                   ServiceConfiguration.TryResolve<IErrorHandlingService>(out var error) ? error : null)
        {
            _autoCADService = autoCADService ?? throw new ArgumentNullException(nameof(autoCADService));
            Layers = new ObservableCollection<LayerModel>();

            InitializeCommands();

            // Initialize asynchronously without blocking constructor
            _ = Task.Run(async () =>
            {
                try
                {
                    await RefreshLayersAsync();
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError($"Error during initial layer refresh: {ex.Message}", "LayerManagerViewModel");
                }
            });
        }

        private void InitializeCommands()
        {
            RefreshCommand = CreateAsyncCommand(RefreshLayersAsync, () => !IsBusy, "Refreshing layers...", "RefreshLayers");
            ToggleVisibilityCommand = CreateAsyncCommand<LayerModel>(ToggleLayerVisibilityAsync,
                layer => !IsBusy && layer != null, "Updating layer...", "ToggleVisibility");
            ToggleAllVisibilityCommand = CreateAsyncCommand(ToggleAllLayersVisibilityAsync,
                () => !IsBusy && Layers?.Any() == true, "Updating all layers...", "ToggleAllVisibility");
            ClearSearchCommand = new RelayCommand(() => SearchText = string.Empty, () => !string.IsNullOrEmpty(SearchText));
        }

        private async Task RefreshLayersAsync()
        {
            try
            {
                _loggingService?.LogInfo("Refreshing layers", "LayerManagerViewModel");

                var layers = await Task.Run(() => _autoCADService.GetLayers());

                // Update on UI thread - use safer dispatcher access
                var dispatcher = System.Windows.Application.Current?.Dispatcher ??
                                System.Windows.Threading.Dispatcher.CurrentDispatcher;

                if (dispatcher != null)
                {
                    await dispatcher.InvokeAsync(() =>
                    {
                        if (Layers != null)
                        {
                            Layers.Clear();
                            if (layers != null)
                            {
                                foreach (var layer in layers)
                                {
                                    Layers.Add(layer);
                                }
                            }
                        }
                    });
                }
                else
                {
                    // Fallback: update directly if no dispatcher available
                    if (Layers != null)
                    {
                        Layers.Clear();
                        if (layers != null)
                        {
                            foreach (var layer in layers)
                            {
                                Layers.Add(layer);
                            }
                        }
                    }
                }

                FilterLayers();
                _loggingService?.LogInfo($"Refreshed {layers?.Count ?? 0} layers", "LayerManagerViewModel");
            }
            catch (Exception ex)
            {
                HandleError(ex, "Failed to refresh layers");
            }
        }

        private async Task ToggleLayerVisibilityAsync(LayerModel layer)
        {
            if (layer == null) return;

            try
            {
                _loggingService?.LogDebug($"Toggling visibility for layer: {layer.Name}", "LayerManagerViewModel");

                bool newVisibility = !layer.IsVisible;

                await Task.Run(() => _autoCADService.SetLayerVisibility(layer.Name, newVisibility));

                // Update on UI thread - use safer dispatcher access
                var dispatcher = System.Windows.Application.Current?.Dispatcher ??
                                System.Windows.Threading.Dispatcher.CurrentDispatcher;

                if (dispatcher != null)
                {
                    await dispatcher.InvokeAsync(() =>
                    {
                        layer.IsVisible = newVisibility;
                    });
                }
                else
                {
                    // Fallback: update directly if no dispatcher available
                    layer.IsVisible = newVisibility;
                }

                _loggingService?.LogDebug($"Layer {layer.Name} visibility set to {newVisibility}", "LayerManagerViewModel");
            }
            catch (Exception ex)
            {
                HandleError(ex, $"Failed to toggle visibility for layer '{layer.Name}'");
            }
        }

        private async Task ToggleAllLayersVisibilityAsync()
        {
            if (Layers == null || !Layers.Any()) return;

            try
            {
                _loggingService?.LogInfo("Toggling visibility for all layers", "LayerManagerViewModel");

                // Determine new visibility state (if any layer is hidden, show all; if all visible, hide all)
                bool anyHidden = Layers.Any(l => !l.IsVisible);
                bool newVisibility = anyHidden;

                var tasks = Layers.Select(async layer =>
                {
                    await Task.Run(() => _autoCADService.SetLayerVisibility(layer.Name, newVisibility));
                    return layer;
                });

                var updatedLayers = await Task.WhenAll(tasks);

                // Update on UI thread - use safer dispatcher access
                var dispatcher = System.Windows.Application.Current?.Dispatcher ??
                                System.Windows.Threading.Dispatcher.CurrentDispatcher;

                if (dispatcher != null)
                {
                    await dispatcher.InvokeAsync(() =>
                    {
                        if (updatedLayers != null)
                        {
                            foreach (var layer in updatedLayers)
                            {
                                if (layer != null)
                                {
                                    layer.IsVisible = newVisibility;
                                }
                            }
                        }
                    });
                }
                else
                {
                    // Fallback: update directly if no dispatcher available
                    if (updatedLayers != null)
                    {
                        foreach (var layer in updatedLayers)
                        {
                            if (layer != null)
                            {
                                layer.IsVisible = newVisibility;
                            }
                        }
                    }
                }

                string action = newVisibility ? "shown" : "hidden";
                ShowSuccess($"All layers {action} successfully");
                _loggingService?.LogInfo($"All layers {action}", "LayerManagerViewModel");
            }
            catch (Exception ex)
            {
                HandleError(ex, "Failed to toggle visibility for all layers");
            }
        }

        private void FilterLayers()
        {
            try
            {
                if (Layers == null) return;

                if (string.IsNullOrWhiteSpace(SearchText))
                {
                    // Show all layers
                    foreach (var layer in Layers)
                    {
                        // In a real implementation, you might use a CollectionView for filtering
                        // For now, we'll just log the filter action
                    }
                }
                else
                {
                    // Filter layers by search text
                    // In a real implementation, you would use CollectionViewSource.GetDefaultView()
                    _loggingService?.LogDebug($"Filtering layers by: {SearchText}", "LayerManagerViewModel");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error filtering layers: {ex.Message}", "LayerManagerViewModel");
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                Layers?.Clear();
            }
            base.Dispose(disposing);
        }
    }
}