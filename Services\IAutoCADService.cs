using System.Collections.Generic;
using IECAD.Models;

namespace IECAD.Services
{
    /// <summary>
    /// Interface for AutoCAD service operations
    /// </summary>
    public interface IAutoCADService
    {
        /// <summary>
        /// Get all layers from the current drawing
        /// </summary>
        /// <returns>List of layer models</returns>
        List<LayerModel> GetLayers();

        /// <summary>
        /// Set the visibility of a specific layer
        /// </summary>
        /// <param name="layerName">Name of the layer</param>
        /// <param name="isVisible">Whether the layer should be visible</param>
        void SetLayerVisibility(string layerName, bool isVisible);

        /// <summary>
        /// Execute an AutoCAD command
        /// </summary>
        /// <param name="command">The command to execute</param>
        void ExecuteCommand(string command);

        /// <summary>
        /// Check if AutoCAD is available and a document is active
        /// </summary>
        /// <returns>True if AutoCAD is ready for operations</returns>
        bool IsAutoCADReady();

        /// <summary>
        /// Get the current active document name
        /// </summary>
        /// <returns>The name of the active document, or null if none</returns>
        string GetActiveDocumentName();
    }
}
