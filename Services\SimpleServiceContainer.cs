using System;
using System.Collections.Generic;

namespace IECAD.Services
{
    /// <summary>
    /// Simple service container implementation for dependency injection
    /// </summary>
    public class SimpleServiceContainer : IServiceContainer, IDisposable
    {
        private readonly Dictionary<Type, ServiceDescriptor> _services = new Dictionary<Type, ServiceDescriptor>();
        private readonly Dictionary<Type, object> _singletonInstances = new Dictionary<Type, object>();
        private bool _disposed = false;

        public void RegisterSingleton<TInterface, TImplementation>()
            where TImplementation : class, TInterface
            where TInterface : class
        {
            _services[typeof(TInterface)] = new ServiceDescriptor
            {
                ServiceType = typeof(TInterface),
                ImplementationType = typeof(TImplementation),
                Lifetime = ServiceLifetime.Singleton
            };
        }

        public void RegisterSingleton<TInterface>(TInterface instance)
            where TInterface : class
        {
            if (instance == null) throw new ArgumentNullException(nameof(instance));

            _services[typeof(TInterface)] = new ServiceDescriptor
            {
                ServiceType = typeof(TInterface),
                ImplementationType = instance.GetType(),
                Lifetime = ServiceLifetime.Singleton
            };

            _singletonInstances[typeof(TInterface)] = instance;
        }

        public void RegisterTransient<TInterface, TImplementation>()
            where TImplementation : class, TInterface
            where TInterface : class
        {
            _services[typeof(TInterface)] = new ServiceDescriptor
            {
                ServiceType = typeof(TInterface),
                ImplementationType = typeof(TImplementation),
                Lifetime = ServiceLifetime.Transient
            };
        }

        public T Resolve<T>() where T : class
        {
            if (TryResolve<T>(out T service))
            {
                return service;
            }

            throw new InvalidOperationException($"Service of type {typeof(T).Name} is not registered.");
        }

        public bool TryResolve<T>(out T service) where T : class
        {
            service = null;

            if (!_services.TryGetValue(typeof(T), out ServiceDescriptor descriptor))
            {
                return false;
            }

            try
            {
                if (descriptor.Lifetime == ServiceLifetime.Singleton)
                {
                    if (_singletonInstances.TryGetValue(typeof(T), out object instance))
                    {
                        service = (T)instance;
                        return true;
                    }

                    // Create singleton instance
                    service = (T)CreateInstance(descriptor.ImplementationType);
                    _singletonInstances[typeof(T)] = service;
                    return true;
                }
                else
                {
                    // Create transient instance
                    service = (T)CreateInstance(descriptor.ImplementationType);
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        public bool IsRegistered<T>() where T : class
        {
            return _services.ContainsKey(typeof(T));
        }

        private object CreateInstance(Type type)
        {
            // Simple constructor injection - find constructor with most parameters that we can resolve
            var constructors = type.GetConstructors();
            
            foreach (var constructor in constructors)
            {
                var parameters = constructor.GetParameters();
                var args = new object[parameters.Length];
                bool canResolveAll = true;

                for (int i = 0; i < parameters.Length; i++)
                {
                    var paramType = parameters[i].ParameterType;
                    if (_services.ContainsKey(paramType))
                    {
                        // Recursively resolve dependencies
                        var method = typeof(SimpleServiceContainer).GetMethod(nameof(TryResolve));
                        var genericMethod = method.MakeGenericMethod(paramType);
                        var resolveArgs = new object[] { null };
                        
                        if ((bool)genericMethod.Invoke(this, resolveArgs))
                        {
                            args[i] = resolveArgs[0];
                        }
                        else
                        {
                            canResolveAll = false;
                            break;
                        }
                    }
                    else
                    {
                        canResolveAll = false;
                        break;
                    }
                }

                if (canResolveAll)
                {
                    return Activator.CreateInstance(type, args);
                }
            }

            // Fallback to parameterless constructor
            return Activator.CreateInstance(type);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // Dispose singleton instances that implement IDisposable
                foreach (var instance in _singletonInstances.Values)
                {
                    if (instance is IDisposable disposable)
                    {
                        try
                        {
                            disposable.Dispose();
                        }
                        catch
                        {
                            // Ignore disposal errors
                        }
                    }
                }

                _singletonInstances.Clear();
                _services.Clear();
                _disposed = true;
            }
        }

        private class ServiceDescriptor
        {
            public Type ServiceType { get; set; }
            public Type ImplementationType { get; set; }
            public ServiceLifetime Lifetime { get; set; }
        }

        private enum ServiceLifetime
        {
            Singleton,
            Transient
        }
    }
}
