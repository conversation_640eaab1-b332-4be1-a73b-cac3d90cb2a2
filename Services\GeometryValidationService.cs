using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using System;
using System.Collections.Generic;
using System.Linq;

namespace IECAD.Services
{
    /// <summary>
    /// 几何验证服务 - 提供几何数据验证和错误处理功能
    /// </summary>
    public class GeometryValidationService : IDisposable
    {
        private readonly double _tolerance;
        private bool _disposed = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="tolerance">验证容差</param>
        public GeometryValidationService(double tolerance = 1e-6)
        {
            _tolerance = tolerance;
        }

        /// <summary>
        /// 验证实体集合是否适合进行几何运算
        /// </summary>
        /// <param name="entities">实体集合</param>
        /// <returns>验证结果</returns>
        public GeometryValidationResult ValidateEntities(IEnumerable<Entity> entities)
        {
            var result = new GeometryValidationResult();
            
            if (entities == null)
            {
                result.IsValid = false;
                result.ErrorMessage = "实体集合为空";
                return result;
            }

            var entityList = entities.ToList();
            if (entityList.Count == 0)
            {
                result.IsValid = false;
                result.ErrorMessage = "没有选择任何实体";
                return result;
            }

            var validEntities = new List<Entity>();
            var invalidEntities = new List<string>();

            foreach (var entity in entityList)
            {
                var entityValidation = ValidateEntity(entity);
                if (entityValidation.IsValid)
                {
                    validEntities.Add(entity);
                }
                else
                {
                    invalidEntities.Add($"{entity.GetType().Name}: {entityValidation.ErrorMessage}");
                }
            }

            result.ValidEntities = validEntities;
            result.InvalidEntityMessages = invalidEntities;

            if (validEntities.Count == 0)
            {
                result.IsValid = false;
                result.ErrorMessage = "没有有效的实体可用于几何运算";
            }
            else if (validEntities.Count < entityList.Count)
            {
                result.IsValid = true;
                result.WarningMessage = $"已忽略 {invalidEntities.Count} 个无效实体";
            }
            else
            {
                result.IsValid = true;
            }

            return result;
        }

        /// <summary>
        /// 验证单个实体
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>验证结果</returns>
        public GeometryValidationResult ValidateEntity(Entity entity)
        {
            var result = new GeometryValidationResult();

            if (entity == null)
            {
                result.IsValid = false;
                result.ErrorMessage = "实体为空";
                return result;
            }

            try
            {
                // 检查实体类型是否支持
                if (!IsSupportedEntityType(entity))
                {
                    result.IsValid = false;
                    result.ErrorMessage = $"不支持的实体类型: {entity.GetType().Name}";
                    return result;
                }

                // 检查实体几何有效性
                var geometryValidation = ValidateEntityGeometry(entity);
                if (!geometryValidation.IsValid)
                {
                    result.IsValid = false;
                    result.ErrorMessage = geometryValidation.ErrorMessage;
                    return result;
                }

                result.IsValid = true;
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ErrorMessage = $"实体验证异常: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 验证点集合
        /// </summary>
        /// <param name="points">点集合</param>
        /// <returns>验证结果</returns>
        public GeometryValidationResult ValidatePoints(IEnumerable<Point3d> points)
        {
            var result = new GeometryValidationResult();

            if (points == null)
            {
                result.IsValid = false;
                result.ErrorMessage = "点集合为空";
                return result;
            }

            var pointList = points.ToList();
            if (pointList.Count < 3)
            {
                result.IsValid = false;
                result.ErrorMessage = "至少需要3个点才能形成有效轮廓";
                return result;
            }

            // 检查是否有重复点
            var uniquePoints = RemoveDuplicatePoints(pointList);
            if (uniquePoints.Count < 3)
            {
                result.IsValid = false;
                result.ErrorMessage = "去除重复点后，点数不足3个";
                return result;
            }

            // 检查是否所有点都在同一直线上
            if (ArePointsCollinear(uniquePoints))
            {
                result.IsValid = false;
                result.ErrorMessage = "所有点都在同一直线上，无法形成面积";
                return result;
            }

            result.IsValid = true;
            result.ValidPoints = uniquePoints;
            
            if (uniquePoints.Count < pointList.Count)
            {
                result.WarningMessage = $"已去除 {pointList.Count - uniquePoints.Count} 个重复点";
            }

            return result;
        }

        /// <summary>
        /// 检查实体类型是否支持
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>是否支持</returns>
        private bool IsSupportedEntityType(Entity entity)
        {
            return entity is Line ||
                   entity is Arc ||
                   entity is Circle ||
                   entity is Polyline ||
                   entity is Spline ||
                   entity is Ellipse ||
                   entity is Region;
        }

        /// <summary>
        /// 验证实体几何有效性
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>验证结果</returns>
        private GeometryValidationResult ValidateEntityGeometry(Entity entity)
        {
            var result = new GeometryValidationResult();

            try
            {
                switch (entity)
                {
                    case Line line:
                        if (line.StartPoint.DistanceTo(line.EndPoint) < _tolerance)
                        {
                            result.IsValid = false;
                            result.ErrorMessage = "直线长度过短";
                        }
                        else
                        {
                            result.IsValid = true;
                        }
                        break;

                    case Arc arc:
                        if (arc.Radius < _tolerance)
                        {
                            result.IsValid = false;
                            result.ErrorMessage = "圆弧半径过小";
                        }
                        else if (Math.Abs(arc.EndAngle - arc.StartAngle) < _tolerance)
                        {
                            result.IsValid = false;
                            result.ErrorMessage = "圆弧角度过小";
                        }
                        else
                        {
                            result.IsValid = true;
                        }
                        break;

                    case Circle circle:
                        if (circle.Radius < _tolerance)
                        {
                            result.IsValid = false;
                            result.ErrorMessage = "圆半径过小";
                        }
                        else
                        {
                            result.IsValid = true;
                        }
                        break;

                    case Polyline polyline:
                        if (polyline.NumberOfVertices < 2)
                        {
                            result.IsValid = false;
                            result.ErrorMessage = "多段线顶点数不足";
                        }
                        else
                        {
                            result.IsValid = true;
                        }
                        break;

                    default:
                        // 对于其他类型，尝试获取边界框来验证
                        var extents = entity.GeometricExtents;
                        var size = extents.MaxPoint - extents.MinPoint;
                        if (size.Length < _tolerance)
                        {
                            result.IsValid = false;
                            result.ErrorMessage = "实体尺寸过小";
                        }
                        else
                        {
                            result.IsValid = true;
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ErrorMessage = $"几何验证异常: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 去除重复点
        /// </summary>
        /// <param name="points">点集合</param>
        /// <returns>去重后的点集合</returns>
        private List<Point3d> RemoveDuplicatePoints(List<Point3d> points)
        {
            var uniquePoints = new List<Point3d>();

            foreach (var point in points)
            {
                bool isDuplicate = false;
                foreach (var existing in uniquePoints)
                {
                    if (point.DistanceTo(existing) < _tolerance)
                    {
                        isDuplicate = true;
                        break;
                    }
                }

                if (!isDuplicate)
                {
                    uniquePoints.Add(point);
                }
            }

            return uniquePoints;
        }

        /// <summary>
        /// 检查点是否共线
        /// </summary>
        /// <param name="points">点集合</param>
        /// <returns>是否共线</returns>
        private bool ArePointsCollinear(List<Point3d> points)
        {
            if (points.Count < 3) return true;

            var p1 = points[0];
            var p2 = points[1];

            for (int i = 2; i < points.Count; i++)
            {
                var p3 = points[i];
                
                // 计算三点构成的三角形面积
                var area = Math.Abs((p2.X - p1.X) * (p3.Y - p1.Y) - (p3.X - p1.X) * (p2.Y - p1.Y)) / 2.0;
                
                if (area > _tolerance)
                {
                    return false; // 找到不共线的点
                }
            }

            return true; // 所有点都共线
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // 释放托管资源（当前没有需要释放的托管资源）
                }

                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 几何验证结果
    /// </summary>
    public class GeometryValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 警告消息
        /// </summary>
        public string WarningMessage { get; set; }

        /// <summary>
        /// 有效的实体集合
        /// </summary>
        public List<Entity> ValidEntities { get; set; }

        /// <summary>
        /// 有效的点集合
        /// </summary>
        public List<Point3d> ValidPoints { get; set; }

        /// <summary>
        /// 无效实体的错误消息
        /// </summary>
        public List<string> InvalidEntityMessages { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public GeometryValidationResult()
        {
            ValidEntities = new List<Entity>();
            ValidPoints = new List<Point3d>();
            InvalidEntityMessages = new List<string>();
        }
    }
}
