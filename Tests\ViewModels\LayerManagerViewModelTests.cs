using System.Collections.Generic;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using IECAD.Models;
using IECAD.ViewModels;
using IECAD.Tests.TestHelpers;

namespace IECAD.Tests.ViewModels
{
    [TestClass]
    public class LayerManagerViewModelTests : TestBase
    {
        private LayerManagerViewModel _viewModel;
        private List<LayerModel> _testLayers;

        [TestInitialize]
        public override void TestInitialize()
        {
            base.TestInitialize();
            SetupTestServiceConfiguration();
            
            _testLayers = new List<LayerModel>
            {
                new LayerModel { Name = "Layer1", IsVisible = true },
                new LayerModel { Name = "Layer2", IsVisible = false },
                new LayerModel { Name = "TestLayer", IsVisible = true }
            };

            MockAutoCADService.Setup(x => x.GetLayers()).Returns(_testLayers);
            
            _viewModel = new LayerManagerViewModel(MockAutoCADService.Object);
        }

        [TestCleanup]
        public override void TestCleanup()
        {
            _viewModel?.Dispose();
            base.TestCleanup();
        }

        [TestMethod]
        public void Constructor_ShouldInitializeProperties()
        {
            // Assert
            Assert.IsNotNull(_viewModel.Layers);
            Assert.IsNotNull(_viewModel.RefreshCommand);
            Assert.IsNotNull(_viewModel.ToggleVisibilityCommand);
            Assert.IsNotNull(_viewModel.ToggleAllVisibilityCommand);
            Assert.IsNotNull(_viewModel.ClearSearchCommand);
        }

        [TestMethod]
        public void Constructor_ShouldLoadInitialLayers()
        {
            // Assert
            Assert.AreEqual(_testLayers.Count, _viewModel.Layers.Count);
            AssertMethodCalled(MockAutoCADService, x => x.GetLayers());
        }

        [TestMethod]
        public void SearchText_WhenSet_ShouldRaisePropertyChanged()
        {
            // Arrange
            bool propertyChangedRaised = false;
            _viewModel.PropertyChanged += (sender, e) =>
            {
                if (e.PropertyName == nameof(_viewModel.SearchText))
                    propertyChangedRaised = true;
            };

            // Act
            _viewModel.SearchText = "test";

            // Assert
            Assert.IsTrue(propertyChangedRaised);
            Assert.AreEqual("test", _viewModel.SearchText);
        }

        [TestMethod]
        public void SelectedLayer_WhenSet_ShouldRaisePropertyChanged()
        {
            // Arrange
            var testLayer = _testLayers.First();
            bool propertyChangedRaised = false;
            _viewModel.PropertyChanged += (sender, e) =>
            {
                if (e.PropertyName == nameof(_viewModel.SelectedLayer))
                    propertyChangedRaised = true;
            };

            // Act
            _viewModel.SelectedLayer = testLayer;

            // Assert
            Assert.IsTrue(propertyChangedRaised);
            Assert.AreEqual(testLayer, _viewModel.SelectedLayer);
        }

        [TestMethod]
        public void RefreshCommand_CanExecute_WhenNotBusy_ShouldReturnTrue()
        {
            // Act
            var canExecute = _viewModel.RefreshCommand.CanExecute(null);

            // Assert
            Assert.IsTrue(canExecute);
        }

        [TestMethod]
        public void ToggleVisibilityCommand_CanExecute_WithValidLayer_ShouldReturnTrue()
        {
            // Arrange
            var testLayer = _testLayers.First();

            // Act
            var canExecute = _viewModel.ToggleVisibilityCommand.CanExecute(testLayer);

            // Assert
            Assert.IsTrue(canExecute);
        }

        [TestMethod]
        public void ToggleVisibilityCommand_CanExecute_WithNullLayer_ShouldReturnFalse()
        {
            // Act
            var canExecute = _viewModel.ToggleVisibilityCommand.CanExecute(null);

            // Assert
            Assert.IsFalse(canExecute);
        }

        [TestMethod]
        public void ClearSearchCommand_CanExecute_WithSearchText_ShouldReturnTrue()
        {
            // Arrange
            _viewModel.SearchText = "test";

            // Act
            var canExecute = _viewModel.ClearSearchCommand.CanExecute(null);

            // Assert
            Assert.IsTrue(canExecute);
        }

        [TestMethod]
        public void ClearSearchCommand_CanExecute_WithoutSearchText_ShouldReturnFalse()
        {
            // Arrange
            _viewModel.SearchText = string.Empty;

            // Act
            var canExecute = _viewModel.ClearSearchCommand.CanExecute(null);

            // Assert
            Assert.IsFalse(canExecute);
        }

        [TestMethod]
        public void ClearSearchCommand_Execute_ShouldClearSearchText()
        {
            // Arrange
            _viewModel.SearchText = "test";

            // Act
            _viewModel.ClearSearchCommand.Execute(null);

            // Assert
            Assert.AreEqual(string.Empty, _viewModel.SearchText);
        }

        [TestMethod]
        public void Dispose_ShouldNotThrow()
        {
            // Act & Assert (should not throw)
            _viewModel.Dispose();
        }
    }
}
