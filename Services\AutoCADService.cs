﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using System.Collections.Generic;
using System;
using IECAD.Models;
using IECAD.Helpers;

namespace IECAD.Services
{
    public class AutoCADService : IAutoCADService, IDisposable
    {
        private readonly ILoggingService _loggingService;
        private readonly IErrorHandlingService _errorHandlingService;
        private readonly AutoCADObjectCache _cache;
        private bool _disposed = false;

        public AutoCADService(ILoggingService loggingService, IErrorHandlingService errorHandlingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _errorHandlingService = errorHandlingService ?? throw new ArgumentNullException(nameof(errorHandlingService));
            _cache = new AutoCADObjectCache(_loggingService);
        }
        public List<LayerModel> GetLayers()
        {
            using (var memoryScope = MemoryManager.CreateMemoryMonitoringScope("GetLayers"))
            {
                _loggingService.LogDebug("Getting layers from current drawing", "AutoCADService");

                // Use cache to avoid repeated database queries
                return _cache.GetOrCreate("layers", () =>
                {
                    var layers = new List<LayerModel>();

                    if (!IsAutoCADReady())
                    {
                        _errorHandlingService.HandleError("AutoCAD is not ready or no active document", "AutoCADService");
                        return layers;
                    }

                    // Use optimized transaction helper
                    layers = AutoCADTransactionHelper.ExecuteInTransaction((trans, db) =>
                    {
                        var layerList = new List<LayerModel>();
                        LayerTable lt = trans.GetObject(db.LayerTableId, OpenMode.ForRead) as LayerTable;

                        foreach (ObjectId layerId in lt)
                        {
                            LayerTableRecord ltr = trans.GetObject(layerId, OpenMode.ForRead) as LayerTableRecord;
                            layerList.Add(new LayerModel
                            {
                                Name = ltr.Name,
                                IsVisible = !ltr.IsOff
                            });
                        }

                        return layerList;
                    }, new List<LayerModel>(), _loggingService, _errorHandlingService);

                    _loggingService.LogDebug($"Retrieved {layers.Count} layers", "AutoCADService");
                    return layers;
                }, TimeSpan.FromMinutes(2)); // Cache for 2 minutes
            }
        }

        public void SetLayerVisibility(string layerName, bool isVisible)
        {
            _loggingService.LogDebug($"Setting layer '{layerName}' visibility to {isVisible}", "AutoCADService");

            if (!IsAutoCADReady())
            {
                _errorHandlingService.HandleError("AutoCAD is not ready or no active document", "AutoCADService");
                return;
            }

            if (string.IsNullOrWhiteSpace(layerName))
            {
                _errorHandlingService.HandleError("Layer name cannot be empty", "AutoCADService");
                return;
            }

            // Use optimized transaction helper
            bool success = AutoCADTransactionHelper.ExecuteInTransaction((trans, db) =>
            {
                LayerTable lt = trans.GetObject(db.LayerTableId, OpenMode.ForRead) as LayerTable;
                if (lt.Has(layerName))
                {
                    LayerTableRecord ltr = trans.GetObject(lt[layerName], OpenMode.ForWrite) as LayerTableRecord;
                    ltr.IsOff = !isVisible;
                    _loggingService.LogDebug($"Successfully set layer '{layerName}' visibility", "AutoCADService");
                }
                else
                {
                    _errorHandlingService.HandleWarning($"Layer '{layerName}' not found", "AutoCADService");
                }
            }, _loggingService, _errorHandlingService);

            // Invalidate layer cache since visibility changed
            if (success)
            {
                _cache.Remove("layers");
            }
        }

        public void ExecuteCommand(string command)
        {
            _loggingService.LogDebug($"Executing AutoCAD command: {command}", "AutoCADService");

            if (!IsAutoCADReady())
            {
                _errorHandlingService.HandleError("AutoCAD is not ready or no active document", "AutoCADService");
                return;
            }

            if (string.IsNullOrWhiteSpace(command))
            {
                _errorHandlingService.HandleError("Command cannot be empty", "AutoCADService");
                return;
            }

            try
            {
                Document activeDocument = AutoCADTransactionHelper.GetActiveDocument();
                if (activeDocument != null)
                {
                    activeDocument.SendStringToExecute($"{command} ", true, false, false);
                    _loggingService.LogDebug($"Successfully executed command: {command}", "AutoCADService");

                    // Clear cache after command execution as it might have changed the drawing
                    _cache.Clear();
                }
                else
                {
                    _errorHandlingService.HandleError("Failed to get active document", "AutoCADService");
                }
            }
            catch (Exception ex)
            {
                _errorHandlingService.HandleException(ex, $"Failed to execute command: {command}", "AutoCADService");
            }
        }

        public bool IsAutoCADReady()
        {
            return AutoCADTransactionHelper.IsAutoCADReady();
        }

        public string GetActiveDocumentName()
        {
            try
            {
                var activeDoc = AutoCADTransactionHelper.GetActiveDocument();
                return activeDoc?.Name;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Failed to get active document name: {ex.Message}", "AutoCADService");
                return null;
            }
        }

        /// <summary>
        /// Clear the internal cache (useful when drawing changes significantly)
        /// </summary>
        public void ClearCache()
        {
            _cache.Clear();
            _loggingService.LogDebug("AutoCAD service cache cleared", "AutoCADService");
        }

        /// <summary>
        /// Get cache statistics for monitoring
        /// </summary>
        /// <returns>Cache statistics</returns>
        public CacheStatistics GetCacheStatistics()
        {
            return _cache.GetStatistics();
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _cache?.Dispose();
                _disposed = true;
            }
        }

        // Static method for backward compatibility - will be deprecated
        public static void ExecuteAutoCADCommand(string command)
        {
            try
            {
                // Get current active document without checking availability
                Document activeDocument = Application.DocumentManager.MdiActiveDocument;
                // Execute AutoCAD command directly
                activeDocument.SendStringToExecute($"{command} ", true, false, false);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to execute command {command}: {ex.Message}");
            }
        }
    }

}