; AutoCAD Script to demonstrate Block Arrangement feature
; This script creates test blocks and demonstrates the arrangement functionality

; Clear the drawing
ERASE ALL 

; Create a simple circle for our test block
CIRCLE 0,0 100

; Create a block definition
BLOCK TestBlock 0,0 LAST 

; Insert multiple instances of the block at random positions
INSERT TestBlock 1000,500 1 1 0
INSERT TestBlock 2500,1200 1 1 0
INSERT TestBlock 800,1800 1 1 0
INSERT TestBlock 3200,800 1 1 0
INSERT TestBlock 1800,2000 1 1 0

; Zoom to show all blocks
ZOOM EXTENTS

; Add text instructions
TEXT 500,2500 200 0 "Demo: Block Arrangement Feature"
TEXT 500,2200 150 0 "1. Type ARRANGEBLOCKS or use sidebar button"
TEXT 500,1950 150 0 "2. Select all blocks (or use window selection)"
TEXT 500,1700 150 0 "3. Choose reference block"
TEXT 500,1450 150 0 "4. Enter spacing distance (try 1500)"
TEXT 500,1200 150 0 "5. Blocks will arrange automatically!"

; Save the demonstration file
QSAVE demo_blocks_arrangement.dwg
