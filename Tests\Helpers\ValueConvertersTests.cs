using System;
using System.Globalization;
using System.Windows.Media;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using IECAD.Helpers;
using IECAD.Tests.TestHelpers;

namespace IECAD.Tests.Helpers
{
    [TestClass]
    public class ValueConvertersTests : TestBase
    {
        [TestMethod]
        public void BooleanToColorConverter_WithTrue_ShouldReturnGreen()
        {
            // Arrange
            var converter = new BooleanToColorConverter();

            // Act
            var result = converter.Convert(true, typeof(SolidColorBrush), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.IsInstanceOfType(result, typeof(SolidColorBrush));
            var brush = (SolidColorBrush)result;
            Assert.AreEqual(Colors.Green, brush.Color);
        }

        [TestMethod]
        public void BooleanToColorConverter_WithFalse_ShouldReturnRed()
        {
            // Arrange
            var converter = new BooleanToColorConverter();

            // Act
            var result = converter.Convert(false, typeof(SolidColorBrush), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.IsInstanceOfType(result, typeof(SolidColorBrush));
            var brush = (SolidColorBrush)result;
            Assert.AreEqual(Colors.Red, brush.Color);
        }

        [TestMethod]
        public void BooleanToColorConverter_WithNonBoolean_ShouldReturnGray()
        {
            // Arrange
            var converter = new BooleanToColorConverter();

            // Act
            var result = converter.Convert("not a boolean", typeof(SolidColorBrush), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.IsInstanceOfType(result, typeof(SolidColorBrush));
            var brush = (SolidColorBrush)result;
            Assert.AreEqual(Colors.Gray, brush.Color);
        }

        [TestMethod]
        public void BooleanToVisibilityIconConverter_WithTrue_ShouldReturnEyeIcon()
        {
            // Arrange
            var converter = new BooleanToVisibilityIconConverter();

            // Act
            var result = converter.Convert(true, typeof(string), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual("👁️", result);
        }

        [TestMethod]
        public void BooleanToVisibilityIconConverter_WithFalse_ShouldReturnBlockIcon()
        {
            // Arrange
            var converter = new BooleanToVisibilityIconConverter();

            // Act
            var result = converter.Convert(false, typeof(string), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual("🚫", result);
        }

        [TestMethod]
        public void StringToBooleanConverter_WithNonEmptyString_ShouldReturnTrue()
        {
            // Arrange
            var converter = new StringToBooleanConverter();

            // Act
            var result = converter.Convert("test string", typeof(bool), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual(true, result);
        }

        [TestMethod]
        public void StringToBooleanConverter_WithEmptyString_ShouldReturnFalse()
        {
            // Arrange
            var converter = new StringToBooleanConverter();

            // Act
            var result = converter.Convert(string.Empty, typeof(bool), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual(false, result);
        }

        [TestMethod]
        public void StringToBooleanConverter_WithNull_ShouldReturnFalse()
        {
            // Arrange
            var converter = new StringToBooleanConverter();

            // Act
            var result = converter.Convert(null, typeof(bool), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual(false, result);
        }

        [TestMethod]
        public void InverseBooleanConverter_WithTrue_ShouldReturnFalse()
        {
            // Arrange
            var converter = new InverseBooleanConverter();

            // Act
            var result = converter.Convert(true, typeof(bool), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual(false, result);
        }

        [TestMethod]
        public void InverseBooleanConverter_WithFalse_ShouldReturnTrue()
        {
            // Arrange
            var converter = new InverseBooleanConverter();

            // Act
            var result = converter.Convert(false, typeof(bool), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual(true, result);
        }

        [TestMethod]
        public void InverseBooleanConverter_ConvertBack_ShouldInvertValue()
        {
            // Arrange
            var converter = new InverseBooleanConverter();

            // Act
            var result = converter.ConvertBack(true, typeof(bool), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual(false, result);
        }

        [TestMethod]
        public void TimeSpanToStringConverter_WithDays_ShouldReturnDaysFormat()
        {
            // Arrange
            var converter = new TimeSpanToStringConverter();
            var timeSpan = new TimeSpan(2, 3, 4, 5);

            // Act
            var result = converter.Convert(timeSpan, typeof(string), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual("2d 3h 4m", result);
        }

        [TestMethod]
        public void TimeSpanToStringConverter_WithHours_ShouldReturnHoursFormat()
        {
            // Arrange
            var converter = new TimeSpanToStringConverter();
            var timeSpan = new TimeSpan(0, 2, 3, 4);

            // Act
            var result = converter.Convert(timeSpan, typeof(string), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual("2h 3m 4s", result);
        }

        [TestMethod]
        public void TimeSpanToStringConverter_WithMinutes_ShouldReturnMinutesFormat()
        {
            // Arrange
            var converter = new TimeSpanToStringConverter();
            var timeSpan = new TimeSpan(0, 0, 2, 3);

            // Act
            var result = converter.Convert(timeSpan, typeof(string), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual("2m 3s", result);
        }

        [TestMethod]
        public void TimeSpanToStringConverter_WithSeconds_ShouldReturnSecondsFormat()
        {
            // Arrange
            var converter = new TimeSpanToStringConverter();
            var timeSpan = new TimeSpan(0, 0, 0, 2, 500);

            // Act
            var result = converter.Convert(timeSpan, typeof(string), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual("2.500s", result);
        }

        [TestMethod]
        public void TimeSpanToStringConverter_WithMilliseconds_ShouldReturnMillisecondsFormat()
        {
            // Arrange
            var converter = new TimeSpanToStringConverter();
            var timeSpan = new TimeSpan(0, 0, 0, 0, 500);

            // Act
            var result = converter.Convert(timeSpan, typeof(string), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual("500ms", result);
        }

        [TestMethod]
        public void MultiBooleanConverter_WithAndOperation_AllTrue_ShouldReturnTrue()
        {
            // Arrange
            var converter = new MultiBooleanConverter();
            var values = new object[] { true, true, true };

            // Act
            var result = converter.Convert(values, typeof(bool), "AND", CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual(true, result);
        }

        [TestMethod]
        public void MultiBooleanConverter_WithAndOperation_OneFalse_ShouldReturnFalse()
        {
            // Arrange
            var converter = new MultiBooleanConverter();
            var values = new object[] { true, false, true };

            // Act
            var result = converter.Convert(values, typeof(bool), "AND", CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual(false, result);
        }

        [TestMethod]
        public void MultiBooleanConverter_WithOrOperation_OneTrue_ShouldReturnTrue()
        {
            // Arrange
            var converter = new MultiBooleanConverter();
            var values = new object[] { false, true, false };

            // Act
            var result = converter.Convert(values, typeof(bool), "OR", CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual(true, result);
        }

        [TestMethod]
        public void MultiBooleanConverter_WithOrOperation_AllFalse_ShouldReturnFalse()
        {
            // Arrange
            var converter = new MultiBooleanConverter();
            var values = new object[] { false, false, false };

            // Act
            var result = converter.Convert(values, typeof(bool), "OR", CultureInfo.InvariantCulture);

            // Assert
            Assert.AreEqual(false, result);
        }
    }
}
