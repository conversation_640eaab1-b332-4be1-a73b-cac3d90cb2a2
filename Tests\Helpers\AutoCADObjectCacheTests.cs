using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using IECAD.Helpers;
using IECAD.Tests.TestHelpers;

namespace IECAD.Tests.Helpers
{
    [TestClass]
    public class AutoCADObjectCacheTests : TestBase
    {
        private AutoCADObjectCache _cache;

        [TestInitialize]
        public override void TestInitialize()
        {
            base.TestInitialize();
            _cache = new AutoCADObjectCache(MockLoggingService.Object);
        }

        [TestCleanup]
        public override void TestCleanup()
        {
            _cache?.Dispose();
            base.TestCleanup();
        }

        [TestMethod]
        public void GetOrCreate_WithNewKey_ShouldCreateAndReturnObject()
        {
            // Arrange
            const string key = "testKey";
            const string expectedValue = "testValue";

            // Act
            var result = _cache.GetOrCreate(key, () => expectedValue);

            // Assert
            Assert.AreEqual(expectedValue, result);
        }

        [TestMethod]
        public void GetOrCreate_WithExistingKey_ShouldReturnCachedObject()
        {
            // Arrange
            const string key = "testKey";
            const string expectedValue = "testValue";
            _cache.GetOrCreate(key, () => expectedValue);

            // Act
            var result = _cache.GetOrCreate(key, () => "differentValue");

            // Assert
            Assert.AreEqual(expectedValue, result);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void GetOrCreate_WithNullKey_ShouldThrowArgumentNullException()
        {
            // Act
            _cache.GetOrCreate<string>(null, () => "value");
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void GetOrCreate_WithNullFactory_ShouldThrowArgumentNullException()
        {
            // Act
            _cache.GetOrCreate<string>("key", null);
        }

        [TestMethod]
        public void Remove_ExistingKey_ShouldReturnTrue()
        {
            // Arrange
            const string key = "testKey";
            _cache.GetOrCreate(key, () => "value");

            // Act
            var result = _cache.Remove(key);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void Remove_NonExistingKey_ShouldReturnFalse()
        {
            // Act
            var result = _cache.Remove("nonExistingKey");

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void ContainsKey_ExistingKey_ShouldReturnTrue()
        {
            // Arrange
            const string key = "testKey";
            _cache.GetOrCreate(key, () => "value");

            // Act
            var result = _cache.ContainsKey(key);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void ContainsKey_NonExistingKey_ShouldReturnFalse()
        {
            // Act
            var result = _cache.ContainsKey("nonExistingKey");

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void Clear_ShouldRemoveAllEntries()
        {
            // Arrange
            _cache.GetOrCreate("key1", () => "value1");
            _cache.GetOrCreate("key2", () => "value2");

            // Act
            _cache.Clear();

            // Assert
            Assert.IsFalse(_cache.ContainsKey("key1"));
            Assert.IsFalse(_cache.ContainsKey("key2"));
        }

        [TestMethod]
        public void GetStatistics_ShouldReturnValidStatistics()
        {
            // Arrange
            _cache.GetOrCreate("key1", () => "value1");
            _cache.GetOrCreate("key2", () => "value2");

            // Act
            var statistics = _cache.GetStatistics();

            // Assert
            Assert.IsNotNull(statistics);
            Assert.IsTrue(statistics.TotalEntries >= 0);
            Assert.IsTrue(statistics.ActiveEntries >= 0);
            Assert.IsTrue(statistics.ExpiredEntries >= 0);
        }

        [TestMethod]
        public void CleanupExpired_ShouldNotThrow()
        {
            // Arrange
            _cache.GetOrCreate("key1", () => "value1");

            // Act & Assert (should not throw)
            _cache.CleanupExpired();
        }

        [TestMethod]
        public void Dispose_ShouldNotThrow()
        {
            // Act & Assert (should not throw)
            _cache.Dispose();
        }
    }
}
