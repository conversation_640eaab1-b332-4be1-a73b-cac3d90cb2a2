using Microsoft.VisualStudio.TestTools.UnitTesting;
using IECAD.Services;
using IECAD.Tests.TestHelpers;
using System;

namespace IECAD.Tests.Services
{
    [TestClass]
    public class BlockArrangementServiceTests : TestBase
    {
        private BlockArrangementService _service;

        [TestInitialize]
        public void Setup()
        {
            _service = new BlockArrangementService();
        }

        [TestMethod]
        public void Constructor_WithNullParameters_ShouldNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => new BlockArrangementService());
        }

        [TestMethod]
        public void Constructor_WithValidServices_ShouldNotThrow()
        {
            // Arrange
            var errorService = ServiceConfiguration.Resolve<IErrorHandlingService>();
            var loggingService = ServiceConfiguration.Resolve<ILoggingService>();
            var settingsService = ServiceConfiguration.Resolve<IUserSettingsService>();

            // Act & Assert
            Assert.DoesNotThrow(() => new BlockArrangementService(errorService, loggingService, settingsService));
        }

        [TestMethod]
        public void ArrangeBlocks_WithoutActiveDocument_ShouldHandleGracefully()
        {
            // Note: This test would require mocking AutoCAD's Application.DocumentManager
            // For now, we just verify the service can be instantiated
            Assert.IsNotNull(_service);
        }
    }
}
