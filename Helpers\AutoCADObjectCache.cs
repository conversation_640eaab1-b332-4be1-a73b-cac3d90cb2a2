using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using Autodesk.AutoCAD.DatabaseServices;
using IECAD.Services;

namespace IECAD.Helpers
{
    /// <summary>
    /// Cache for AutoCAD objects to reduce database queries and improve performance
    /// </summary>
    public class AutoCADObjectCache : IDisposable
    {
        private readonly ConcurrentDictionary<string, CacheEntry> _cache = new ConcurrentDictionary<string, CacheEntry>();
        private readonly ILoggingService _loggingService;
        private readonly TimeSpan _defaultExpiration = TimeSpan.FromMinutes(5);
        private bool _disposed = false;

        public AutoCADObjectCache(ILoggingService loggingService = null)
        {
            _loggingService = loggingService;
        }

        /// <summary>
        /// Get or create a cached object
        /// </summary>
        /// <typeparam name="T">The type of object to cache</typeparam>
        /// <param name="key">The cache key</param>
        /// <param name="factory">Factory function to create the object if not cached</param>
        /// <param name="expiration">Cache expiration time (optional)</param>
        /// <returns>The cached or newly created object</returns>
        public T GetOrCreate<T>(string key, Func<T> factory, TimeSpan? expiration = null) where T : class
        {
            if (string.IsNullOrEmpty(key)) throw new ArgumentNullException(nameof(key));
            if (factory == null) throw new ArgumentNullException(nameof(factory));

            var expirationTime = expiration ?? _defaultExpiration;
            var now = DateTime.UtcNow;

            // Check if item exists and is not expired
            if (_cache.TryGetValue(key, out CacheEntry entry))
            {
                if (entry.ExpirationTime > now)
                {
                    _loggingService?.LogDebug($"Cache hit for key: {key}", "AutoCADObjectCache");
                    return entry.Value as T;
                }
                else
                {
                    // Remove expired entry
                    _cache.TryRemove(key, out _);
                    _loggingService?.LogDebug($"Cache entry expired for key: {key}", "AutoCADObjectCache");
                }
            }

            // Create new object and cache it
            try
            {
                T newObject = factory();
                if (newObject != null)
                {
                    var newEntry = new CacheEntry
                    {
                        Value = newObject,
                        ExpirationTime = now.Add(expirationTime),
                        CreatedTime = now
                    };

                    _cache.TryAdd(key, newEntry);
                    _loggingService?.LogDebug($"Cache miss - created and cached object for key: {key}", "AutoCADObjectCache");
                }
                return newObject;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Failed to create object for cache key {key}: {ex.Message}", "AutoCADObjectCache");
                return null;
            }
        }

        /// <summary>
        /// Remove an item from the cache
        /// </summary>
        /// <param name="key">The cache key</param>
        /// <returns>True if the item was removed</returns>
        public bool Remove(string key)
        {
            if (string.IsNullOrEmpty(key)) return false;

            bool removed = _cache.TryRemove(key, out _);
            if (removed)
            {
                _loggingService?.LogDebug($"Removed cache entry for key: {key}", "AutoCADObjectCache");
            }
            return removed;
        }

        /// <summary>
        /// Clear all cached items
        /// </summary>
        public void Clear()
        {
            int count = _cache.Count;
            _cache.Clear();
            _loggingService?.LogDebug($"Cleared {count} cache entries", "AutoCADObjectCache");
        }

        /// <summary>
        /// Remove expired entries from the cache
        /// </summary>
        public void CleanupExpired()
        {
            var now = DateTime.UtcNow;
            var expiredKeys = new List<string>();

            foreach (var kvp in _cache)
            {
                if (kvp.Value.ExpirationTime <= now)
                {
                    expiredKeys.Add(kvp.Key);
                }
            }

            foreach (var key in expiredKeys)
            {
                _cache.TryRemove(key, out _);
            }

            if (expiredKeys.Count > 0)
            {
                _loggingService?.LogDebug($"Cleaned up {expiredKeys.Count} expired cache entries", "AutoCADObjectCache");
            }
        }

        /// <summary>
        /// Get cache statistics
        /// </summary>
        /// <returns>Cache statistics</returns>
        public CacheStatistics GetStatistics()
        {
            var now = DateTime.UtcNow;
            int totalEntries = _cache.Count;
            int expiredEntries = 0;

            foreach (var entry in _cache.Values)
            {
                if (entry.ExpirationTime <= now)
                {
                    expiredEntries++;
                }
            }

            return new CacheStatistics
            {
                TotalEntries = totalEntries,
                ExpiredEntries = expiredEntries,
                ActiveEntries = totalEntries - expiredEntries
            };
        }

        /// <summary>
        /// Check if a key exists in the cache and is not expired
        /// </summary>
        /// <param name="key">The cache key</param>
        /// <returns>True if the key exists and is not expired</returns>
        public bool ContainsKey(string key)
        {
            if (string.IsNullOrEmpty(key)) return false;

            if (_cache.TryGetValue(key, out CacheEntry entry))
            {
                return entry.ExpirationTime > DateTime.UtcNow;
            }

            return false;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                Clear();
                _disposed = true;
            }
        }

        private class CacheEntry
        {
            public object Value { get; set; }
            public DateTime ExpirationTime { get; set; }
            public DateTime CreatedTime { get; set; }
        }
    }

    /// <summary>
    /// Cache statistics information
    /// </summary>
    public class CacheStatistics
    {
        public int TotalEntries { get; set; }
        public int ActiveEntries { get; set; }
        public int ExpiredEntries { get; set; }
    }
}
