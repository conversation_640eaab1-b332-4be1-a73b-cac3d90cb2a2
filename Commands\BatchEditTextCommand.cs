using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.Windows;
using Autodesk.AutoCAD.GraphicsInterface;
using Autodesk.AutoCAD.Geometry;
using IECAD.Views;
using IECAD.Services;

[assembly: CommandClass(typeof(IECAD.Commands.BatchEditTextCommand))]

namespace IECAD.Commands
{
    public class BatchEditTextCommand
    {
        [CommandMethod("BatchEditTexts")]
        public void BatchEditTexts()
        {
            var service = new BatchTextEditorService();
            service.BatchEditTexts();
        }
    }
}
