using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace IECAD.Services.OutlineWalker
{
    /// <summary>
    /// 轮廓步行者测试工具 - 用于验证各种策略的性能和准确性
    /// </summary>
    public class OutlineWalkerTester
    {
        private readonly Document _doc;
        private readonly Editor _ed;

        public OutlineWalkerTester()
        {
            _doc = Application.DocumentManager.MdiActiveDocument;
            _ed = _doc.Editor;
        }

        /// <summary>
        /// 测试所有策略并比较结果
        /// </summary>
        public void TestAllStrategies()
        {
            _ed.WriteMessage("\n=== 轮廓步行者策略测试开始 ===");

            // 提示用户选择实体
            PromptSelectionResult psr = _ed.GetSelection();
            if (psr.Status != PromptStatus.OK)
            {
                _ed.WriteMessage("\n测试取消 - 未选择实体");
                return;
            }

            var entities = GetEntitiesFromSelection(psr.Value);
            if (entities.Count == 0)
            {
                _ed.WriteMessage("\n测试失败 - 没有有效实体");
                return;
            }

            _ed.WriteMessage($"\n测试实体数量: {entities.Count}");

            // 测试各种策略
            var strategies = new[] 
            { 
                WalkerStrategy.Precise, 
                WalkerStrategy.Tolerant, 
                WalkerStrategy.Morphological, 
                WalkerStrategy.Grid 
            };

            var tolerances = new[] { 0.5, 1.0, 2.0 };

            foreach (var strategy in strategies)
            {
                _ed.WriteMessage($"\n--- 测试策略: {strategy} ---");
                
                foreach (var tolerance in tolerances)
                {
                    TestSingleStrategy(strategy, tolerance, entities);
                }
            }

            _ed.WriteMessage("\n=== 轮廓步行者策略测试完成 ===");
        }

        /// <summary>
        /// 测试单个策略
        /// </summary>
        private void TestSingleStrategy(WalkerStrategy strategy, double tolerance, List<Entity> entities)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var walker = CreateWalker(strategy);
                if (walker == null)
                {
                    _ed.WriteMessage($"\n  ❌ 容差{tolerance}: 无法创建步行者");
                    return;
                }

                // 初始化步行者
                walker.Initialize(entities, tolerance);
                
                // 执行步行
                int stepCount = 0;
                int maxSteps = 10000; // 防止死循环
                
                while (!walker.IsCompleted && stepCount < maxSteps)
                {
                    var eventResult = walker.FindNextEvent();
                    var decision = walker.MakeDecision(eventResult);
                    walker.UpdateState(decision);
                    stepCount++;
                }

                stopwatch.Stop();

                // 获取结果
                var resultPath = walker.GetResultPath();
                
                // 输出测试结果
                if (walker.IsCompleted && resultPath.Count > 0)
                {
                    _ed.WriteMessage($"\n  ✅ 容差{tolerance}: 成功 - {stepCount}步, {resultPath.Count}点, {stopwatch.ElapsedMilliseconds}ms");
                    
                    // 计算轮廓属性
                    var area = CalculatePolygonArea(resultPath);
                    var perimeter = CalculatePolygonPerimeter(resultPath);
                    _ed.WriteMessage($"     面积: {area:F2}, 周长: {perimeter:F2}");
                }
                else
                {
                    _ed.WriteMessage($"\n  ⚠️ 容差{tolerance}: 未完成 - {stepCount}步, {stopwatch.ElapsedMilliseconds}ms");
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _ed.WriteMessage($"\n  ❌ 容差{tolerance}: 异常 - {ex.Message}");
            }
        }

        /// <summary>
        /// 创建指定策略的步行者
        /// </summary>
        private IOutlineWalker CreateWalker(WalkerStrategy strategy)
        {
            return strategy switch
            {
                WalkerStrategy.Precise => new PreciseWalker(),
                WalkerStrategy.Tolerant => new TolerantWalker(),
                WalkerStrategy.Morphological => new MorphologicalWalker(),
                WalkerStrategy.Grid => new GridWalker(),
                _ => null
            };
        }

        /// <summary>
        /// 从选择集获取实体列表
        /// </summary>
        private List<Entity> GetEntitiesFromSelection(SelectionSet selectionSet)
        {
            var entities = new List<Entity>();
            
            using (var tr = _doc.TransactionManager.StartTransaction())
            {
                foreach (ObjectId id in selectionSet.GetObjectIds())
                {
                    var entity = tr.GetObject(id, OpenMode.ForRead) as Entity;
                    if (entity != null)
                    {
                        entities.Add(entity);
                    }
                }
                tr.Commit();
            }
            
            return entities;
        }

        /// <summary>
        /// 计算多边形面积
        /// </summary>
        private double CalculatePolygonArea(List<Point3d> points)
        {
            if (points.Count < 3) return 0;

            double area = 0;
            for (int i = 0; i < points.Count; i++)
            {
                int j = (i + 1) % points.Count;
                area += points[i].X * points[j].Y;
                area -= points[j].X * points[i].Y;
            }
            return Math.Abs(area) / 2.0;
        }

        /// <summary>
        /// 计算多边形周长
        /// </summary>
        private double CalculatePolygonPerimeter(List<Point3d> points)
        {
            if (points.Count < 2) return 0;

            double perimeter = 0;
            for (int i = 0; i < points.Count; i++)
            {
                int j = (i + 1) % points.Count;
                perimeter += points[i].DistanceTo(points[j]);
            }
            return perimeter;
        }

        /// <summary>
        /// 可视化比较所有策略的结果
        /// </summary>
        public void VisualCompareStrategies()
        {
            _ed.WriteMessage("\n=== 可视化策略比较 ===");

            // 提示用户选择实体
            PromptSelectionResult psr = _ed.GetSelection();
            if (psr.Status != PromptStatus.OK) return;

            var entities = GetEntitiesFromSelection(psr.Value);
            if (entities.Count == 0) return;

            double tolerance = 1.0;
            var strategies = new[] { WalkerStrategy.Precise, WalkerStrategy.Tolerant, WalkerStrategy.Morphological, WalkerStrategy.Grid };
            var colors = new[] { 1, 2, 3, 4 }; // 红、黄、绿、青

            using (var tr = _doc.TransactionManager.StartTransaction())
            {
                var modelSpace = (BlockTableRecord)tr.GetObject(
                    SymbolUtilityServices.GetBlockModelSpaceId(_doc.Database), OpenMode.ForWrite);

                for (int i = 0; i < strategies.Length; i++)
                {
                    try
                    {
                        var walker = CreateWalker(strategies[i]);
                        if (walker == null) continue;

                        walker.Initialize(entities, tolerance);
                        
                        // 执行步行
                        int steps = 0;
                        while (!walker.IsCompleted && steps < 5000)
                        {
                            var eventResult = walker.FindNextEvent();
                            var decision = walker.MakeDecision(eventResult);
                            walker.UpdateState(decision);
                            steps++;
                        }

                        var resultPath = walker.GetResultPath();
                        if (resultPath.Count > 2)
                        {
                            // 创建多段线显示结果
                            using (var polyline = new Polyline())
                            {
                                for (int j = 0; j < resultPath.Count; j++)
                                {
                                    polyline.AddVertexAt(j, new Point2d(resultPath[j].X, resultPath[j].Y), 0, 0, 0);
                                }
                                polyline.Closed = true;
                                polyline.ColorIndex = colors[i];
                                polyline.LineWeight = LineWeight.LineWeight050;

                                modelSpace.AppendEntity(polyline);
                                tr.AddNewlyCreatedDBObject(polyline, true);
                            }
                            
                            _ed.WriteMessage($"\n{strategies[i]} (颜色{colors[i]}): {resultPath.Count}个点");
                        }
                    }
                    catch (Exception ex)
                    {
                        _ed.WriteMessage($"\n{strategies[i]}策略出错: {ex.Message}");
                    }
                }

                tr.Commit();
            }

            _ed.WriteMessage("\n=== 可视化比较完成 ===");
        }
    }
}
