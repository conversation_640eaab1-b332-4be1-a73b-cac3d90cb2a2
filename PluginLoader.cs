﻿using Autodesk.AutoCAD.Runtime;
using IECAD.Services;
using System;
using System.Windows;
using Application = Autodesk.AutoCAD.ApplicationServices.Application;

namespace IECAD
{
    public class PluginLoader : IExtensionApplication
    {
        private static ILoggingService _loggingService;
        private static IErrorHandlingService _errorHandlingService;

        static PluginLoader()
        {
            // Initialize services using service container
            _loggingService = ServiceConfiguration.Resolve<ILoggingService>();
            _errorHandlingService = ServiceConfiguration.Resolve<IErrorHandlingService>();
        }

        public void Initialize()
        {
            try
            {
                _loggingService.LogInfo("Plugin initialization started", "PluginLoader");

                // Log the log file path for debugging
                string logPath = _loggingService.GetLogFilePath();
                if (!string.IsNullOrEmpty(logPath))
                {
                    _loggingService.LogInfo($"Log file location: {logPath}", "PluginLoader");
                }

                // Optimize memory settings at startup
                Helpers.MemoryManager.OptimizeMemorySettings();

                // Use delayed loading mechanism to ensure AutoCAD startup is complete before executing commands
                Application.Idle += OnIdle;

                _loggingService.LogInfo("Plugin initialization completed successfully", "PluginLoader");
            }
            catch (System.Exception ex)
            {
                _errorHandlingService.HandleException(ex, "Plugin initialization failed. Please contact support if this problem persists.", "PluginLoader");
            }
        }

        private void OnIdle(object sender, System.EventArgs e)
        {
            try
            {
                Application.Idle -= OnIdle; // Ensure this only executes once
                _loggingService.LogInfo("OnIdle event triggered, showing sidebar", "PluginLoader");
                ShowSidebar();
            }
            catch (System.Exception ex)
            {
                _errorHandlingService.HandleException(ex, "Failed to show sidebar during initialization", "PluginLoader");
            }
        }

        private void ShowSidebar()
        {
            try
            {
                _loggingService.LogInfo("Executing SHOWSIDEBAR command", "PluginLoader");
                // Execute the sidebar command - could be extracted to a CommandService or similar class
                AutoCADService.ExecuteAutoCADCommand("SHOWSIDEBAR");
                _loggingService.LogInfo("SHOWSIDEBAR command executed successfully", "PluginLoader");
            }
            catch (System.Exception ex)
            {
                _errorHandlingService.HandleException(ex, "Failed to execute SHOWSIDEBAR command", "PluginLoader");
            }
        }

        public void Terminate()
        {
            try
            {
                _loggingService.LogInfo("Plugin termination started", "PluginLoader");

                // Clean up resources or unregister events
                // Remove any remaining event handlers
                Application.Idle -= OnIdle;

                // Dispose object pools
                Helpers.ObjectPools.DisposeAll();

                // Final memory cleanup
                Helpers.MemoryManager.ForceGarbageCollection();

                // Dispose of service container
                ServiceConfiguration.Reset();

                _loggingService.LogInfo("Plugin termination completed", "PluginLoader");
            }
            catch (System.Exception ex)
            {
                // Use debug output as fallback since logging service might be disposed
                System.Diagnostics.Debug.WriteLine($"Plugin termination error: {ex.Message}");
            }
        }
    }
}