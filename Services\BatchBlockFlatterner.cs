﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using System.Collections.Generic;

namespace IECAD.Services
{
    public class BatchBlockFlattener
    {
        public void FlattenBlocks()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            // Prompt for block references to flatten
            PromptSelectionOptions pso = new PromptSelectionOptions();
            pso.MessageForAdding = "\nSelect block references to flatten: ";
            PromptSelectionResult psr = ed.GetSelection(pso);
            if (psr.Status != PromptStatus.OK) return;

            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    HashSet<ObjectId> processedBlockDefs = new HashSet<ObjectId>();

                    foreach (SelectedObject selObj in psr.Value)
                    {
                        if (selObj != null && selObj.ObjectId.IsValid)
                        {
                            BlockReference blockRef = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as BlockReference;
                            if (blockRef != null)
                            {
                                ObjectId blockDefId = blockRef.BlockTableRecord;

                                // If this block definition has already been processed, skip it
                                if (processedBlockDefs.Contains(blockDefId))
                                {
                                    continue;
                                }

                                BlockTableRecord blockDef = tr.GetObject(blockDefId, OpenMode.ForWrite) as BlockTableRecord;

                                // Store original entities
                                List<Entity> originalEntities = new List<Entity>();
                                foreach (ObjectId entId in blockDef)
                                {
                                    Entity ent = tr.GetObject(entId, OpenMode.ForRead) as Entity;
                                    originalEntities.Add(ent);
                                }

                                // Clear existing entities
                                foreach (ObjectId entId in blockDef)
                                {
                                    Entity ent = tr.GetObject(entId, OpenMode.ForWrite) as Entity;
                                    ent.Erase();
                                }

                                // Recursively flatten nested blocks
                                FlattenNestedBlocks(Matrix3d.Identity, originalEntities, blockDef, tr);

                                processedBlockDefs.Add(blockDefId);
                            }
                        }
                    }

                    // Commit changes
                    tr.Commit();
                }
                catch (Autodesk.AutoCAD.Runtime.Exception ex)
                {
                    ed.WriteMessage($"\nError: {ex.Message}");
                    tr.Abort();
                }
            }

            ed.WriteMessage("\nBlock flattening completed.");
        }

        private void FlattenNestedBlocks(Matrix3d parentTransform, List<Entity> sourceEntities, BlockTableRecord targetBlock, Transaction tr)
        {
            foreach (Entity ent in sourceEntities)
            {
                if (ent is BlockReference nestedBlockRef)
                {
                    BlockTableRecord nestedBlockDef = tr.GetObject(nestedBlockRef.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;

                    List<Entity> nestedEntities = new List<Entity>();
                    foreach (ObjectId entId in nestedBlockDef)
                    {
                        Entity nestedEnt = tr.GetObject(entId, OpenMode.ForRead) as Entity;
                        nestedEntities.Add(nestedEnt);
                    }

                    // Compute the transformation for nested block
                    Matrix3d nestedTransform = parentTransform * nestedBlockRef.BlockTransform;
                    FlattenNestedBlocks(nestedTransform, nestedEntities, targetBlock, tr);
                }
                else
                {
                    // Clone the entity and transform it
                    Entity clonedEnt = ent.Clone() as Entity;
                    clonedEnt.TransformBy(parentTransform);
                    targetBlock.AppendEntity(clonedEnt);
                    tr.AddNewlyCreatedDBObject(clonedEnt, true);
                }
            }
        }
    }
}