using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using IECAD.Helpers;
using IECAD.Tests.TestHelpers;

namespace IECAD.Tests.Helpers
{
    [TestClass]
    public class AutoCADTransactionHelperTests : TestBase
    {
        [TestMethod]
        public void IsAutoCADReady_ShouldReturnBooleanValue()
        {
            // Act
            var result = AutoCADTransactionHelper.IsAutoCADReady();

            // Assert
            Assert.IsInstanceOfType(result, typeof(bool));
        }

        [TestMethod]
        public void GetActiveDocument_ShouldNotThrow()
        {
            // Act & Assert (should not throw)
            var document = AutoCADTransactionHelper.GetActiveDocument();
            // Document may be null in test environment, which is expected
        }

        [TestMethod]
        public void GetActiveDatabase_ShouldNotThrow()
        {
            // Act & Assert (should not throw)
            var database = AutoCADTransactionHelper.GetActiveDatabase();
            // Database may be null in test environment, which is expected
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void ExecuteInTransaction_WithNullAction_ShouldThrowArgumentNullException()
        {
            // Act
            AutoCADTransactionHelper.ExecuteInTransaction(null);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void ExecuteInTransactionWithResult_WithNullFunction_ShouldThrowArgumentNullException()
        {
            // Act
            AutoCADTransactionHelper.ExecuteInTransaction<string>(null);
        }

        [TestMethod]
        public void ExecuteBatchInTransaction_WithNullOperations_ShouldReturnTrue()
        {
            // Act
            var result = AutoCADTransactionHelper.ExecuteBatchInTransaction(null);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void ExecuteBatchInTransaction_WithEmptyOperations_ShouldReturnTrue()
        {
            // Act
            var result = AutoCADTransactionHelper.ExecuteBatchInTransaction(new System.Action<Autodesk.AutoCAD.DatabaseServices.Transaction, Autodesk.AutoCAD.DatabaseServices.Database>[0]);

            // Assert
            Assert.IsTrue(result);
        }
    }
}
