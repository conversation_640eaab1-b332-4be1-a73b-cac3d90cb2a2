using System;
using IECAD.Services;

namespace IECAD
{
    /// <summary>
    /// Simple test class to verify logging service functionality
    /// </summary>
    public static class LoggingTest
    {
        /// <summary>
        /// Test the logging service to ensure it works without permission issues
        /// </summary>
        public static void TestLogging()
        {
            try
            {
                Console.WriteLine("Testing FileLoggingService...");
                
                var loggingService = new FileLoggingService();
                
                // Test basic logging
                loggingService.LogInfo("Logging service test started", "LoggingTest");
                loggingService.LogDebug("Debug message test", "LoggingTest");
                loggingService.LogWarning("Warning message test", "LoggingTest");
                loggingService.LogError("Error message test", "LoggingTest");
                
                // Test exception logging
                try
                {
                    throw new InvalidOperationException("Test exception for logging");
                }
                catch (Exception ex)
                {
                    loggingService.LogException(ex, "Test exception logging", "LoggingTest");
                }
                
                // Get log file path
                string logPath = loggingService.GetLogFilePath();
                Console.WriteLine($"Log file created at: {logPath}");
                
                loggingService.LogInfo("Logging service test completed successfully", "LoggingTest");
                
                // Dispose the service
                loggingService.Dispose();
                
                Console.WriteLine("FileLoggingService test completed successfully!");
                Console.WriteLine($"Check log file at: {logPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Logging test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
