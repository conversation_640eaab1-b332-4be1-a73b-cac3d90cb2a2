using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using IECAD.Helpers;
using IECAD.Services;

namespace IECAD.ViewModels
{
    /// <summary>
    /// Base view model class with common functionality for MVVM pattern
    /// </summary>
    public abstract class BaseViewModel : INotifyPropertyChanged, IDisposable
    {
        protected readonly ILoggingService _loggingService;
        protected readonly IErrorHandlingService _errorHandlingService;
        private bool _isBusy;
        private string _busyMessage;
        private bool _disposed = false;

        protected BaseViewModel(ILoggingService loggingService = null, IErrorHandlingService errorHandlingService = null)
        {
            _loggingService = loggingService;
            _errorHandlingService = errorHandlingService;
        }

        /// <summary>
        /// Indicates whether the view model is currently busy with an operation
        /// </summary>
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value);
        }

        /// <summary>
        /// Message to display when the view model is busy
        /// </summary>
        public string BusyMessage
        {
            get => _busyMessage;
            set => SetProperty(ref _busyMessage, value);
        }

        /// <summary>
        /// Event raised when a property value changes
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// Raises the PropertyChanged event for the specified property
        /// </summary>
        /// <param name="propertyName">Name of the property that changed</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Sets the property value and raises PropertyChanged if the value has changed
        /// </summary>
        /// <typeparam name="T">Type of the property</typeparam>
        /// <param name="field">Reference to the backing field</param>
        /// <param name="value">New value</param>
        /// <param name="propertyName">Name of the property</param>
        /// <returns>True if the property value was changed</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(field, value)) return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// Execute an async operation with busy state management and error handling
        /// </summary>
        /// <param name="operation">The async operation to execute</param>
        /// <param name="busyMessage">Message to display while busy</param>
        /// <param name="operationName">Name of the operation for logging</param>
        protected async Task ExecuteAsync(Func<Task> operation, string busyMessage = "Processing...", string operationName = null)
        {
            if (IsBusy) return;

            try
            {
                IsBusy = true;
                BusyMessage = busyMessage;

                _loggingService?.LogDebug($"Starting async operation: {operationName ?? "Unknown"}", GetType().Name);

                await operation();

                _loggingService?.LogDebug($"Completed async operation: {operationName ?? "Unknown"}", GetType().Name);
            }
            catch (Exception ex)
            {
                _errorHandlingService?.HandleException(ex, $"Error in async operation: {operationName ?? "Unknown"}", GetType().Name);
            }
            finally
            {
                IsBusy = false;
                BusyMessage = null;
            }
        }

        /// <summary>
        /// Execute an async operation with a result, with busy state management and error handling
        /// </summary>
        /// <typeparam name="T">Type of the result</typeparam>
        /// <param name="operation">The async operation to execute</param>
        /// <param name="defaultValue">Default value to return if operation fails</param>
        /// <param name="busyMessage">Message to display while busy</param>
        /// <param name="operationName">Name of the operation for logging</param>
        /// <returns>The result of the operation or default value if failed</returns>
        protected async Task<T> ExecuteAsync<T>(Func<Task<T>> operation, T defaultValue = default(T), string busyMessage = "Processing...", string operationName = null)
        {
            if (IsBusy) return defaultValue;

            try
            {
                IsBusy = true;
                BusyMessage = busyMessage;

                _loggingService?.LogDebug($"Starting async operation: {operationName ?? "Unknown"}", GetType().Name);

                T result = await operation();

                _loggingService?.LogDebug($"Completed async operation: {operationName ?? "Unknown"}", GetType().Name);

                return result;
            }
            catch (Exception ex)
            {
                _errorHandlingService?.HandleException(ex, $"Error in async operation: {operationName ?? "Unknown"}", GetType().Name);
                return defaultValue;
            }
            finally
            {
                IsBusy = false;
                BusyMessage = null;
            }
        }

        /// <summary>
        /// Create an async command that executes the specified async operation
        /// </summary>
        /// <param name="execute">The async operation to execute</param>
        /// <param name="canExecute">Function to determine if the command can execute</param>
        /// <param name="busyMessage">Message to display while busy</param>
        /// <param name="operationName">Name of the operation for logging</param>
        /// <returns>An async command</returns>
        protected ICommand CreateAsyncCommand(Func<Task> execute, Func<bool> canExecute = null, string busyMessage = "Processing...", string operationName = null)
        {
            return new AsyncRelayCommand(
                async () => await ExecuteAsync(execute, busyMessage, operationName),
                canExecute ?? (() => !IsBusy)
            );
        }

        /// <summary>
        /// Create an async command with parameter that executes the specified async operation
        /// </summary>
        /// <typeparam name="T">Type of the command parameter</typeparam>
        /// <param name="execute">The async operation to execute</param>
        /// <param name="canExecute">Function to determine if the command can execute</param>
        /// <param name="busyMessage">Message to display while busy</param>
        /// <param name="operationName">Name of the operation for logging</param>
        /// <returns>An async command with parameter</returns>
        protected ICommand CreateAsyncCommand<T>(Func<T, Task> execute, Func<T, bool> canExecute = null, string busyMessage = "Processing...", string operationName = null)
        {
            return new AsyncRelayCommand<T>(
                async (param) => await ExecuteAsync(() => execute(param), busyMessage, operationName),
                canExecute ?? ((_) => !IsBusy)
            );
        }

        /// <summary>
        /// Handle errors in a consistent way
        /// </summary>
        /// <param name="ex">The exception to handle</param>
        /// <param name="userMessage">User-friendly message</param>
        /// <param name="showToUser">Whether to show the error to the user</param>
        protected void HandleError(Exception ex, string userMessage = null, bool showToUser = true)
        {
            _errorHandlingService?.HandleException(ex, userMessage, GetType().Name, showToUser);
        }

        /// <summary>
        /// Show an informational message to the user
        /// </summary>
        /// <param name="message">The message to show</param>
        /// <param name="title">The title of the message</param>
        protected void ShowInfo(string message, string title = null)
        {
            _errorHandlingService?.ShowInfo(message, title);
        }

        /// <summary>
        /// Show a success message to the user
        /// </summary>
        /// <param name="message">The success message to show</param>
        /// <param name="title">The title of the message</param>
        protected void ShowSuccess(string message, string title = null)
        {
            _errorHandlingService?.ShowSuccess(message, title);
        }

        /// <summary>
        /// Dispose of resources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Dispose of resources
        /// </summary>
        /// <param name="disposing">True if disposing</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // Override in derived classes to dispose of specific resources
                _disposed = true;
            }
        }
    }
}
