using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;
using ExcelDataReader;
using System;
using System.Data;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;
using Application = Autodesk.AutoCAD.ApplicationServices.Application;

namespace IECAD.Services
{
    public class RoomPlanImporter
    {
        // Constants
        private const string ROOM_BLOCK_NAME = "RoomPlan";
        private const string ROOM_BLOCK_RESOURCE = "IECAD.Resources.RoomPlan.dwg";  // Updated to match the RootNamespace
        private const string ATTRIBUTE_NAME = "名称";
        private const string DYNAMIC_PROPERTY_LENGTH = "d1";
        private const string DYNAMIC_PROPERTY_AREA = "面积";

        // Logger (you need to implement this based on your logging framework)
        //private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();

        public void ImportRoomPlan()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;

            string filePath =  FileDialogService.ShowExcelFileDialog(allowLegacyFormat: true);
            if (string.IsNullOrEmpty(filePath)) return;

            try
            {
                using (var stream = File.Open(filePath, FileMode.Open, FileAccess.Read))
                using (var reader = ExcelReaderFactory.CreateReader(stream))
                {
                    var result = reader.AsDataSet(new ExcelDataSetConfiguration()
                    {
                        ConfigureDataTable = (_) => new ExcelDataTableConfiguration()
                        {
                            UseHeaderRow = true
                        }
                    });

                    System.Data.DataTable dataTable = result.Tables[0]; // Assuming data is in the first sheet

                    using (Transaction trans = db.TransactionManager.StartTransaction())
                    {
                        BlockTableRecord modelSpace = (BlockTableRecord)trans.GetObject(
                            SymbolUtilityServices.GetBlockModelSpaceId(db), OpenMode.ForWrite);

                        EnsureRoomBlockExists(db, trans);

                        Point3d insertPoint = Point3d.Origin;
                        foreach (DataRow row in dataTable.Rows)
                        {
                            RoomInfo roomInfo = ExtractRoomInfo(row);
                            insertPoint = InsertRoomBlock(trans, db, modelSpace, roomInfo, insertPoint);
                        }

                        trans.Commit();
                    }
                }
            }
            catch (Autodesk.AutoCAD.Runtime.Exception ex)
            {
                ShowErrorMessage($"AutoCAD Error: {ex.Message}");
                //Logger.Error(ex, "Error in ImportRoomPlan");
            }
            catch (System.Exception ex)
            {
                ShowErrorMessage($"Unexpected error: {ex.Message}");
                //Logger.Error(ex, "Unexpected error in ImportRoomPlan");
            }
        }

    

        private RoomInfo ExtractRoomInfo(DataRow row)
        {
            return new RoomInfo
            {
                Name = row["Name"].ToString() ?? string.Empty,
                Area = Convert.ToDouble(row["Area"]),
                DefaultWidth = Convert.ToDouble(row["DefaultWidth"])
            };
        }

        private void EnsureRoomBlockExists(Database db, Transaction trans)
        {
            BlockTable blkTbl = (BlockTable)trans.GetObject(db.BlockTableId, OpenMode.ForWrite);
            if (!blkTbl.Has(ROOM_BLOCK_NAME))
            {
                using (Stream stream = GetEmbeddedResource(ROOM_BLOCK_RESOURCE))
                {
                    if (stream == null)
                    {
                        throw new System.InvalidOperationException($"Cannot find embedded '{ROOM_BLOCK_NAME}' block resource. Please ensure the resource file is correctly added to the project.");
                    }

                    string tempFileName = Path.GetTempFileName();
                    try
                    {
                        // Debug: Show temp file path
                        //System.Windows.Forms.MessageBox.Show($"Temp file path: {tempFileName}");

                        using (FileStream fileStream = new FileStream(tempFileName, FileMode.Create, FileAccess.Write))
                        {
                            stream.CopyTo(fileStream);
                        }

                        using (Database blockDb = new Database(false, true))
                        {
                            blockDb.ReadDwgFile(tempFileName, FileOpenMode.OpenForReadAndAllShare, false, "");

                            // Get the block table from source drawing
                            using (Transaction blockTrans = blockDb.TransactionManager.StartTransaction())
                            {
                                BlockTable sourceBlkTbl = (BlockTable)blockTrans.GetObject(blockDb.BlockTableId, OpenMode.ForRead);

                                // Debug: List all blocks in the source drawing
                                
                                var blockNames = new System.Text.StringBuilder("Blocks in source drawing:\n");
                                /***
                                foreach (ObjectId id in sourceBlkTbl)
                                {
                                    BlockTableRecord btr = (BlockTableRecord)blockTrans.GetObject(id, OpenMode.ForRead);
                                    blockNames.AppendLine($"- {btr.Name}");
                                }
                                System.Windows.Forms.MessageBox.Show(blockNames.ToString());
                                ***/

                                // Check if the block exists in the source drawing
                                if (!sourceBlkTbl.Has(ROOM_BLOCK_NAME))
                                {
                                    throw new System.InvalidOperationException($"The source DWG file does not contain a block named '{ROOM_BLOCK_NAME}'. Available blocks are:\n{blockNames}");
                                }

                                // Get the block definition
                                ObjectId blockDefId = sourceBlkTbl[ROOM_BLOCK_NAME];
                                BlockTableRecord blockDef = (BlockTableRecord)blockTrans.GetObject(blockDefId, OpenMode.ForRead);

                                // Create ObjectIdCollection with just the block definition
                                ObjectIdCollection blockIds = new ObjectIdCollection { blockDefId };

                                // Clone the block definition
                                IdMapping mapping = new IdMapping();
                                db.WblockCloneObjects(blockIds, blkTbl.ObjectId, mapping, DuplicateRecordCloning.Replace, false);

                                blockTrans.Commit();
                            }
                        }
                    }
                    catch (Autodesk.AutoCAD.Runtime.Exception acadEx)
                    {
                        throw new System.InvalidOperationException($"Error inserting '{ROOM_BLOCK_NAME}' block: {acadEx.Message}\nPlease ensure the DWG file format is correct and contains a valid block definition.", acadEx);
                    }
                    catch (System.Exception ex)
                    {
                        // Debug: Show any other exceptions
                        System.Windows.Forms.MessageBox.Show($"General error: {ex.Message}\n\nStack trace: {ex.StackTrace}");
                        throw;
                    }
                    finally
                    {
                        if (File.Exists(tempFileName))
                        {
                            File.Delete(tempFileName);
                        }
                    }
                }
            }
        }

        private Stream GetEmbeddedResource(string resourceName)
        {
            var assembly = Assembly.GetExecutingAssembly();

            // Debug: List all embedded resources
            //var resources = assembly.GetManifestResourceNames();
            //string availableResources = string.Join("\n", resources);
            //System.Windows.Forms.MessageBox.Show($"Available resources:\n{availableResources}\n\nTrying to find: {resourceName}");

            return assembly.GetManifestResourceStream(resourceName);
        }

        private Point3d InsertRoomBlock(Transaction trans, Database db, BlockTableRecord modelSpace, RoomInfo roomInfo, Point3d insertPoint)
        {
            BlockTable blkTbl = (BlockTable)trans.GetObject(db.BlockTableId, OpenMode.ForRead);
            if (!blkTbl.Has(ROOM_BLOCK_NAME))
            {
                throw new System.InvalidOperationException($"{ROOM_BLOCK_NAME} block not found. Please ensure it's properly loaded.");
            }

            BlockTableRecord roomBlock = (BlockTableRecord)trans.GetObject(blkTbl[ROOM_BLOCK_NAME], OpenMode.ForRead);
            using (BlockReference roomBlockRef = new BlockReference(insertPoint, roomBlock.ObjectId))
            {
                modelSpace.AppendEntity(roomBlockRef);
                trans.AddNewlyCreatedDBObject(roomBlockRef, true);

                SetBlockAttributes(trans, roomBlock, roomBlockRef, roomInfo);
                SetDynamicBlockProperties(roomBlockRef, roomInfo);

                return new Point3d(insertPoint.X, insertPoint.Y + roomInfo.Area * 1000 / roomInfo.DefaultWidth, insertPoint.Z);
            }
        }

        private void SetBlockAttributes(Transaction trans, BlockTableRecord roomBlock, BlockReference roomBlockRef, RoomInfo roomInfo)
        {
            foreach (ObjectId attribId in roomBlock)
            {
                AttributeDefinition attDef = trans.GetObject(attribId, OpenMode.ForRead) as AttributeDefinition;
                if (attDef != null)
                {
                    using (AttributeReference attRef = new AttributeReference())
                    {
                        attRef.SetAttributeFromBlock(attDef, roomBlockRef.BlockTransform);
                        attRef.Position = attDef.Position.TransformBy(roomBlockRef.BlockTransform);

                        if (attRef.Tag == ATTRIBUTE_NAME)
                        {
                            attRef.TextString = roomInfo.Name;
                        }

                        roomBlockRef.AttributeCollection.AppendAttribute(attRef);
                        trans.AddNewlyCreatedDBObject(attRef, true);
                    }
                }
            }
        }

        private void SetDynamicBlockProperties(BlockReference roomBlockRef, RoomInfo roomInfo)
        {
            DynamicBlockReferencePropertyCollection dynProps = roomBlockRef.DynamicBlockReferencePropertyCollection;
            if (dynProps != null)
            {
                foreach (DynamicBlockReferenceProperty prop in dynProps)
                {
                    if (prop.PropertyName == DYNAMIC_PROPERTY_LENGTH)
                    {
                        prop.Value = roomInfo.Area / roomInfo.DefaultWidth * 1000;
                    }
                    else if (prop.PropertyName == DYNAMIC_PROPERTY_AREA)
                    {
                        prop.Value = roomInfo.Area;
                    }
                }
            }
        }

        private void ShowErrorMessage(string message)
        {
            Application.ShowAlertDialog(message);
            //Logger.Error(message);
        }
    }

    public class RoomInfo
    {
        public string Name { get; set; } = string.Empty;
        public double Area { get; set; }
        public double DefaultWidth { get; set; }
    }
}
