﻿using System;
using System.ComponentModel;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Input;
using IECAD.Helpers;
using IECAD.Services;

namespace IECAD.ViewModels
{
    public class MainSidebarViewModel : BaseViewModel
    {
        private readonly IAutoCADService _autoCADService;
        private LayerManagerViewModel _layerManagerVM;
        private string _logoPath = string.Empty;
        private string _applicationTitle;

        public LayerManagerViewModel LayerManagerVM
        {
            get => _layerManagerVM;
            private set => SetProperty(ref _layerManagerVM, value);
        }

        public string LogoPath
        {
            get => _logoPath;
            set => SetProperty(ref _logoPath, value);
        }

        public string ApplicationTitle
        {
            get => _applicationTitle;
            set => SetProperty(ref _applicationTitle, value);
        }

        // Commands
        public ICommand RefreshCommand { get; private set; }
        public ICommand ClearCacheCommand { get; private set; }

        public MainSidebarViewModel(IAutoCADService autoCADService)
            : base(ServiceConfiguration.TryResolve<ILoggingService>(out var logging) ? logging : null,
                   ServiceConfiguration.TryResolve<IErrorHandlingService>(out var error) ? error : null)
        {
            _autoCADService = autoCADService ?? throw new ArgumentNullException(nameof(autoCADService));

            InitializeAsync();
        }

        private async void InitializeAsync()
        {
            await ExecuteAsync(async () =>
            {
                // Initialize properties
                ApplicationTitle = Constants.ApplicationConstants.APPLICATION_TITLE;
                InitializeLogoPath();

                // Initialize child view models
                LayerManagerVM = new LayerManagerViewModel(_autoCADService);

                // Initialize commands
                InitializeCommands();

                _loggingService?.LogInfo("MainSidebarViewModel initialized", "MainSidebarViewModel");
            }, "Initializing...", "Initialize");
        }

        private void InitializeLogoPath()
        {
            try
            {
                string assemblyName = Assembly.GetExecutingAssembly().GetName().Name;
                LogoPath = $"pack://application:,,,/{assemblyName};component/Resources/logo.png";
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Failed to initialize logo path: {ex.Message}", "MainSidebarViewModel");
            }
        }

        private void InitializeCommands()
        {
            RefreshCommand = CreateAsyncCommand(RefreshAsync, () => !IsBusy, "Refreshing...", "Refresh");
            ClearCacheCommand = CreateAsyncCommand(ClearCacheAsync, () => !IsBusy, "Clearing cache...", "ClearCache");
        }

        private async Task RefreshAsync()
        {
            try
            {
                _loggingService?.LogInfo("Refreshing sidebar data", "MainSidebarViewModel");

                // Refresh layer manager
                if (LayerManagerVM?.RefreshCommand is AsyncRelayCommand asyncRefresh)
                {
                    await Task.Run(() => asyncRefresh.Execute(null));
                }
                else if (LayerManagerVM?.RefreshCommand?.CanExecute(null) == true)
                {
                    LayerManagerVM.RefreshCommand.Execute(null);
                }

                ShowSuccess("Sidebar refreshed successfully");
            }
            catch (Exception ex)
            {
                HandleError(ex, "Failed to refresh sidebar data");
            }
        }

        private async Task ClearCacheAsync()
        {
            try
            {
                _loggingService?.LogInfo("Clearing AutoCAD service cache", "MainSidebarViewModel");

                // Clear AutoCAD service cache if available
                if (_autoCADService is AutoCADService service)
                {
                    await Task.Run(() => service.ClearCache());
                }

                ShowSuccess("Cache cleared successfully");
            }
            catch (Exception ex)
            {
                HandleError(ex, "Failed to clear cache");
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                LayerManagerVM?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}