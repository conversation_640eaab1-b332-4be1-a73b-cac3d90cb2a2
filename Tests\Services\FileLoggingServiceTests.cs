using System;
using System.IO;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using IECAD.Services;
using IECAD.Tests.TestHelpers;

namespace IECAD.Tests.Services
{
    [TestClass]
    public class FileLoggingServiceTests : TestBase
    {
        private FileLoggingService _service;
        private string _testLogDirectory;

        [TestInitialize]
        public override void TestInitialize()
        {
            base.TestInitialize();
            _testLogDirectory = Path.Combine(Path.GetTempPath(), "IECAD_Tests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testLogDirectory);
            
            // Set the base directory for testing
            AppDomain.CurrentDomain.SetData("DataDirectory", _testLogDirectory);
            _service = new FileLoggingService();
        }

        [TestCleanup]
        public override void TestCleanup()
        {
            _service?.Dispose();
            
            // Clean up test directory
            if (Directory.Exists(_testLogDirectory))
            {
                try
                {
                    Directory.Delete(_testLogDirectory, true);
                }
                catch
                {
                    // Ignore cleanup errors
                }
            }
            
            base.TestCleanup();
        }

        [TestMethod]
        public void LogInfo_ShouldNotThrow()
        {
            // Act & Assert (should not throw)
            _service.LogInfo("Test info message", "TestSource");
        }

        [TestMethod]
        public void LogError_ShouldNotThrow()
        {
            // Act & Assert (should not throw)
            _service.LogError("Test error message", "TestSource");
        }

        [TestMethod]
        public void LogException_ShouldNotThrow()
        {
            // Arrange
            var testException = CreateTestException();

            // Act & Assert (should not throw)
            _service.LogException(testException, "Test exception message", "TestSource");
        }

        [TestMethod]
        public void Dispose_ShouldNotThrow()
        {
            // Act & Assert (should not throw)
            _service.Dispose();
        }

        [TestMethod]
        public void LogDebug_InDebugMode_ShouldNotThrow()
        {
            // Act & Assert (should not throw)
            _service.LogDebug("Test debug message", "TestSource");
        }
    }
}
