using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using IECAD.Services;
using IECAD.Helpers;

namespace IECAD.ViewModels
{
    /// <summary>
    /// ViewModel for performance dashboard
    /// </summary>
    public class PerformanceDashboardViewModel : BaseViewModel
    {
        private readonly IPerformanceOptimizationService _performanceOptimization;
        private readonly IPerformanceMonitoringService _performanceMonitoring;
        private PerformanceMetrics _currentMetrics;
        private ObservableCollection<PerformanceRecommendation> _recommendations;
        private bool _autoOptimizationEnabled;

        public PerformanceMetrics CurrentMetrics
        {
            get => _currentMetrics;
            set => SetProperty(ref _currentMetrics, value);
        }

        public ObservableCollection<PerformanceRecommendation> Recommendations
        {
            get => _recommendations;
            set => SetProperty(ref _recommendations, value);
        }

        public bool AutoOptimizationEnabled
        {
            get => _autoOptimizationEnabled;
            set
            {
                if (SetProperty(ref _autoOptimizationEnabled, value))
                {
                    OnAutoOptimizationToggled();
                }
            }
        }

        // Commands
        public ICommand RefreshMetricsCommand { get; private set; }
        public ICommand OptimizePerformanceCommand { get; private set; }
        public ICommand OptimizeMemoryCommand { get; private set; }
        public ICommand ApplyRecommendationCommand { get; private set; }
        public ICommand ClearPerformanceDataCommand { get; private set; }

        public PerformanceDashboardViewModel(IPerformanceOptimizationService performanceOptimization,
            IPerformanceMonitoringService performanceMonitoring,
            ILoggingService loggingService,
            IErrorHandlingService errorHandlingService)
            : base(loggingService, errorHandlingService)
        {
            _performanceOptimization = performanceOptimization ?? throw new ArgumentNullException(nameof(performanceOptimization));
            _performanceMonitoring = performanceMonitoring ?? throw new ArgumentNullException(nameof(performanceMonitoring));

            Recommendations = new ObservableCollection<PerformanceRecommendation>();
            
            InitializeCommands();
            InitializeAsync();
        }

        private void InitializeCommands()
        {
            RefreshMetricsCommand = CreateAsyncCommand(RefreshMetricsAsync, () => !IsBusy, "Refreshing metrics...", "RefreshMetrics");
            OptimizePerformanceCommand = CreateAsyncCommand(OptimizePerformanceAsync, () => !IsBusy, "Optimizing performance...", "OptimizePerformance");
            OptimizeMemoryCommand = CreateAsyncCommand(OptimizeMemoryAsync, () => !IsBusy, "Optimizing memory...", "OptimizeMemory");
            ApplyRecommendationCommand = CreateAsyncCommand<PerformanceRecommendation>(ApplyRecommendationAsync, 
                rec => !IsBusy && rec?.ApplyAction != null, "Applying recommendation...", "ApplyRecommendation");
            ClearPerformanceDataCommand = CreateAsyncCommand(ClearPerformanceDataAsync, () => !IsBusy, "Clearing data...", "ClearPerformanceData");
        }

        private async void InitializeAsync()
        {
            await RefreshMetricsAsync();
        }

        private async Task RefreshMetricsAsync()
        {
            try
            {
                _loggingService?.LogDebug("Refreshing performance metrics", "PerformanceDashboardViewModel");

                // Get current metrics
                CurrentMetrics = await Task.Run(() => _performanceOptimization.GetPerformanceMetrics());

                // Get recommendations
                var recommendations = await Task.Run(() => _performanceOptimization.GetPerformanceRecommendations());
                
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    Recommendations.Clear();
                    foreach (var recommendation in recommendations.OrderByDescending(r => r.Impact))
                    {
                        Recommendations.Add(recommendation);
                    }
                });

                _loggingService?.LogDebug($"Refreshed metrics: {CurrentMetrics?.MemoryUsageMB} MB memory, {Recommendations.Count} recommendations", "PerformanceDashboardViewModel");
            }
            catch (Exception ex)
            {
                HandleError(ex, "Failed to refresh performance metrics");
            }
        }

        private async Task OptimizePerformanceAsync()
        {
            try
            {
                _loggingService?.LogInfo("Starting performance optimization", "PerformanceDashboardViewModel");

                await Task.Run(() => _performanceOptimization.OptimizePerformance());
                
                ShowSuccess("Performance optimization completed successfully");
                
                // Refresh metrics after optimization
                await RefreshMetricsAsync();
            }
            catch (Exception ex)
            {
                HandleError(ex, "Failed to optimize performance");
            }
        }

        private async Task OptimizeMemoryAsync()
        {
            try
            {
                _loggingService?.LogInfo("Starting memory optimization", "PerformanceDashboardViewModel");

                await Task.Run(() => _performanceOptimization.OptimizeMemory());
                
                ShowSuccess("Memory optimization completed successfully");
                
                // Refresh metrics after optimization
                await RefreshMetricsAsync();
            }
            catch (Exception ex)
            {
                HandleError(ex, "Failed to optimize memory");
            }
        }

        private async Task ApplyRecommendationAsync(PerformanceRecommendation recommendation)
        {
            if (recommendation?.ApplyAction == null) return;

            try
            {
                _loggingService?.LogInfo($"Applying recommendation: {recommendation.Title}", "PerformanceDashboardViewModel");

                await Task.Run(() => recommendation.ApplyAction());
                
                ShowSuccess($"Applied recommendation: {recommendation.Title}");
                
                // Refresh metrics after applying recommendation
                await RefreshMetricsAsync();
            }
            catch (Exception ex)
            {
                HandleError(ex, $"Failed to apply recommendation: {recommendation.Title}");
            }
        }

        private async Task ClearPerformanceDataAsync()
        {
            try
            {
                _loggingService?.LogInfo("Clearing performance data", "PerformanceDashboardViewModel");

                await Task.Run(() => _performanceMonitoring.Clear());
                
                ShowSuccess("Performance data cleared successfully");
                
                // Refresh metrics after clearing data
                await RefreshMetricsAsync();
            }
            catch (Exception ex)
            {
                HandleError(ex, "Failed to clear performance data");
            }
        }

        private void OnAutoOptimizationToggled()
        {
            try
            {
                _performanceOptimization.SetPerformanceMonitoring(AutoOptimizationEnabled);
                
                if (AutoOptimizationEnabled)
                {
                    _loggingService?.LogInfo("Auto-optimization enabled", "PerformanceDashboardViewModel");
                    ShowInfo("Auto-optimization enabled. The system will automatically apply performance optimizations when needed.");
                }
                else
                {
                    _loggingService?.LogInfo("Auto-optimization disabled", "PerformanceDashboardViewModel");
                    ShowInfo("Auto-optimization disabled. You can manually apply optimizations as needed.");
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "Failed to toggle auto-optimization");
                // Revert the setting
                AutoOptimizationEnabled = !AutoOptimizationEnabled;
            }
        }

        /// <summary>
        /// Get formatted memory usage string
        /// </summary>
        public string FormattedMemoryUsage
        {
            get
            {
                if (CurrentMetrics == null) return "N/A";
                return $"{CurrentMetrics.MemoryUsageMB:N0} MB";
            }
        }

        /// <summary>
        /// Get formatted average response time string
        /// </summary>
        public string FormattedResponseTime
        {
            get
            {
                if (CurrentMetrics?.AverageResponseTime == null) return "N/A";
                return $"{CurrentMetrics.AverageResponseTime.TotalMilliseconds:F0} ms";
            }
        }

        /// <summary>
        /// Get formatted cache hit rate string
        /// </summary>
        public string FormattedCacheHitRate
        {
            get
            {
                if (CurrentMetrics == null) return "N/A";
                return $"{CurrentMetrics.CacheHitRate}%";
            }
        }

        /// <summary>
        /// Get count of high priority recommendations
        /// </summary>
        public int HighPriorityRecommendationsCount
        {
            get
            {
                return Recommendations?.Count(r => r.Impact == PerformanceImpact.High || r.Impact == PerformanceImpact.Critical) ?? 0;
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                Recommendations?.Clear();
            }
            base.Dispose(disposing);
        }
    }
}
