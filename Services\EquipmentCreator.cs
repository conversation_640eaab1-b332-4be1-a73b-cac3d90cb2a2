using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.Colors;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;
using ExcelDataReader;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using IECAD.Constants;
using IECAD.Helpers;

namespace IECAD.Services
{
    public class EquipmentCreator : IEquipmentCreatorService
    {
        // Constants moved to ApplicationConstants class
        private readonly ILoggingService _loggingService;
        private readonly IErrorHandlingService _errorHandlingService;
        private readonly IAutoCADService _autoCADService;
        private readonly IPerformanceMonitoringService _performanceMonitoring;

        public EquipmentCreator(ILoggingService loggingService, IErrorHandlingService errorHandlingService,
            IAutoCADService autoCADService, IPerformanceMonitoringService performanceMonitoring)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _errorHandlingService = errorHandlingService ?? throw new ArgumentNullException(nameof(errorHandlingService));
            _autoCADService = autoCADService ?? throw new ArgumentNullException(nameof(autoCADService));
            _performanceMonitoring = performanceMonitoring ?? throw new ArgumentNullException(nameof(performanceMonitoring));
        }

        [CommandMethod("CE")]
        public void CreateEquipment()
        {
            try
            {
                _loggingService.LogInfo("Starting equipment creation process", "EquipmentCreator");

                string filePath = FileDialogService.ShowExcelFileDialog(allowLegacyFormat: true);
                if (string.IsNullOrEmpty(filePath))
                {
                    _errorHandlingService.ShowInfo(ApplicationConstants.ERROR_NO_FILE_SELECTED);
                    return;
                }

                bool success = CreateEquipmentFromExcel(filePath);
                if (success)
                {
                    _loggingService.LogInfo("Equipment creation process completed successfully", "EquipmentCreator");
                }
            }
            catch (System.Exception ex)
            {
                _errorHandlingService.HandleException(ex, "An error occurred while creating equipment blocks", "EquipmentCreator");
            }
        }

        public bool CreateEquipmentFromExcel(string filePath)
        {
            using var measurement = _performanceMonitoring.StartMeasurement("CreateEquipmentFromExcel");
            using var memoryScope = MemoryManager.CreateMemoryMonitoringScope("CreateEquipmentFromExcel", 200);
            {
                try
                {
                    _loggingService.LogInfo($"Creating equipment from Excel file: {filePath}", "EquipmentCreator");

                    List<Equipment> equipmentList;
                    using var readMeasurement = _performanceMonitoring.StartMeasurement("ReadEquipmentDataFromExcel");
                    equipmentList = ReadEquipmentDataFromExcel(filePath);

                    if (!equipmentList.Any())
                    {
                        _errorHandlingService.HandleError(ApplicationConstants.ERROR_NO_VALID_DATA, "EquipmentCreator");
                        return false;
                    }

                    _loggingService.LogInfo($"Successfully read {equipmentList.Count} equipment items from Excel", "EquipmentCreator");

                    bool success;
                    using var createMeasurement = _performanceMonitoring.StartMeasurement("CreateEquipmentBlocks");
                    success = CreateEquipmentBlocks(equipmentList);

                    if (success)
                    {
                        _errorHandlingService.ShowSuccess($"Successfully created {equipmentList.Sum(e => e.Amount)} equipment blocks from {equipmentList.Count} equipment types.");
                    }

                    // Clear equipment list to free memory
                    equipmentList.Clear();
                    equipmentList = null;

                    // Suggest garbage collection for large operations
                    if (_performanceMonitoring.GetStatistics("CreateEquipmentFromExcel")?.AverageDuration.TotalSeconds > 5)
                    {
                        MemoryManager.ForceGarbageCollection(0, false); // Quick Gen0 collection
                    }

                    return success;
                }
                catch (System.Exception ex)
                {
                    _errorHandlingService.HandleException(ex, "Failed to create equipment from Excel file", "EquipmentCreator");
                    return false;
                }
            }
        }

        public bool CreateEquipmentBlocks(List<Equipment> equipmentList)
        {
            try
            {
                if (equipmentList == null || !equipmentList.Any())
                {
                    _errorHandlingService.HandleError("No equipment data provided", "EquipmentCreator");
                    return false;
                }

                if (!_autoCADService.IsAutoCADReady())
                {
                    _errorHandlingService.HandleError("AutoCAD is not ready for operations", "EquipmentCreator");
                    return false;
                }

                InsertEquipmentBlocks(equipmentList);
                return true;
            }
            catch (System.Exception ex)
            {
                _errorHandlingService.HandleException(ex, "Failed to create equipment blocks", "EquipmentCreator");
                return false;
            }
        }

        private void InsertEquipmentBlocks(List<Equipment> equipmentList)
        {
            _loggingService.LogInfo("Starting block insertion process", "EquipmentCreator");

            // Use optimized transaction helper for better performance and error handling
            bool success = AutoCADTransactionHelper.ExecuteInTransaction((transaction, db) =>
            {
                BlockTable blockTable = transaction.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord modelSpace = transaction.GetObject(blockTable[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                Point3d insertionPoint = new Point3d(0, 0, 0);

                // Pre-create all blocks first to optimize performance
                var blockIds = new Dictionary<string, ObjectId>();
                foreach (var equipment in equipmentList)
                {
                    string blockName = GetValidBlockName(equipment);
                    if (!blockIds.ContainsKey(blockName))
                    {
                        ObjectId blockId = FindOrCreateBlock(equipment, blockTable, transaction);
                        if (blockId != ObjectId.Null)
                        {
                            blockIds[blockName] = blockId;
                            _loggingService.LogDebug($"Created/found block for equipment: {equipment.Name} - {equipment.Model}", "EquipmentCreator");
                        }
                        else
                        {
                            _loggingService.LogWarning($"Failed to create block for equipment: {equipment.Name}", "EquipmentCreator");
                        }
                    }
                }

                // Now insert all block references
                foreach (var equipment in equipmentList)
                {
                    string blockName = GetValidBlockName(equipment);
                    if (blockIds.TryGetValue(blockName, out ObjectId blockId))
                    {
                        for (int i = 0; i < equipment.Amount; i++)
                        {
                            InsertBlockReference(blockId, insertionPoint, modelSpace, transaction);
                            insertionPoint = new Point3d(insertionPoint.X, insertionPoint.Y + equipment.Length + ApplicationConstants.EQUIPMENT_VERTICAL_SPACING, 0);
                        }
                    }
                    insertionPoint = new Point3d(insertionPoint.X + equipment.Width + ApplicationConstants.EQUIPMENT_HORIZONTAL_SPACING, 0, 0);
                }

            }, _loggingService, _errorHandlingService);

            if (success)
            {
                _loggingService.LogInfo("Block insertion process completed successfully", "EquipmentCreator");
            }
            else
            {
                throw new InvalidOperationException("Failed to insert equipment blocks");
            }
        }

        private ObjectId FindOrCreateBlock(Equipment equipment, BlockTable blockTable, Transaction transaction)
        {
            string blockName = GetValidBlockName(equipment);
            if (blockTable.Has(blockName))
                return blockTable[blockName];

            using (BlockTableRecord blockRecord = new BlockTableRecord { Name = blockName })
            {
                blockTable.UpgradeOpen();
                ObjectId blockId = blockTable.Add(blockRecord);
                transaction.AddNewlyCreatedDBObject(blockRecord, true);

                AddEntitiesToBlock(blockRecord, equipment, transaction);

                return blockId;
            }
        }

        private void AddEntitiesToBlock(BlockTableRecord blockRecord, Equipment equipment, Transaction transaction)
        {
            using (Polyline outline = CreateOutline(equipment.Width, equipment.Length))
            {
                blockRecord.AppendEntity(outline);
                transaction.AddNewlyCreatedDBObject(outline, true);
            }

            // Create a single DBText with both name and model (for simpler height editing)
            string textContent = equipment.Name;
            if (!string.IsNullOrEmpty(equipment.Model))
            {
                textContent += "\n" + equipment.Model;
            }
            
            // Position text in center of equipment
            Point3d textPosition = new Point3d(equipment.Width / 2, equipment.Length / 2, 0);
            
            // Create a standard DBText entity that should have editable text height in properties panel
            DBText text = new DBText();
            text.Position = textPosition;
            text.TextString = textContent;
            text.Height = ApplicationConstants.DEFAULT_TEXT_HEIGHT; // Default height - this should be editable
            text.HorizontalMode = TextHorizontalMode.TextCenter;
            text.VerticalMode = TextVerticalMode.TextVerticalMid;
            text.AlignmentPoint = textPosition;
            text.Color = Color.FromRgb(255, 255, 0); // Yellow color
            
            // Add the text to the block
            blockRecord.AppendEntity(text);
            transaction.AddNewlyCreatedDBObject(text, true);
        }

        private Polyline CreateOutline(double width, double length)
        {
            Polyline outline = new Polyline
            {
                Closed = true
            };
            outline.AddVertexAt(0, new Point2d(0, 0), 0, 0, 0);
            outline.AddVertexAt(1, new Point2d(width, 0), 0, 0, 0);
            outline.AddVertexAt(2, new Point2d(width, length), 0, 0, 0);
            outline.AddVertexAt(3, new Point2d(0, length), 0, 0, 0);
            SetEntityProperties(outline);
            return outline;
        }



        private void SetEntityProperties(Entity entity)
        {
            // Only apply these settings to non-text entities to ensure text height remains editable
            if (!(entity is DBText))
            {
                entity.Color = Color.FromColorIndex(ColorMethod.ByBlock, 0);
                entity.Linetype = "ByBlock";
                entity.LinetypeScale = 1.0;
            }
        }

        private void InsertBlockReference(ObjectId blockId, Point3d insertionPoint, BlockTableRecord modelSpace, Transaction transaction)
        {
            if (modelSpace == null)
            {
                throw new ArgumentNullException(nameof(modelSpace));
            }

            using (BlockReference blockRef = new BlockReference(insertionPoint, blockId))
            {
                // Add the block reference to model space first
                modelSpace.AppendEntity(blockRef);
                transaction.AddNewlyCreatedDBObject(blockRef, true);
                
                // Now we can access the attribute definitions
                try
                {
                    // Get the block table record
                    BlockTableRecord blockDef = transaction.GetObject(blockId, OpenMode.ForRead) as BlockTableRecord;
                    
                    if (blockDef != null && blockDef.HasAttributeDefinitions)
                    {
                        // Collect all attribute definitions from the block
                        foreach (ObjectId objId in blockDef)
                        {
                            DBObject obj = transaction.GetObject(objId, OpenMode.ForRead);
                            if (obj is AttributeDefinition attDef && !attDef.Constant)
                            {
                                // Create an attribute reference for this block
                                using (AttributeReference attRef = new AttributeReference())
                                {
                                    // Copy all properties from the definition
                                    attRef.SetAttributeFromBlock(attDef, blockRef.BlockTransform);
                                    attRef.TextString = attDef.TextString;
                                    attRef.Height = attDef.Height; // Ensure height is copied
                                    
                                    // Add it to the block reference
                                    blockRef.AttributeCollection.AppendAttribute(attRef);
                                    transaction.AddNewlyCreatedDBObject(attRef, true);
                                }
                            }
                        }
                    }
                }
                catch (Autodesk.AutoCAD.Runtime.Exception ex)
                {
                    // Log the error but don't fail the entire operation
                    System.Diagnostics.Debug.WriteLine($"Error adding attributes: {ex.Message}");
                }
            }
        }



        private string GetValidBlockName(Equipment equipment)
        {
            string blockName = $"{equipment.Name}_{equipment.Model}".Replace(" ", "_").Replace("/", "_");
            
            // Add underscore prefix if the block name starts with a digit
            if (blockName.Length > 0 && char.IsDigit(blockName[0]))
            {
                blockName = "_" + blockName;
            }
            
            return blockName;
        }

        // Removed ShowUserMessage method - now using ErrorHandlingService

        public List<Equipment> ReadEquipmentDataFromExcel(string filePath)
        {
            List<Equipment> equipmentList = new List<Equipment>();

            try
            {
                _loggingService.LogInfo($"Reading equipment data from Excel file: {filePath}", "EquipmentCreator");

                using (var stream = File.Open(filePath, FileMode.Open, FileAccess.Read))
                {
                    using (var reader = ExcelReaderFactory.CreateReader(stream))
                    {
                        var result = reader.AsDataSet();
                        var table = result.Tables[0];

                        _loggingService.LogInfo($"Excel file contains {table.Rows.Count} rows", "EquipmentCreator");

                        for (int i = 1; i < table.Rows.Count; i++) // Skip header row
                        {
                            try
                            {
                                var row = table.Rows[i];
                                if (row.ItemArray.Length >= 5)
                                {
                                    Equipment equipment = new Equipment
                                    {
                                        Name = row[0]?.ToString() ?? string.Empty,
                                        Model = row[1]?.ToString() ?? string.Empty,
                                        Amount = Convert.ToInt32(row[2]),
                                        Width = Convert.ToDouble(row[3]),
                                        Length = Convert.ToDouble(row[4])
                                    };

                                    // Validate equipment data
                                    if (ValidateEquipment(equipment))
                                    {
                                        equipmentList.Add(equipment);
                                        _loggingService.LogDebug($"Added equipment: {equipment.Name} - {equipment.Model}", "EquipmentCreator");
                                    }
                                    else
                                    {
                                        _loggingService.LogWarning($"Skipped invalid equipment data at row {i + 1}", "EquipmentCreator");
                                    }
                                }
                                else
                                {
                                    _loggingService.LogWarning($"Row {i + 1} has insufficient columns ({row.ItemArray.Length}), expected at least 5", "EquipmentCreator");
                                }
                            }
                            catch (System.Exception ex)
                            {
                                _loggingService.LogError($"Error processing row {i + 1}: {ex.Message}", "EquipmentCreator");
                                // Continue processing other rows
                            }
                        }
                    }
                }

                _loggingService.LogInfo($"Successfully read {equipmentList.Count} valid equipment items", "EquipmentCreator");
            }
            catch (System.Exception ex)
            {
                _errorHandlingService.HandleException(ex, "Failed to read equipment data from Excel file", "EquipmentCreator");
                throw; // Re-throw to be handled by calling method
            }

            return equipmentList;
        }

        public bool ValidateEquipment(Equipment equipment)
        {
            if (string.IsNullOrWhiteSpace(equipment.Name))
            {
                _loggingService.LogWarning("Equipment name is empty", "EquipmentCreator");
                return false;
            }

            if (equipment.Amount <= 0)
            {
                _loggingService.LogWarning($"Equipment {equipment.Name} has invalid amount: {equipment.Amount}", "EquipmentCreator");
                return false;
            }

            if (equipment.Width <= 0 || equipment.Length <= 0)
            {
                _loggingService.LogWarning($"Equipment {equipment.Name} has invalid dimensions: {equipment.Width}x{equipment.Length}", "EquipmentCreator");
                return false;
            }

            return true;
        }
    }
}