using System;

namespace IECAD.Services
{
    /// <summary>
    /// Interface for logging service to provide abstraction for different logging implementations
    /// </summary>
    public interface ILoggingService
    {
        /// <summary>
        /// Log an informational message
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the log message (optional)</param>
        void LogInfo(string message, string source = null);

        /// <summary>
        /// Log a warning message
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the log message (optional)</param>
        void LogWarning(string message, string source = null);

        /// <summary>
        /// Log an error message
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the log message (optional)</param>
        void LogError(string message, string source = null);

        /// <summary>
        /// Log an exception with full details
        /// </summary>
        /// <param name="exception">The exception to log</param>
        /// <param name="message">Additional message (optional)</param>
        /// <param name="source">The source of the log message (optional)</param>
        void LogException(Exception exception, string message = null, string source = null);

        /// <summary>
        /// Log a debug message (only in debug builds)
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the log message (optional)</param>
        void LogDebug(string message, string source = null);

        /// <summary>
        /// Get the current log file path (implementation-specific)
        /// </summary>
        /// <returns>The path to the current log file, or null if not applicable</returns>
        string GetLogFilePath();
    }
}
