using System;

namespace IECAD.Services
{
    /// <summary>
    /// Service configuration and registration for dependency injection
    /// </summary>
    public static class ServiceConfiguration
    {
        private static IServiceContainer _container;
        private static readonly object _lock = new object();

        /// <summary>
        /// Get the configured service container
        /// </summary>
        public static IServiceContainer Container
        {
            get
            {
                if (_container == null)
                {
                    lock (_lock)
                    {
                        if (_container == null)
                        {
                            ConfigureServices();
                        }
                    }
                }
                return _container;
            }
        }

        /// <summary>
        /// Configure all services for dependency injection
        /// </summary>
        private static void ConfigureServices()
        {
            _container = new SimpleServiceContainer();

            // Register core services as singletons
            _container.RegisterSingleton<ILoggingService, FileLoggingService>();
            _container.RegisterSingleton<IErrorHandlingService, ErrorHandlingService>();
            _container.RegisterSingleton<IPerformanceMonitoringService, PerformanceMonitoringService>();
            _container.RegisterSingleton<IPerformanceOptimizationService, PerformanceOptimizationService>();
            _container.RegisterSingleton<IAutoCADService, AutoCADService>();
            _container.RegisterSingleton<IUserSettingsService, UserSettingsService>();

            // Register business services as transients (new instance each time)
            _container.RegisterTransient<IEquipmentCreatorService, EquipmentCreator>();

            // Register other services as needed
            // _container.RegisterSingleton<IOtherService, OtherServiceImplementation>();
        }

        /// <summary>
        /// Resolve a service from the container
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <returns>The service instance</returns>
        public static T Resolve<T>() where T : class
        {
            return Container.Resolve<T>();
        }

        /// <summary>
        /// Try to resolve a service from the container
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <param name="service">The resolved service instance</param>
        /// <returns>True if the service was resolved successfully</returns>
        public static bool TryResolve<T>(out T service) where T : class
        {
            return Container.TryResolve<T>(out service);
        }

        /// <summary>
        /// Check if a service is registered
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <returns>True if the service is registered</returns>
        public static bool IsRegistered<T>() where T : class
        {
            return Container.IsRegistered<T>();
        }

        /// <summary>
        /// Reset the service container (mainly for testing)
        /// </summary>
        public static void Reset()
        {
            lock (_lock)
            {
                if (_container is IDisposable disposable)
                {
                    disposable.Dispose();
                }
                _container = null;
            }
        }

        /// <summary>
        /// Register additional services (for extensibility)
        /// </summary>
        /// <param name="configureAction">Action to configure additional services</param>
        public static void ConfigureAdditionalServices(Action<IServiceContainer> configureAction)
        {
            if (configureAction == null) throw new ArgumentNullException(nameof(configureAction));
            
            configureAction(Container);
        }
    }
}
