using System;
using System.IO;
using System.Text;
using IECAD.Constants;

namespace IECAD.Services
{
    /// <summary>
    /// File-based logging service implementation
    /// </summary>
    public class FileLoggingService : ILoggingService, IDisposable
    {
        private readonly string _logFilePath;
        private readonly object _lockObject = new object();
        private bool _disposed = false;

        public FileLoggingService()
        {
            try
            {
                // Create logs directory in user's AppData folder to avoid permission issues
                string userAppData = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
                string logDirectory = Path.Combine(userAppData, "IECAD", "Logs");

                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }

                // Create log file with timestamp
                string timestamp = DateTime.Now.ToString("yyyy-MM-dd");
                _logFilePath = Path.Combine(logDirectory, $"IECAD_{timestamp}.log");
            }
            catch (Exception ex)
            {
                // Fallback to temp directory if AppData is not accessible
                try
                {
                    string tempDirectory = Path.Combine(Path.GetTempPath(), "IECAD_Logs");
                    if (!Directory.Exists(tempDirectory))
                    {
                        Directory.CreateDirectory(tempDirectory);
                    }

                    string timestamp = DateTime.Now.ToString("yyyy-MM-dd");
                    _logFilePath = Path.Combine(tempDirectory, $"IECAD_{timestamp}.log");
                }
                catch
                {
                    // Final fallback - use temp file
                    _logFilePath = Path.GetTempFileName();
                }

                // Log the initial error to debug output as fallback
                System.Diagnostics.Debug.WriteLine($"FileLoggingService initialization warning: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Using fallback log path: {_logFilePath}");
            }
        }

        public void LogInfo(string message, string source = null)
        {
            WriteLog("INFO", message, source);
        }

        public void LogWarning(string message, string source = null)
        {
            WriteLog("WARN", message, source);
        }

        public void LogError(string message, string source = null)
        {
            WriteLog("ERROR", message, source);
        }

        public void LogException(Exception exception, string message = null, string source = null)
        {
            var logMessage = new StringBuilder();
            
            if (!string.IsNullOrEmpty(message))
            {
                logMessage.AppendLine(message);
            }
            
            logMessage.AppendLine($"Exception Type: {exception.GetType().Name}");
            logMessage.AppendLine($"Exception Message: {exception.Message}");
            
            if (!string.IsNullOrEmpty(exception.StackTrace))
            {
                logMessage.AppendLine($"Stack Trace: {exception.StackTrace}");
            }
            
            if (exception.InnerException != null)
            {
                logMessage.AppendLine($"Inner Exception: {exception.InnerException.Message}");
            }

            WriteLog("ERROR", logMessage.ToString(), source);
        }

        public void LogDebug(string message, string source = null)
        {
#if DEBUG
            WriteLog("DEBUG", message, source);
#endif
        }

        /// <summary>
        /// Get the current log file path for debugging purposes
        /// </summary>
        /// <returns>The path to the current log file</returns>
        public string GetLogFilePath()
        {
            return _logFilePath;
        }

        private void WriteLog(string level, string message, string source)
        {
            if (_disposed) return;

            try
            {
                lock (_lockObject)
                {
                    var logEntry = new StringBuilder();
                    logEntry.Append($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] ");
                    logEntry.Append($"[{level}] ");
                    
                    if (!string.IsNullOrEmpty(source))
                    {
                        logEntry.Append($"[{source}] ");
                    }
                    
                    logEntry.AppendLine(message);

                    File.AppendAllText(_logFilePath, logEntry.ToString(), Encoding.UTF8);
                }
            }
            catch (Exception ex)
            {
                // If logging fails, write to debug output as fallback
                System.Diagnostics.Debug.WriteLine($"Logging failed: {ex.Message}");
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // Log service shutdown
                try
                {
                    WriteLog("INFO", "Logging service shutting down", "FileLoggingService");
                }
                catch
                {
                    // Ignore errors during shutdown
                }
                
                _disposed = true;
            }
        }

        ~FileLoggingService()
        {
            Dispose(false);
        }
    }
}
