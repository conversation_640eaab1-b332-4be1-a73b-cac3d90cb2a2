using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using IECAD.Services;

namespace IECAD.Tests.TestHelpers
{
    /// <summary>
    /// Base class for unit tests with common setup and utilities
    /// </summary>
    [TestClass]
    public abstract class TestBase
    {
        protected Mock<ILoggingService> MockLoggingService { get; private set; }
        protected Mock<IErrorHandlingService> MockErrorHandlingService { get; private set; }
        protected Mock<IAutoCADService> MockAutoCADService { get; private set; }
        protected Mock<IPerformanceMonitoringService> MockPerformanceMonitoring { get; private set; }

        [TestInitialize]
        public virtual void TestInitialize()
        {
            // Create mock services
            MockLoggingService = new Mock<ILoggingService>();
            MockErrorHandlingService = new Mock<IErrorHandlingService>();
            MockAutoCADService = new Mock<IAutoCADService>();
            MockPerformanceMonitoring = new Mock<IPerformanceMonitoringService>();

            // Setup default behaviors
            SetupDefaultMockBehaviors();
        }

        [TestCleanup]
        public virtual void TestCleanup()
        {
            // Reset service configuration after each test
            ServiceConfiguration.Reset();
        }

        protected virtual void SetupDefaultMockBehaviors()
        {
            // Setup logging service to not throw exceptions
            MockLoggingService.Setup(x => x.LogInfo(It.IsAny<string>(), It.IsAny<string>()));
            MockLoggingService.Setup(x => x.LogDebug(It.IsAny<string>(), It.IsAny<string>()));
            MockLoggingService.Setup(x => x.LogWarning(It.IsAny<string>(), It.IsAny<string>()));
            MockLoggingService.Setup(x => x.LogError(It.IsAny<string>(), It.IsAny<string>()));
            MockLoggingService.Setup(x => x.LogException(It.IsAny<Exception>(), It.IsAny<string>(), It.IsAny<string>()));

            // Setup error handling service
            MockErrorHandlingService.Setup(x => x.HandleException(It.IsAny<Exception>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>()));
            MockErrorHandlingService.Setup(x => x.HandleError(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>()));
            MockErrorHandlingService.Setup(x => x.HandleWarning(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>()));
            MockErrorHandlingService.Setup(x => x.ShowInfo(It.IsAny<string>(), It.IsAny<string>()));
            MockErrorHandlingService.Setup(x => x.ShowSuccess(It.IsAny<string>(), It.IsAny<string>()));

            // Setup AutoCAD service
            MockAutoCADService.Setup(x => x.IsAutoCADReady()).Returns(true);
            MockAutoCADService.Setup(x => x.GetActiveDocumentName()).Returns("TestDocument.dwg");

            // Setup performance monitoring
            MockPerformanceMonitoring.Setup(x => x.StartMeasurement(It.IsAny<string>())).Returns(Mock.Of<IDisposable>());
        }

        /// <summary>
        /// Assert that a specific method was called on a mock
        /// </summary>
        /// <typeparam name="T">Type of the mock</typeparam>
        /// <param name="mock">The mock object</param>
        /// <param name="expression">Expression representing the method call</param>
        /// <param name="times">Expected number of times the method should be called</param>
        protected void AssertMethodCalled<T>(Mock<T> mock, System.Linq.Expressions.Expression<System.Action<T>> expression, Times? times = null) where T : class
        {
            mock.Verify(expression, times ?? Times.Once);
        }

        /// <summary>
        /// Assert that a specific method was never called on a mock
        /// </summary>
        /// <typeparam name="T">Type of the mock</typeparam>
        /// <param name="mock">The mock object</param>
        /// <param name="expression">Expression representing the method call</param>
        protected void AssertMethodNotCalled<T>(Mock<T> mock, System.Linq.Expressions.Expression<System.Action<T>> expression) where T : class
        {
            mock.Verify(expression, Times.Never);
        }

        /// <summary>
        /// Create a test exception for testing error handling
        /// </summary>
        /// <param name="message">Exception message</param>
        /// <returns>Test exception</returns>
        protected Exception CreateTestException(string message = "Test exception")
        {
            return new InvalidOperationException(message);
        }

        /// <summary>
        /// Setup service configuration with mock services for testing
        /// </summary>
        protected void SetupTestServiceConfiguration()
        {
            ServiceConfiguration.ConfigureAdditionalServices(container =>
            {
                container.RegisterSingleton<ILoggingService>(MockLoggingService.Object);
                container.RegisterSingleton<IErrorHandlingService>(MockErrorHandlingService.Object);
                container.RegisterSingleton<IAutoCADService>(MockAutoCADService.Object);
                container.RegisterSingleton<IPerformanceMonitoringService>(MockPerformanceMonitoring.Object);
            });
        }

        /// <summary>
        /// Wait for async operations to complete (useful for testing async methods)
        /// </summary>
        /// <param name="timeoutMs">Timeout in milliseconds</param>
        protected void WaitForAsyncOperations(int timeoutMs = 1000)
        {
            var timeout = DateTime.Now.AddMilliseconds(timeoutMs);
            while (DateTime.Now < timeout)
            {
                System.Threading.Thread.Sleep(10);
                System.Windows.Forms.Application.DoEvents();
            }
        }
    }
}
