<UserControl x:Class="IECAD.Views.ProgressIndicator"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="60" d:DesignWidth="300">

    <UserControl.Resources>
        <!-- Spinner animation -->
        <Storyboard x:Key="SpinnerAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetName="SpinnerRotateTransform"
                           Storyboard.TargetProperty="Angle"
                           From="0" To="360"
                           Duration="0:0:1"/>
        </Storyboard>
    </UserControl.Resources>
    
    <Border Background="#80000000" 
            CornerRadius="8"
            Padding="16,12">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Spinner -->
            <Grid Grid.Column="0" 
                  Width="24" Height="24"
                  Margin="0,0,12,0">
                <Ellipse Width="20" Height="20"
                        Stroke="#4A90E2"
                        StrokeThickness="2"
                        StrokeDashArray="3,1"
                        RenderTransformOrigin="0.5,0.5">
                    <Ellipse.RenderTransform>
                        <RotateTransform x:Name="SpinnerRotateTransform"/>
                    </Ellipse.RenderTransform>
                </Ellipse>
            </Grid>
            
            <!-- Message -->
            <TextBlock Grid.Column="1"
                      Text="{Binding Message, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      Foreground="White"
                      FontSize="12"
                      VerticalAlignment="Center"
                      TextWrapping="Wrap"/>
        </Grid>
    </Border>
</UserControl>
