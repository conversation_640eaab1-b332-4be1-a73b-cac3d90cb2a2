using System;

namespace IECAD.Services
{
    /// <summary>
    /// Simple service container interface for dependency injection
    /// </summary>
    public interface IServiceContainer
    {
        /// <summary>
        /// Register a service implementation for an interface
        /// </summary>
        /// <typeparam name="TInterface">The interface type</typeparam>
        /// <typeparam name="TImplementation">The implementation type</typeparam>
        void RegisterSingleton<TInterface, TImplementation>()
            where TImplementation : class, TInterface
            where TInterface : class;

        /// <summary>
        /// Register a service instance
        /// </summary>
        /// <typeparam name="TInterface">The interface type</typeparam>
        /// <param name="instance">The service instance</param>
        void RegisterSingleton<TInterface>(TInterface instance)
            where TInterface : class;

        /// <summary>
        /// Register a transient service (new instance each time)
        /// </summary>
        /// <typeparam name="TInterface">The interface type</typeparam>
        /// <typeparam name="TImplementation">The implementation type</typeparam>
        void RegisterTransient<TInterface, TImplementation>()
            where TImplementation : class, TInterface
            where TInterface : class;

        /// <summary>
        /// Resolve a service instance
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <returns>The service instance</returns>
        T Resolve<T>() where T : class;

        /// <summary>
        /// Try to resolve a service instance
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <param name="service">The resolved service instance</param>
        /// <returns>True if the service was resolved successfully</returns>
        bool TryResolve<T>(out T service) where T : class;

        /// <summary>
        /// Check if a service is registered
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <returns>True if the service is registered</returns>
        bool IsRegistered<T>() where T : class;
    }
}
