<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="packages\Costura.Fody.6.0.0-beta0000\build\Costura.Fody.props" Condition="Exists('packages\Costura.Fody.6.0.0-beta0000\build\Costura.Fody.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{EAC2AB81-F09E-4BE3-BF3D-97AAF2F40D76}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>IECAD</RootNamespace>
    <AssemblyName>IEDesignHelper</AssemblyName>
    <!-- Change the targeted .NET Framework version -->
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <LangVersion>preview</LangVersion>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>
    <FileAlignment>512</FileAlignment>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <StartAction>Program</StartAction>
    <!-- Change the path to the installation folder of the  targeted AutoCAD version -->
    <StartProgram>C:\Program Files\Autodesk\AutoCAD 2024\acad.exe</StartProgram>
    <StartArguments>/nologo /b "start.scr"</StartArguments>
  </PropertyGroup>
  <!-- 添加Release配置 -->
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\\Release\\</OutputPath>
    <Optimize>true</Optimize>
    <DefineConstants>TRACE</DefineConstants>
    <DocumentationFile>
    </DocumentationFile>
  </PropertyGroup>
  <ItemGroup>
    <!-- Change the paths to the targeted AutoCAD libraries -->
    <Reference Include="AcCoreMgd">
      <HintPath>..\..\..\..\..\Program Files\Autodesk\AutoCAD 2024\accoremgd.dll</HintPath>
      <Private>False</Private>
      <EmbedInteropTypes>False</EmbedInteropTypes>
    </Reference>
    <Reference Include="AcCui">
      <HintPath>..\..\..\..\..\Program Files\Autodesk\AutoCAD 2024\AcCui.dll</HintPath>
      <Private>False</Private>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="acdbmgd">
      <HintPath>..\..\..\..\..\Program Files\Autodesk\AutoCAD 2024\acdbmgd.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="acmgd">
      <HintPath>..\..\..\..\..\Program Files\Autodesk\AutoCAD 2024\acmgd.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AcMPolygonMGD">
      <HintPath>..\..\..\..\..\Program Files\Autodesk\AutoCAD 2024\AcMPolygonMGD.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AcWindows">
      <HintPath>..\..\..\..\..\Program Files\Autodesk\AutoCAD 2024\AcWindows.dll</HintPath>
      <Private>False</Private>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="AdWindows">
      <HintPath>..\..\..\..\..\Program Files\Autodesk\AutoCAD 2024\AdWindows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Clipper2Lib, Version=*******, Culture=neutral, PublicKeyToken=ddeb3f68c442d6e4, processorArchitecture=MSIL">
      <HintPath>packages\Clipper2.1.4.0\lib\netstandard2.0\Clipper2Lib.dll</HintPath>
    </Reference>
    <Reference Include="Costura, Version=*******, Culture=neutral, PublicKeyToken=9919ef960d84173d, processorArchitecture=MSIL">
      <HintPath>packages\Costura.Fody.6.0.0-beta0000\lib\netstandard2.0\Costura.dll</HintPath>
    </Reference>
    <Reference Include="ExcelDataReader, Version=*******, Culture=neutral, PublicKeyToken=93517dbe6a4012fa, processorArchitecture=MSIL">
      <HintPath>packages\ExcelDataReader.3.7.0\lib\net462\ExcelDataReader.dll</HintPath>
    </Reference>
    <Reference Include="ExcelDataReader.DataSet, Version=*******, Culture=neutral, PublicKeyToken=93517dbe6a4012fa, processorArchitecture=MSIL">
      <HintPath>packages\ExcelDataReader.DataSet.3.7.0\lib\net462\ExcelDataReader.DataSet.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
    <Reference Include="WindowsFormsIntegration" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Commands\AlignBlockCommand.cs" />
    <Compile Include="Commands\ArrangeBlocksCommand.cs" />
    <Compile Include="Commands\ExtractOutlineCommand.cs" />
    <Compile Include="Commands\ImportRoomPlanCommand.cs" />
    <Compile Include="Commands\CreateBlockCommand.cs" />
    <Compile Include="Commands\FlattenBlocksCommand.cs" />
    <Compile Include="Commands\TestLayerManagerCommand.cs" />
    <Compile Include="Commands\TestLoggingCommand.cs" />
    <Compile Include="Constants\ApplicationConstants.cs" />
    <Compile Include="Services\AlignService.cs" />
    <Compile Include="Services\BatchTextEditorService.cs" />
    <Compile Include="Services\BlockArrangementService.cs" />
    <Compile Include="Services\ErrorHandlingService.cs" />
    <Compile Include="Services\FileDialogService.cs" />
    <Compile Include="Services\FileLoggingService.cs" />
    <Compile Include="Services\IAutoCADService.cs" />
    <Compile Include="Services\IEquipmentCreatorService.cs" />
    <Compile Include="Services\IErrorHandlingService.cs" />
    <Compile Include="Services\ILoggingService.cs" />
    <Compile Include="Services\IPerformanceMonitoringService.cs" />
    <Compile Include="Services\IPerformanceOptimizationService.cs" />
    <Compile Include="Services\IServiceContainer.cs" />
    <Compile Include="Services\IUserSettingsService.cs" />
    <Compile Include="Services\PerformanceMonitoringService.cs" />
    <Compile Include="Services\PerformanceOptimizationService.cs" />
    <Compile Include="Services\UserSettingsService.cs" />
    <Compile Include="Services\OutlineExtractor.cs" />
    <Compile Include="Services\OutlineWalker\IOutlineWalker.cs" />
    <Compile Include="Services\OutlineWalker\WalkerDataStructures.cs" />
    <Compile Include="Services\OutlineWalker\OutlineWalkerEngine.cs" />
    <Compile Include="Services\OutlineWalker\PreciseWalker.cs" />
    <Compile Include="Services\OutlineWalker\StepwiseIntersectionFinder.cs" />
    <Compile Include="Services\OutlineWalker\TolerantWalker.cs" />
    <Compile Include="Services\OutlineWalker\MorphologicalWalker.cs" />
    <Compile Include="Services\OutlineWalker\GridWalker.cs" />
    <Compile Include="Services\OutlineWalker\OutlineWalkerTester.cs" />
    <Compile Include="Services\RoomPlanImporter.cs" />
    <Compile Include="Services\ServiceConfiguration.cs" />
    <Compile Include="Services\SimpleServiceContainer.cs" />
    <Compile Include="Commands\SidebarCommands.cs" />
    <Compile Include="Helpers\AsyncRelayCommand.cs" />
    <Compile Include="Helpers\AutoCADObjectCache.cs" />
    <Compile Include="Helpers\AutoCADTransactionHelper.cs" />
    <Compile Include="Helpers\EmbeddedImageHelper.cs" />
    <Compile Include="Helpers\MemoryManager.cs" />
    <Compile Include="Helpers\ObjectPool.cs" />
    <Compile Include="Helpers\RelayCommand.cs" />
    <Compile Include="Helpers\ValueConverters.cs" />
    <Compile Include="LoggingTest.cs" />
    <Compile Include="Models\LayerModel.cs" />
    <Compile Include="PluginLoader.cs" />
    <Compile Include="Commands\BatchEditTextCommand.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Services\AutoCADService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Services\BlockFlattener.cs" />
    <Compile Include="Services\BatchBlockFlatterner.cs" />
    <Compile Include="Services\EquipmentCreator.cs" />
    <Compile Include="ViewModels\BaseViewModel.cs" />
    <Compile Include="ViewModels\LayerManagerViewModel.cs" />
    <Compile Include="ViewModels\MainSidebarViewModel.cs" />
    <Compile Include="ViewModels\PerformanceDashboardViewModel.cs" />
    <Compile Include="Views\BatchEditTextPalette.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Views\LayerManagerControl.xaml.cs">
      <DependentUpon>LayerManagerControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\MainSidebarControl.xaml.cs">
      <DependentUpon>MainSidebarControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\ProgressIndicator.xaml.cs">
      <DependentUpon>ProgressIndicator.xaml</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
    <None Include="start.scr">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Page Include="Views\LayerManagerControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\MainSidebarControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\ProgressIndicator.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Resources\logo.png">
      <LogicalName>IECAD.Resources.logo.png</LogicalName>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\RoomPlan.dwg" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="packages\Fody.6.8.2\build\Fody.targets" Condition="Exists('packages\Fody.6.8.2\build\Fody.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('packages\Fody.6.8.2\build\Fody.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\Fody.6.8.2\build\Fody.targets'))" />
    <Error Condition="!Exists('packages\Costura.Fody.6.0.0-beta0000\build\Costura.Fody.props')" Text="$([System.String]::Format('$(ErrorText)', 'packages\Costura.Fody.6.0.0-beta0000\build\Costura.Fody.props'))" />
    <Error Condition="!Exists('packages\Costura.Fody.6.0.0-beta0000\build\Costura.Fody.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\Costura.Fody.6.0.0-beta0000\build\Costura.Fody.targets'))" />
  </Target>
  <Import Project="packages\Costura.Fody.6.0.0-beta0000\build\Costura.Fody.targets" Condition="Exists('packages\Costura.Fody.6.0.0-beta0000\build\Costura.Fody.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>