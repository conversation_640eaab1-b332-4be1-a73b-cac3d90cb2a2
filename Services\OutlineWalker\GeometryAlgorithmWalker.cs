using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using System;
using System.Collections.Generic;
using System.Linq;

namespace IECAD.Services.OutlineWalker
{
    /// <summary>
    /// 几何算法步行者 - 使用NetTopologySuite进行高级几何运算
    /// </summary>
    public class GeometryAlgorithmWalker : IOutlineWalker
    {
        private readonly GeometryAlgorithmService _geometryService;
        private List<Entity> _entities;
        private List<Point3d> _resultPath;
        private double _tolerance;
        private bool _isCompleted;
        private Point3d _currentPosition;
        private GeometryAlgorithmType _algorithmType;

        /// <summary>
        /// 几何算法类型
        /// </summary>
        public enum GeometryAlgorithmType
        {
            /// <summary>
            /// 凸包算法
            /// </summary>
            ConvexHull,
            
            /// <summary>
            /// 边界提取算法
            /// </summary>
            Boundary,
            
            /// <summary>
            /// Alpha Shape（凹包）算法
            /// </summary>
            AlphaShape
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="algorithmType">算法类型</param>
        public GeometryAlgorithmWalker(GeometryAlgorithmType algorithmType = GeometryAlgorithmType.ConvexHull)
        {
            _algorithmType = algorithmType;
            _geometryService = new GeometryAlgorithmService();
            _resultPath = new List<Point3d>();
            _isCompleted = false;
            _currentPosition = Point3d.Origin;
        }

        /// <summary>
        /// 步行者策略名称
        /// </summary>
        public string StrategyName => $"几何算法步行者 ({GetAlgorithmName()})";

        /// <summary>
        /// 当前位置
        /// </summary>
        public Point3d CurrentPosition => _currentPosition;

        /// <summary>
        /// 检查是否已完成轮廓追踪
        /// </summary>
        public bool IsCompleted => _isCompleted;

        /// <summary>
        /// 初始化步行者，找到起点和初始状态
        /// </summary>
        /// <param name="entities">要处理的几何对象集合</param>
        /// <param name="tolerance">容差参数</param>
        public void Initialize(IEnumerable<Entity> entities, double tolerance = 1.0)
        {
            try
            {
                _entities = entities?.ToList() ?? new List<Entity>();
                _tolerance = tolerance;
                _isCompleted = false;
                _resultPath.Clear();

                if (_entities.Count == 0)
                {
                    _isCompleted = true;
                    return;
                }

                // 智能选择算法类型：根据tolerance参数自动选择最合适的算法
                var selectedAlgorithm = DetermineOptimalAlgorithm(tolerance);

                // 根据算法类型执行相应的几何运算
                switch (selectedAlgorithm)
                {
                    case GeometryAlgorithmType.ConvexHull:
                        _resultPath = _geometryService.ExtractConvexHull(_entities);
                        break;

                    case GeometryAlgorithmType.Boundary:
                        _resultPath = _geometryService.ExtractBoundary(_entities, tolerance);
                        break;

                    case GeometryAlgorithmType.AlphaShape:
                        // 为Alpha Shape使用优化的参数
                        var alphaParam = GetOptimalAlphaParameter(tolerance);
                        _resultPath = _geometryService.ExtractAlphaShape(_entities, alphaParam);
                        break;

                    default:
                        _resultPath = _geometryService.ExtractConvexHull(_entities);
                        break;
                }

                // 设置起始位置
                if (_resultPath.Count > 0)
                {
                    _currentPosition = _resultPath[0];
                }

                // 几何算法是一次性计算，直接标记为完成
                _isCompleted = true;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"几何算法步行者初始化失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据tolerance参数智能选择最优算法
        /// </summary>
        /// <param name="tolerance">容差参数</param>
        /// <returns>推荐的算法类型</returns>
        private GeometryAlgorithmType DetermineOptimalAlgorithm(double tolerance)
        {
            // 如果已经明确设置了算法类型，则使用设置的类型
            if (_algorithmType != GeometryAlgorithmType.ConvexHull)
            {
                return _algorithmType;
            }

            // 根据tolerance值的范围智能选择算法
            if (tolerance <= 0.1)
            {
                // 小容差：使用凸包算法，获得最简单的外轮廓
                return GeometryAlgorithmType.ConvexHull;
            }
            else if (tolerance <= 2.0)
            {
                // 中等容差：使用边界提取，保持形状特征
                return GeometryAlgorithmType.Boundary;
            }
            else
            {
                // 大容差：使用Alpha Shape，允许更复杂的凹包形状
                return GeometryAlgorithmType.AlphaShape;
            }
        }

        /// <summary>
        /// 设置Alpha Shape的推荐参数值
        /// </summary>
        /// <param name="tolerance">用户输入的tolerance</param>
        /// <returns>调整后的Alpha参数</returns>
        private double GetOptimalAlphaParameter(double tolerance)
        {
            // 将tolerance转换为合适的Alpha参数
            // Alpha值越小，凹包越"凹"；Alpha值越大，越接近凸包
            if (tolerance <= 0.5)
            {
                return 0.5; // 较小的Alpha，产生更凹的形状
            }
            else if (tolerance <= 2.0)
            {
                return tolerance; // 直接使用tolerance作为Alpha
            }
            else
            {
                return Math.Min(tolerance, 5.0); // 限制最大Alpha值
            }
        }

        /// <summary>
        /// 寻找下一个决策点/事件
        /// </summary>
        /// <returns>事件结果</returns>
        public WalkerEventResult FindNextEvent()
        {
            // 几何算法步行者是一次性计算，不需要逐步寻找事件
            return new WalkerEventResult
            {
                EventType = _isCompleted ? WalkerEventType.Completed : WalkerEventType.OutlineClosed,
                Position = _currentPosition,
                AvailableDirections = new List<WalkDirection>()
            };
        }

        /// <summary>
        /// 根据当前事件决定下一步方向
        /// </summary>
        /// <param name="eventResult">事件结果</param>
        /// <returns>方向决策</returns>
        public DirectionDecision MakeDecision(WalkerEventResult eventResult)
        {
            // 几何算法步行者不需要逐步决策
            return new DirectionDecision
            {
                ChosenDirection = new WalkDirection(),
                NextPosition = _currentPosition,
                RequiresBacktrack = false,
                Reason = "几何算法一次性计算完成"
            };
        }

        /// <summary>
        /// 更新步行者状态，包括位置和路径记录
        /// </summary>
        /// <param name="decision">方向决策</param>
        public void UpdateState(DirectionDecision decision)
        {
            // 几何算法步行者不需要逐步更新状态
            // 状态在Initialize时已经完成计算
        }

        /// <summary>
        /// 获取最终的轮廓路径
        /// </summary>
        /// <returns>顶点列表</returns>
        public List<Point3d> GetResultPath()
        {
            return new List<Point3d>(_resultPath);
        }

        /// <summary>
        /// 设置算法类型
        /// </summary>
        /// <param name="algorithmType">算法类型</param>
        public void SetAlgorithmType(GeometryAlgorithmType algorithmType)
        {
            _algorithmType = algorithmType;
        }

        /// <summary>
        /// 获取算法名称
        /// </summary>
        /// <returns>算法名称</returns>
        private string GetAlgorithmName()
        {
            switch (_algorithmType)
            {
                case GeometryAlgorithmType.ConvexHull:
                    return "凸包算法";
                case GeometryAlgorithmType.Boundary:
                    return "边界提取";
                case GeometryAlgorithmType.AlphaShape:
                    return "Alpha Shape";
                default:
                    return "未知算法";
            }
        }

        /// <summary>
        /// 获取算法描述
        /// </summary>
        /// <returns>算法描述</returns>
        public string GetAlgorithmDescription()
        {
            switch (_algorithmType)
            {
                case GeometryAlgorithmType.ConvexHull:
                    return "计算实体集合的凸包，适用于获取最外层轮廓";
                case GeometryAlgorithmType.Boundary:
                    return "提取实体集合的边界，保持原始形状特征";
                case GeometryAlgorithmType.AlphaShape:
                    return "计算Alpha Shape凹包，可调节凹凸程度";
                default:
                    return "高级几何算法轮廓提取";
            }
        }

        /// <summary>
        /// 获取支持的算法类型列表
        /// </summary>
        /// <returns>算法类型列表</returns>
        public static List<GeometryAlgorithmType> GetSupportedAlgorithms()
        {
            return new List<GeometryAlgorithmType>
            {
                GeometryAlgorithmType.ConvexHull,
                GeometryAlgorithmType.Boundary,
                GeometryAlgorithmType.AlphaShape
            };
        }

        /// <summary>
        /// 创建指定算法类型的步行者实例
        /// </summary>
        /// <param name="algorithmType">算法类型</param>
        /// <returns>步行者实例</returns>
        public static GeometryAlgorithmWalker CreateWalker(GeometryAlgorithmType algorithmType)
        {
            return new GeometryAlgorithmWalker(algorithmType);
        }
    }
}
