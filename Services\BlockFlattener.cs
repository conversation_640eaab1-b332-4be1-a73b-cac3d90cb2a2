﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;

namespace IECAD.Services
{
    public class BlockFlattener
    {

        public void FlattenBlocks()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord modelSpace = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                // Prompt for block to flatten
                PromptEntityOptions peo = new PromptEntityOptions("\nSelect block reference: ");
                peo.SetRejectMessage("\nObject must be a block reference.");
                peo.AddAllowedClass(typeof(BlockReference), false);

                PromptEntityResult per = ed.GetEntity(peo);
                if (per.Status != PromptStatus.OK) return;

                BlockReference blockRef = tr.GetObject(per.ObjectId, OpenMode.ForRead) as BlockReference;
                BlockTableRecord blockDef = tr.GetObject(blockRef.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;

                // Create a new block definition for flattened contents
                string newBlockDefName = blockDef.Name + "_Flattened";
                if (bt.Has(newBlockDefName)) // Check if the block already exists
                {
                    // Optional: You can either delete it or generate a new name
                    // bt.Remove(newBlockDefName); // Un-comment if you want to overwrite the existing block
                    newBlockDefName += "_1"; // Change name to avoid duplicate
                }

                BlockTableRecord newBlockDef = new BlockTableRecord
                {
                    Name = newBlockDefName
                };

                bt.UpgradeOpen();
                ObjectId newBlockDefId = bt.Add(newBlockDef);
                tr.AddNewlyCreatedDBObject(newBlockDef, true);

                // Recursively add all entities from the block definition using the block transformation
                FlattenNestedBlocks(Matrix3d.Identity, blockDef, newBlockDef, tr);

                // Calculate the transform for the new block to keep the original position
                BlockReference newBlockRef = new BlockReference(Point3d.Origin, newBlockDefId)
                {
                    Rotation = 0,
                    ScaleFactors = new Scale3d(1)
                };

                // Apply only the block reference's transformation separately
                newBlockRef.TransformBy(blockRef.BlockTransform);

                modelSpace.AppendEntity(newBlockRef);
                tr.AddNewlyCreatedDBObject(newBlockRef, true);

                blockRef.UpgradeOpen();
                blockRef.Erase();

                // Commit changes
                tr.Commit();
            }
        }

        private void FlattenNestedBlocks(Matrix3d parentTransform, BlockTableRecord sourceBlock, BlockTableRecord targetBlock, Transaction tr)
        {
            foreach (ObjectId entId in sourceBlock)
            {
                Entity ent = tr.GetObject(entId, OpenMode.ForRead) as Entity;
                if (ent is BlockReference nestedBlockRef)
                {
                    BlockTableRecord nestedBlockDef = tr.GetObject(nestedBlockRef.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
                    // Compute the transformation for nested block
                    Matrix3d nestedTransform = parentTransform * nestedBlockRef.BlockTransform;
                    FlattenNestedBlocks(nestedTransform, nestedBlockDef, targetBlock, tr);
                }
                else
                {
                    // Clone the entity and transform it
                    Entity clonedEnt = ent.Clone() as Entity;
                    clonedEnt.TransformBy(parentTransform);
                    targetBlock.AppendEntity(clonedEnt);
                    tr.AddNewlyCreatedDBObject(clonedEnt, true);
                }
            }
        }
    }
}



