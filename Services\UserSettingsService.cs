using System;
using System.Globalization;
using Microsoft.Win32;
using Autodesk.AutoCAD.ApplicationServices;

namespace IECAD.Services
{
    /// <summary>
    /// User settings service that provides persistent storage using AutoCAD system variables and Windows Registry as fallback
    /// </summary>
    public class UserSettingsService : IUserSettingsService
    {
        private readonly ILoggingService _loggingService;
        private readonly IErrorHandlingService _errorHandlingService;
        private const string REGISTRY_KEY_PATH = @"SOFTWARE\IECAD\Settings";
        private const string AUTOCAD_VAR_PREFIX = "IECAD_";

        public UserSettingsService(ILoggingService loggingService = null, IErrorHandlingService errorHandlingService = null)
        {
            _loggingService = loggingService;
            _errorHandlingService = errorHandlingService;
        }

        public double GetDouble(string key, double defaultValue = 0.0)
        {
            try
            {
                // Try AutoCAD system variable first
                string autoCADKey = AUTOCAD_VAR_PREFIX + key.ToUpper();
                if (TryGetAutoCADVariable(autoCADKey, out object value))
                {
                    if (value is double doubleValue)
                        return doubleValue;
                    if (double.TryParse(value.ToString(), NumberStyles.Float, CultureInfo.InvariantCulture, out double parsedValue))
                        return parsedValue;
                }

                // Fallback to registry
                return GetRegistryDouble(key, defaultValue);
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning($"Failed to get double setting '{key}': {ex.Message}", "UserSettingsService");
                return defaultValue;
            }
        }

        public bool SetDouble(string key, double value)
        {
            try
            {
                bool success = false;

                // Try to set AutoCAD system variable first
                string autoCADKey = AUTOCAD_VAR_PREFIX + key.ToUpper();
                if (TrySetAutoCADVariable(autoCADKey, value))
                {
                    success = true;
                    _loggingService?.LogDebug($"Saved setting '{key}' to AutoCAD variable: {value}", "UserSettingsService");
                }

                // Also save to registry as backup
                if (SetRegistryDouble(key, value))
                {
                    success = true;
                    _loggingService?.LogDebug($"Saved setting '{key}' to registry: {value}", "UserSettingsService");
                }

                return success;
            }
            catch (Exception ex)
            {
                _errorHandlingService?.HandleException(ex, $"Failed to set double setting '{key}'", "UserSettingsService", false);
                return false;
            }
        }

        public string GetString(string key, string defaultValue = "")
        {
            try
            {
                // Try AutoCAD system variable first
                string autoCADKey = AUTOCAD_VAR_PREFIX + key.ToUpper();
                if (TryGetAutoCADVariable(autoCADKey, out object value))
                {
                    return value?.ToString() ?? defaultValue;
                }

                // Fallback to registry
                return GetRegistryString(key, defaultValue);
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning($"Failed to get string setting '{key}': {ex.Message}", "UserSettingsService");
                return defaultValue;
            }
        }

        public bool SetString(string key, string value)
        {
            try
            {
                bool success = false;

                // Try to set AutoCAD system variable first
                string autoCADKey = AUTOCAD_VAR_PREFIX + key.ToUpper();
                if (TrySetAutoCADVariable(autoCADKey, value))
                {
                    success = true;
                }

                // Also save to registry as backup
                if (SetRegistryString(key, value))
                {
                    success = true;
                }

                return success;
            }
            catch (Exception ex)
            {
                _errorHandlingService?.HandleException(ex, $"Failed to set string setting '{key}'", "UserSettingsService", false);
                return false;
            }
        }

        public int GetInt(string key, int defaultValue = 0)
        {
            try
            {
                // Try AutoCAD system variable first
                string autoCADKey = AUTOCAD_VAR_PREFIX + key.ToUpper();
                if (TryGetAutoCADVariable(autoCADKey, out object value))
                {
                    if (value is int intValue)
                        return intValue;
                    if (int.TryParse(value.ToString(), out int parsedValue))
                        return parsedValue;
                }

                // Fallback to registry
                return GetRegistryInt(key, defaultValue);
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning($"Failed to get int setting '{key}': {ex.Message}", "UserSettingsService");
                return defaultValue;
            }
        }

        public bool SetInt(string key, int value)
        {
            try
            {
                bool success = false;

                // Try to set AutoCAD system variable first
                string autoCADKey = AUTOCAD_VAR_PREFIX + key.ToUpper();
                if (TrySetAutoCADVariable(autoCADKey, value))
                {
                    success = true;
                }

                // Also save to registry as backup
                if (SetRegistryInt(key, value))
                {
                    success = true;
                }

                return success;
            }
            catch (Exception ex)
            {
                _errorHandlingService?.HandleException(ex, $"Failed to set int setting '{key}'", "UserSettingsService", false);
                return false;
            }
        }

        public bool GetBool(string key, bool defaultValue = false)
        {
            try
            {
                // Try AutoCAD system variable first
                string autoCADKey = AUTOCAD_VAR_PREFIX + key.ToUpper();
                if (TryGetAutoCADVariable(autoCADKey, out object value))
                {
                    if (value is bool boolValue)
                        return boolValue;
                    if (bool.TryParse(value.ToString(), out bool parsedValue))
                        return parsedValue;
                    // Handle numeric boolean (0/1)
                    if (int.TryParse(value.ToString(), out int intValue))
                        return intValue != 0;
                }

                // Fallback to registry
                return GetRegistryBool(key, defaultValue);
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning($"Failed to get bool setting '{key}': {ex.Message}", "UserSettingsService");
                return defaultValue;
            }
        }

        public bool SetBool(string key, bool value)
        {
            try
            {
                bool success = false;

                // Try to set AutoCAD system variable first (as integer 0/1)
                string autoCADKey = AUTOCAD_VAR_PREFIX + key.ToUpper();
                if (TrySetAutoCADVariable(autoCADKey, value ? 1 : 0))
                {
                    success = true;
                }

                // Also save to registry as backup
                if (SetRegistryBool(key, value))
                {
                    success = true;
                }

                return success;
            }
            catch (Exception ex)
            {
                _errorHandlingService?.HandleException(ex, $"Failed to set bool setting '{key}'", "UserSettingsService", false);
                return false;
            }
        }

        public bool HasSetting(string key)
        {
            try
            {
                // Check AutoCAD system variable first
                string autoCADKey = AUTOCAD_VAR_PREFIX + key.ToUpper();
                if (TryGetAutoCADVariable(autoCADKey, out _))
                {
                    return true;
                }

                // Check registry
                return HasRegistrySetting(key);
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning($"Failed to check setting '{key}': {ex.Message}", "UserSettingsService");
                return false;
            }
        }

        public bool RemoveSetting(string key)
        {
            try
            {
                bool success = false;

                // Remove from registry
                if (RemoveRegistrySetting(key))
                {
                    success = true;
                }

                // Note: AutoCAD system variables cannot be easily removed, they just get reset to default
                // We could set them to a special "unset" value, but for simplicity we'll rely on registry removal

                return success;
            }
            catch (Exception ex)
            {
                _errorHandlingService?.HandleException(ex, $"Failed to remove setting '{key}'", "UserSettingsService", false);
                return false;
            }
        }

        public bool ClearAllSettings()
        {
            try
            {
                return ClearRegistrySettings();
            }
            catch (Exception ex)
            {
                _errorHandlingService?.HandleException(ex, "Failed to clear all settings", "UserSettingsService", false);
                return false;
            }
        }

        #region AutoCAD System Variables

        private bool TryGetAutoCADVariable(string variableName, out object value)
        {
            value = null;
            try
            {
                var doc = Application.DocumentManager.MdiActiveDocument;
                if (doc == null) return false;

                value = Application.GetSystemVariable(variableName);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private bool TrySetAutoCADVariable(string variableName, object value)
        {
            try
            {
                var doc = Application.DocumentManager.MdiActiveDocument;
                if (doc == null) return false;

                Application.SetSystemVariable(variableName, value);
                return true;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Registry Operations

        private RegistryKey GetSettingsKey(bool writable = false)
        {
            try
            {
                return Registry.CurrentUser.CreateSubKey(REGISTRY_KEY_PATH, writable);
            }
            catch
            {
                return null;
            }
        }

        private double GetRegistryDouble(string key, double defaultValue)
        {
            using (var regKey = GetSettingsKey())
            {
                if (regKey == null) return defaultValue;
                
                var value = regKey.GetValue(key, defaultValue.ToString(CultureInfo.InvariantCulture));
                if (double.TryParse(value.ToString(), NumberStyles.Float, CultureInfo.InvariantCulture, out double result))
                    return result;
                
                return defaultValue;
            }
        }

        private bool SetRegistryDouble(string key, double value)
        {
            try
            {
                using (var regKey = GetSettingsKey(true))
                {
                    if (regKey == null) return false;
                    regKey.SetValue(key, value.ToString(CultureInfo.InvariantCulture));
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        private string GetRegistryString(string key, string defaultValue)
        {
            using (var regKey = GetSettingsKey())
            {
                if (regKey == null) return defaultValue;
                return regKey.GetValue(key, defaultValue)?.ToString() ?? defaultValue;
            }
        }

        private bool SetRegistryString(string key, string value)
        {
            try
            {
                using (var regKey = GetSettingsKey(true))
                {
                    if (regKey == null) return false;
                    regKey.SetValue(key, value ?? "");
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        private int GetRegistryInt(string key, int defaultValue)
        {
            using (var regKey = GetSettingsKey())
            {
                if (regKey == null) return defaultValue;
                return (int)regKey.GetValue(key, defaultValue);
            }
        }

        private bool SetRegistryInt(string key, int value)
        {
            try
            {
                using (var regKey = GetSettingsKey(true))
                {
                    if (regKey == null) return false;
                    regKey.SetValue(key, value);
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        private bool GetRegistryBool(string key, bool defaultValue)
        {
            using (var regKey = GetSettingsKey())
            {
                if (regKey == null) return defaultValue;
                var value = regKey.GetValue(key, defaultValue ? 1 : 0);
                return Convert.ToInt32(value) != 0;
            }
        }

        private bool SetRegistryBool(string key, bool value)
        {
            try
            {
                using (var regKey = GetSettingsKey(true))
                {
                    if (regKey == null) return false;
                    regKey.SetValue(key, value ? 1 : 0);
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        private bool HasRegistrySetting(string key)
        {
            using (var regKey = GetSettingsKey())
            {
                if (regKey == null) return false;
                return regKey.GetValue(key) != null;
            }
        }

        private bool RemoveRegistrySetting(string key)
        {
            try
            {
                using (var regKey = GetSettingsKey(true))
                {
                    if (regKey == null) return false;
                    regKey.DeleteValue(key, false);
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        private bool ClearRegistrySettings()
        {
            try
            {
                Registry.CurrentUser.DeleteSubKeyTree(REGISTRY_KEY_PATH, false);
                return true;
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }
}
