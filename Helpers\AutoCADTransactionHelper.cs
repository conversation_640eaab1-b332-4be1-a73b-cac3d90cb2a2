using System;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using IECAD.Services;

namespace IECAD.Helpers
{
    /// <summary>
    /// Helper class for managing AutoCAD transactions efficiently
    /// </summary>
    public static class AutoCADTransactionHelper
    {
        /// <summary>
        /// Execute an action within a transaction with proper error handling and resource disposal
        /// </summary>
        /// <param name="action">The action to execute within the transaction</param>
        /// <param name="loggingService">Optional logging service for debugging</param>
        /// <param name="errorHandlingService">Optional error handling service</param>
        /// <returns>True if the transaction was successful</returns>
        public static bool ExecuteInTransaction(Action<Transaction, Database> action, 
            ILoggingService loggingService = null, 
            IErrorHandlingService errorHandlingService = null)
        {
            if (action == null) throw new ArgumentNullException(nameof(action));

            try
            {
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc == null)
                {
                    errorHandlingService?.HandleError("No active AutoCAD document", "AutoCADTransactionHelper");
                    return false;
                }

                Database db = doc.Database;
                
                using (DocumentLock docLock = doc.LockDocument())
                using (Transaction trans = db.TransactionManager.StartTransaction())
                {
                    loggingService?.LogDebug("Transaction started", "AutoCADTransactionHelper");
                    
                    action(trans, db);
                    
                    trans.Commit();
                    loggingService?.LogDebug("Transaction committed successfully", "AutoCADTransactionHelper");
                    return true;
                }
            }
            catch (Exception ex)
            {
                errorHandlingService?.HandleException(ex, "Transaction failed", "AutoCADTransactionHelper");
                return false;
            }
        }

        /// <summary>
        /// Execute a function within a transaction and return a result
        /// </summary>
        /// <typeparam name="T">The return type</typeparam>
        /// <param name="func">The function to execute within the transaction</param>
        /// <param name="defaultValue">Default value to return if transaction fails</param>
        /// <param name="loggingService">Optional logging service for debugging</param>
        /// <param name="errorHandlingService">Optional error handling service</param>
        /// <returns>The result of the function or default value if failed</returns>
        public static T ExecuteInTransaction<T>(Func<Transaction, Database, T> func, 
            T defaultValue = default(T),
            ILoggingService loggingService = null, 
            IErrorHandlingService errorHandlingService = null)
        {
            if (func == null) throw new ArgumentNullException(nameof(func));

            try
            {
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc == null)
                {
                    errorHandlingService?.HandleError("No active AutoCAD document", "AutoCADTransactionHelper");
                    return defaultValue;
                }

                Database db = doc.Database;
                
                using (DocumentLock docLock = doc.LockDocument())
                using (Transaction trans = db.TransactionManager.StartTransaction())
                {
                    loggingService?.LogDebug("Transaction started", "AutoCADTransactionHelper");
                    
                    T result = func(trans, db);
                    
                    trans.Commit();
                    loggingService?.LogDebug("Transaction committed successfully", "AutoCADTransactionHelper");
                    return result;
                }
            }
            catch (Exception ex)
            {
                errorHandlingService?.HandleException(ex, "Transaction failed", "AutoCADTransactionHelper");
                return defaultValue;
            }
        }

        /// <summary>
        /// Execute multiple operations in a single transaction for better performance
        /// </summary>
        /// <param name="operations">Array of operations to execute</param>
        /// <param name="loggingService">Optional logging service for debugging</param>
        /// <param name="errorHandlingService">Optional error handling service</param>
        /// <returns>True if all operations were successful</returns>
        public static bool ExecuteBatchInTransaction(Action<Transaction, Database>[] operations,
            ILoggingService loggingService = null,
            IErrorHandlingService errorHandlingService = null)
        {
            if (operations == null || operations.Length == 0) return true;

            return ExecuteInTransaction((trans, db) =>
            {
                loggingService?.LogDebug($"Executing {operations.Length} batch operations", "AutoCADTransactionHelper");
                
                for (int i = 0; i < operations.Length; i++)
                {
                    try
                    {
                        operations[i](trans, db);
                        loggingService?.LogDebug($"Batch operation {i + 1} completed", "AutoCADTransactionHelper");
                    }
                    catch (Exception ex)
                    {
                        errorHandlingService?.HandleException(ex, $"Batch operation {i + 1} failed", "AutoCADTransactionHelper");
                        throw; // Re-throw to abort the entire transaction
                    }
                }
            }, loggingService, errorHandlingService);
        }

        /// <summary>
        /// Check if AutoCAD is ready for database operations
        /// </summary>
        /// <returns>True if AutoCAD is ready</returns>
        public static bool IsAutoCADReady()
        {
            try
            {
                var docManager = Application.DocumentManager;
                if (docManager == null) return false;

                var activeDoc = docManager.MdiActiveDocument;
                return activeDoc != null && activeDoc.Database != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Get the current active document safely
        /// </summary>
        /// <returns>The active document or null if not available</returns>
        public static Document GetActiveDocument()
        {
            try
            {
                return Application.DocumentManager?.MdiActiveDocument;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Get the current active database safely
        /// </summary>
        /// <returns>The active database or null if not available</returns>
        public static Database GetActiveDatabase()
        {
            try
            {
                return GetActiveDocument()?.Database;
            }
            catch
            {
                return null;
            }
        }
    }
}
