using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using IECAD.Helpers;
using IECAD.Constants;

namespace IECAD.Services
{
    /// <summary>
    /// Service for arranging blocks based on their insertion points
    /// </summary>
    public class BlockArrangementService
    {
        private readonly IErrorHandlingService _errorHandlingService;
        private readonly ILoggingService _loggingService;
        private readonly IUserSettingsService _settingsService;

        public BlockArrangementService(IErrorHandlingService errorHandlingService = null, ILoggingService loggingService = null, IUserSettingsService settingsService = null)
        {
            _errorHandlingService = errorHandlingService ?? ServiceConfiguration.Resolve<IErrorHandlingService>();
            _loggingService = loggingService ?? ServiceConfiguration.Resolve<ILoggingService>();
            _settingsService = settingsService ?? ServiceConfiguration.Resolve<IUserSettingsService>();
        }

        /// <summary>
        /// Main method to arrange selected blocks
        /// </summary>
        public void ArrangeBlocks()
        {
            try
            {
                var doc = Application.DocumentManager.MdiActiveDocument;
                if (doc == null)
                {
                    _errorHandlingService?.HandleError("No active AutoCAD document found.", "BlockArrangementService");
                    return;
                }

                var ed = doc.Editor;

                // Step 1: Select multiple blocks
                var selectedBlocks = SelectBlocks(ed);
                if (selectedBlocks == null || selectedBlocks.Count < 2)
                {
                    ed.WriteMessage("\nAt least 2 blocks must be selected for arrangement.");
                    return;
                }

                // Step 2: Select reference block
                var referenceBlockId = SelectReferenceBlock(ed, selectedBlocks);
                if (referenceBlockId == ObjectId.Null)
                {
                    ed.WriteMessage("\nReference block selection cancelled.");
                    return;
                }

                // Step 3: Get spacing distance
                var spacing = GetSpacingDistance(ed);
                if (spacing <= 0)
                {
                    ed.WriteMessage("\nInvalid spacing distance provided.");
                    return;
                }

                // Step 4: Arrange blocks
                ArrangeBlocksInLine(selectedBlocks, referenceBlockId, spacing);

                // Step 5: Save the successfully used spacing for next time
                SaveLastUsedSpacing(spacing);

                ed.WriteMessage($"\nSuccessfully arranged {selectedBlocks.Count} blocks with spacing {spacing}.");
            }
            catch (Exception ex)
            {
                _errorHandlingService?.HandleException(ex, "Failed to arrange blocks", "BlockArrangementService");
            }
        }

        /// <summary>
        /// Select multiple blocks from the drawing
        /// </summary>
        private List<ObjectId> SelectBlocks(Editor ed)
        {
            var pso = new PromptSelectionOptions
            {
                MessageForAdding = "\nSelect blocks to arrange (minimum 2 blocks required): "
            };

            var filter = new SelectionFilter(new TypedValue[]
            {
                new TypedValue((int)DxfCode.Start, "INSERT")
            });

            var psr = ed.GetSelection(pso, filter);
            if (psr.Status != PromptStatus.OK)
            {
                return null;
            }

            return psr.Value.GetObjectIds().ToList();
        }

        /// <summary>
        /// Select the reference block from the already selected blocks
        /// </summary>
        private ObjectId SelectReferenceBlock(Editor ed, List<ObjectId> selectedBlocks)
        {
            // Highlight all selected blocks for user reference
            HighlightBlocks(selectedBlocks, true);

            try
            {
                var peo = new PromptEntityOptions("\nSelect the reference block (base block for alignment): ");
                peo.SetRejectMessage("\nMust select one of the previously selected blocks.");
                peo.AddAllowedClass(typeof(BlockReference), false);

                var per = ed.GetEntity(peo);
                if (per.Status != PromptStatus.OK)
                {
                    return ObjectId.Null;
                }

                // Verify the selected block is in our list
                if (!selectedBlocks.Contains(per.ObjectId))
                {
                    ed.WriteMessage("\nSelected block must be one of the previously selected blocks.");
                    return SelectReferenceBlock(ed, selectedBlocks); // Recursive retry
                }

                return per.ObjectId;
            }
            finally
            {
                // Remove highlighting
                HighlightBlocks(selectedBlocks, false);
            }
        }

        /// <summary>
        /// Get spacing distance from user input with remembered default
        /// </summary>
        private double GetSpacingDistance(Editor ed)
        {
            // Get the last used spacing or fall back to default
            double defaultSpacing = GetLastUsedSpacing();

            var pdo = new PromptDistanceOptions($"\nEnter spacing distance between blocks (last used: {defaultSpacing}): ")
            {
                AllowNegative = false,
                AllowZero = false,
                UseDefaultValue = true,
                DefaultValue = defaultSpacing
            };

            var pdr = ed.GetDistance(pdo);
            return pdr.Status == PromptStatus.OK ? pdr.Value : -1;
        }

        /// <summary>
        /// Arrange blocks in a line based on the determined direction
        /// </summary>
        private void ArrangeBlocksInLine(List<ObjectId> blockIds, ObjectId referenceBlockId, double spacing)
        {
            AutoCADTransactionHelper.ExecuteInTransaction((trans, db) =>
            {
                // Get all block references
                var blockRefs = new List<BlockReference>();
                var referenceBlock = trans.GetObject(referenceBlockId, OpenMode.ForRead) as BlockReference;
                
                foreach (var id in blockIds)
                {
                    var blockRef = trans.GetObject(id, OpenMode.ForRead) as BlockReference;
                    if (blockRef != null)
                    {
                        blockRefs.Add(blockRef);
                    }
                }

                if (blockRefs.Count < 2 || referenceBlock == null)
                {
                    throw new InvalidOperationException("Invalid block references found.");
                }

                // Determine arrangement direction
                var direction = DetermineArrangementDirection(blockRefs);
                _loggingService?.LogInfo($"Arrangement direction determined: {(direction.X != 0 ? "X-axis" : "Y-axis")}", "BlockArrangementService");

                // Sort blocks based on their position along the determined direction
                var sortedBlocks = SortBlocksByDirection(blockRefs, direction, referenceBlock);

                // Arrange blocks starting from reference block position
                var referencePosition = referenceBlock.Position;
                
                for (int i = 0; i < sortedBlocks.Count; i++)
                {
                    var block = sortedBlocks[i];
                    if (block.ObjectId == referenceBlockId)
                        continue; // Skip reference block

                    // Calculate new position
                    var newPosition = referencePosition + (direction * spacing * GetBlockIndex(sortedBlocks, block, referenceBlock));
                    
                    // Move block to new position
                    block.UpgradeOpen();
                    var displacement = newPosition - block.Position;
                    block.TransformBy(Matrix3d.Displacement(displacement));
                }

            }, _loggingService, _errorHandlingService);
        }

        /// <summary>
        /// Determine arrangement direction based on bounding box of all blocks
        /// </summary>
        private Vector3d DetermineArrangementDirection(List<BlockReference> blockRefs)
        {
            if (blockRefs.Count < 2)
                return Vector3d.XAxis; // Default to X direction

            // Calculate overall bounding box
            var minX = blockRefs.Min(b => b.Position.X);
            var maxX = blockRefs.Max(b => b.Position.X);
            var minY = blockRefs.Min(b => b.Position.Y);
            var maxY = blockRefs.Max(b => b.Position.Y);

            var width = maxX - minX;
            var height = maxY - minY;

            // Choose direction based on which dimension is larger
            return width > height ? Vector3d.XAxis : Vector3d.YAxis;
        }

        /// <summary>
        /// Sort blocks by their position along the specified direction
        /// </summary>
        private List<BlockReference> SortBlocksByDirection(List<BlockReference> blockRefs, Vector3d direction, BlockReference referenceBlock)
        {
            return blockRefs.OrderBy(block =>
            {
                var relativePos = block.Position - referenceBlock.Position;
                return relativePos.DotProduct(direction);
            }).ToList();
        }

        /// <summary>
        /// Get the index of a block relative to the reference block
        /// </summary>
        private int GetBlockIndex(List<BlockReference> sortedBlocks, BlockReference targetBlock, BlockReference referenceBlock)
        {
            var refIndex = sortedBlocks.FindIndex(b => b.ObjectId == referenceBlock.ObjectId);
            var targetIndex = sortedBlocks.FindIndex(b => b.ObjectId == targetBlock.ObjectId);
            
            return targetIndex - refIndex;
        }

        /// <summary>
        /// Highlight or unhighlight blocks for visual feedback
        /// </summary>
        private void HighlightBlocks(List<ObjectId> blockIds, bool highlight)
        {
            try
            {
                var doc = Application.DocumentManager.MdiActiveDocument;
                if (doc == null) return;

                using (var trans = doc.TransactionManager.StartTransaction())
                {
                    foreach (var id in blockIds)
                    {
                        var entity = trans.GetObject(id, OpenMode.ForRead) as Entity;
                        if (entity != null)
                        {
                            if (highlight)
                                entity.Highlight();
                            else
                                entity.Unhighlight();
                        }
                    }
                    trans.Commit();
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning($"Failed to highlight blocks: {ex.Message}", "BlockArrangementService");
            }
        }

        /// <summary>
        /// Get the last used spacing distance from settings
        /// </summary>
        private double GetLastUsedSpacing()
        {
            try
            {
                return _settingsService?.GetDouble(ApplicationConstants.SETTING_BLOCK_ARRANGEMENT_SPACING, ApplicationConstants.DEFAULT_BLOCK_ARRANGEMENT_SPACING)
                       ?? ApplicationConstants.DEFAULT_BLOCK_ARRANGEMENT_SPACING;
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning($"Failed to retrieve last used spacing: {ex.Message}", "BlockArrangementService");
                return ApplicationConstants.DEFAULT_BLOCK_ARRANGEMENT_SPACING;
            }
        }

        /// <summary>
        /// Save the successfully used spacing distance for future use
        /// </summary>
        private void SaveLastUsedSpacing(double spacing)
        {
            try
            {
                if (_settingsService?.SetDouble(ApplicationConstants.SETTING_BLOCK_ARRANGEMENT_SPACING, spacing) == true)
                {
                    _loggingService?.LogDebug($"Saved last used spacing: {spacing}", "BlockArrangementService");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning($"Failed to save last used spacing: {ex.Message}", "BlockArrangementService");
            }
        }
    }
}
