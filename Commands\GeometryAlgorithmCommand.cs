using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;
using System;
using System.Collections.Generic;
using System.Linq;
using IECAD.Services;
using IECAD.Services.OutlineWalker;

[assembly: CommandClass(typeof(IECAD.Commands.GeometryAlgorithmCommand))]

namespace IECAD.Commands
{
    /// <summary>
    /// 几何算法命令 - 提供直接访问几何算法功能的命令
    /// </summary>
    public class GeometryAlgorithmCommand
    {
        /// <summary>
        /// 几何算法类型枚举
        /// </summary>
        private enum GeometryAlgorithmType
        {
            ConvexHull,
            Boundary,
            AlphaShape
        }
        /// <summary>
        /// 凸包提取命令
        /// </summary>
        [CommandMethod("CONVEXHULL")]
        public void ExtractConvexHull()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;

            try
            {
                ExtractGeometryWithAlgorithm(GeometryAlgorithmType.ConvexHull, 1.0, "凸包");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n凸包提取失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 边界提取命令
        /// </summary>
        [CommandMethod("EXTRACTBOUNDARY")]
        public void ExtractBoundary()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;

            try
            {
                // 提示用户输入缓冲距离
                var pdo = new PromptDoubleOptions("\n输入缓冲距离 [0.1]")
                {
                    DefaultValue = 0.1,
                    AllowNegative = false,
                    AllowZero = true,
                    AllowNone = true
                };

                var pdr = ed.GetDouble(pdo);
                double bufferDistance = pdr.Status == PromptStatus.OK ? pdr.Value : 0.1;

                ExtractGeometryWithAlgorithm(GeometryAlgorithmType.Boundary, bufferDistance, "边界提取");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n边界提取失败: {ex.Message}");
            }
        }

        /// <summary>
        /// Alpha Shape提取命令
        /// </summary>
        [CommandMethod("ALPHASHAPE")]
        public void ExtractAlphaShape()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;

            try
            {
                // 提示用户输入Alpha参数
                var pdo = new PromptDoubleOptions("\n输入Alpha参数 (推荐值: 0.5-5.0, 值越小越凹) [1.0]")
                {
                    DefaultValue = 1.0,
                    AllowNegative = false,
                    AllowZero = false,
                    AllowNone = true
                };

                var pdr = ed.GetDouble(pdo);
                double alpha = pdr.Status == PromptStatus.OK ? pdr.Value : 1.0;

                ed.WriteMessage($"\n使用Alpha参数: {alpha}");
                ed.WriteMessage("\n提示: Alpha值越小，凹包越凹；Alpha值越大，越接近凸包");

                // 使用通用方法提取Alpha Shape
                ExtractGeometryWithAlgorithm(GeometryAlgorithmType.AlphaShape, alpha, "Alpha Shape");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n Alpha Shape提取失败: {ex.Message}");
                ed.WriteMessage("\n建议:");
                ed.WriteMessage("\n  1. 检查选择的实体是否有效");
                ed.WriteMessage("\n  2. 尝试调整Alpha参数值");
                ed.WriteMessage("\n  3. 确保选择了足够的实体（至少3个点）");
            }
        }

        /// <summary>
        /// 几何算法选择命令
        /// </summary>
        [CommandMethod("GEOMETRYALGORITHM")]
        public void SelectGeometryAlgorithm()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;

            try
            {
                // 提示用户选择算法类型
                var pko = new PromptKeywordOptions("\n选择几何算法类型");
                pko.Keywords.Add("ConvexHull", "凸包", "凸包算法（C）");
                pko.Keywords.Add("Boundary", "边界", "边界提取（B）");
                pko.Keywords.Add("AlphaShape", "凹包", "Alpha Shape凹包（A）");
                pko.Keywords.Default = "ConvexHull";
                pko.AllowNone = true;

                var pkr = ed.GetKeywords(pko);
                
                switch (pkr.StringResult)
                {
                    case "ConvexHull":
                        ExtractConvexHull();
                        break;
                    case "Boundary":
                        ExtractBoundary();
                        break;
                    case "AlphaShape":
                        ExtractAlphaShape();
                        break;
                    default:
                        ExtractConvexHull();
                        break;
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n几何算法执行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// Alpha Shape调试命令 - 提供详细的调试信息
        /// </summary>
        [CommandMethod("ALPHASHAPEDEBUG")]
        public void AlphaShapeDebug()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;

            try
            {
                ed.WriteMessage("\n=== Alpha Shape 调试模式 ===");

                // 获取用户选择
                var psr = ed.GetSelection();
                if (psr.Status != PromptStatus.OK)
                {
                    ed.WriteMessage("\n未选择任何实体。");
                    return;
                }

                ed.WriteMessage($"\n选择了 {psr.Value.Count} 个实体");

                // 提示Alpha参数
                var pdo = new PromptDoubleOptions("\n输入Alpha参数进行测试 [1.0]")
                {
                    DefaultValue = 1.0,
                    AllowNegative = false,
                    AllowZero = false,
                    AllowNone = true
                };

                var pdr = ed.GetDouble(pdo);
                double alpha = pdr.Status == PromptStatus.OK ? pdr.Value : 1.0;

                // 创建几何算法服务进行调试
                using (var geometryService = new GeometryAlgorithmService())
                {
                    using (var tr = doc.TransactionManager.StartTransaction())
                    {
                        var entities = new List<Entity>();
                        foreach (ObjectId id in psr.Value.GetObjectIds())
                        {
                            if (tr.GetObject(id, OpenMode.ForRead) is Entity ent)
                            {
                                entities.Add(ent);
                            }
                        }

                        ed.WriteMessage($"\n处理 {entities.Count} 个有效实体");

                        // 测试不同的Alpha值
                        var testAlphas = new[] { 0.5, 1.0, 2.0, 5.0 };

                        foreach (var testAlpha in testAlphas)
                        {
                            try
                            {
                                ed.WriteMessage($"\n--- 测试 Alpha = {testAlpha} ---");
                                var result = geometryService.ExtractAlphaShape(entities, testAlpha);
                                ed.WriteMessage($"  结果点数: {result.Count}");

                                if (result.Count > 0)
                                {
                                    var bounds = GetBounds(result);
                                    ed.WriteMessage($"  边界: ({bounds.min.X:F2},{bounds.min.Y:F2}) 到 ({bounds.max.X:F2},{bounds.max.Y:F2})");
                                }
                            }
                            catch (Autodesk.AutoCAD.Runtime.Exception ex)
                            {
                                ed.WriteMessage($"  Alpha {testAlpha} 失败: {ex.Message}");
                            }
                        }

                        // 使用用户指定的Alpha值
                        ed.WriteMessage($"\n--- 使用用户指定的 Alpha = {alpha} ---");
                        var finalResult = geometryService.ExtractAlphaShape(entities, alpha);
                        ed.WriteMessage($"最终结果点数: {finalResult.Count}");

                        tr.Commit();
                    }
                }

                ed.WriteMessage("\n=== Alpha Shape 调试完成 ===");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n调试过程出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 通用几何算法提取方法
        /// </summary>
        /// <param name="algorithmType">算法类型</param>
        /// <param name="parameter">算法参数</param>
        /// <param name="algorithmName">算法名称</param>
        private void ExtractGeometryWithAlgorithm(GeometryAlgorithmType algorithmType, double parameter, string algorithmName)
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;

            using (var geometryService = new GeometryAlgorithmService())
            {
                using (var tr = doc.TransactionManager.StartTransaction())
                {
                    try
                    {
                        // 获取用户选择的实体
                        var psr = ed.GetSelection();
                        if (psr.Status != PromptStatus.OK)
                        {
                            ed.WriteMessage("\n未选择任何实体。");
                            return;
                        }

                        var entities = new List<Entity>();
                        foreach (ObjectId id in psr.Value.GetObjectIds())
                        {
                            if (tr.GetObject(id, OpenMode.ForRead) is Entity ent)
                            {
                                entities.Add(ent);
                            }
                        }

                        ed.WriteMessage($"\n使用{algorithmName}算法处理 {entities.Count} 个实体...");

                        // 根据算法类型执行相应的几何运算
                        List<Point3d> result = null;
                        switch (algorithmType)
                        {
                            case GeometryAlgorithmType.ConvexHull:
                                result = geometryService.ExtractConvexHull(entities);
                                break;
                            case GeometryAlgorithmType.Boundary:
                                result = geometryService.ExtractBoundary(entities, parameter);
                                break;
                            case GeometryAlgorithmType.AlphaShape:
                                result = geometryService.ExtractAlphaShape(entities, parameter);
                                break;
                        }

                        if (result != null && result.Count > 2)
                        {
                            CreatePolylineFromPoints(result, tr);
                            tr.Commit();
                            ed.WriteMessage($"\n{algorithmName}提取完成！生成了 {result.Count} 个顶点的轮廓。");
                        }
                        else
                        {
                            ed.WriteMessage($"\n无法生成有效的{algorithmName}轮廓。");
                            tr.Abort();
                        }
                    }
                    catch (Autodesk.AutoCAD.Runtime.Exception ex)
                    {
                        ed.WriteMessage($"\n{algorithmName}处理过程中发生错误: {ex.Message}");
                        tr.Abort();
                    }
                }
            }
        }

        /// <summary>
        /// 从点集创建多段线
        /// </summary>
        /// <param name="points">点集</param>
        /// <param name="tr">事务</param>
        private void CreatePolylineFromPoints(List<Point3d> points, Transaction tr)
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;

            using (var polyline = new Polyline())
            {
                for (int i = 0; i < points.Count; i++)
                {
                    polyline.AddVertexAt(i, new Point2d(points[i].X, points[i].Y), 0, 0, 0);
                }

                // 闭合多段线
                if (points.Count > 2)
                {
                    var firstPoint = points.First();
                    var lastPoint = points.Last();
                    if (firstPoint.DistanceTo(lastPoint) > 1e-6)
                    {
                        polyline.AddVertexAt(points.Count, new Point2d(firstPoint.X, firstPoint.Y), 0, 0, 0);
                    }
                    polyline.Closed = true;
                }

                // 设置多段线属性
                polyline.ColorIndex = 1; // 红色
                polyline.LineWeight = LineWeight.LineWeight030;

                // 添加到模型空间
                var bt = (BlockTable)tr.GetObject(doc.Database.BlockTableId, OpenMode.ForRead);
                var btr = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite);

                btr.AppendEntity(polyline);
                tr.AddNewlyCreatedDBObject(polyline, true);
            }
        }

        /// <summary>
        /// 获取点集的边界
        /// </summary>
        private (Point3d min, Point3d max) GetBounds(List<Point3d> points)
        {
            if (points.Count == 0)
                return (Point3d.Origin, Point3d.Origin);

            var minX = points.Min(p => p.X);
            var minY = points.Min(p => p.Y);
            var maxX = points.Max(p => p.X);
            var maxY = points.Max(p => p.Y);

            return (new Point3d(minX, minY, 0), new Point3d(maxX, maxY, 0));
        }

        /// <summary>
        /// 显示几何算法帮助信息
        /// </summary>
        [CommandMethod("GEOMETRYHELP")]
        public void ShowGeometryHelp()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;

            ed.WriteMessage("\n=== 几何算法轮廓提取帮助 ===");
            ed.WriteMessage("\n可用命令:");
            ed.WriteMessage("\n  CONVEXHULL        - 提取凸包轮廓");
            ed.WriteMessage("\n  EXTRACTBOUNDARY   - 提取边界轮廓");
            ed.WriteMessage("\n  ALPHASHAPE        - 提取Alpha Shape凹包");
            ed.WriteMessage("\n  ALPHASHAPEDEBUG   - Alpha Shape调试模式");
            ed.WriteMessage("\n  GEOMETRYALGORITHM - 选择算法类型");
            ed.WriteMessage("\n  EXTRACTOUTLINE    - 通用轮廓提取（包含所有策略）");
            ed.WriteMessage("\n");
            ed.WriteMessage("\n算法说明:");
            ed.WriteMessage("\n  凸包算法    - 计算实体集合的最小凸多边形");
            ed.WriteMessage("\n  边界提取    - 提取实体的实际边界，保持原始形状");
            ed.WriteMessage("\n  Alpha Shape - 可调节凹凸程度的凹包算法");
            ed.WriteMessage("\n                Alpha值越小越凹，Alpha值越大越接近凸包");
            ed.WriteMessage("\n");
            ed.WriteMessage("\n使用方法:");
            ed.WriteMessage("\n  1. 选择要处理的实体");
            ed.WriteMessage("\n  2. 运行相应的命令");
            ed.WriteMessage("\n  3. 根据提示输入参数");
            ed.WriteMessage("\n  4. 查看生成的轮廓结果");
            ed.WriteMessage("\n================================");
        }
    }
}
