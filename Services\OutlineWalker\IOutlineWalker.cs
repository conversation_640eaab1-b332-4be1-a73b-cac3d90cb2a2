using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using System.Collections.Generic;

namespace IECAD.Services.OutlineWalker
{
    /// <summary>
    /// 轮廓步行者策略接口
    /// </summary>
    public interface IOutlineWalker
    {
        /// <summary>
        /// 初始化步行者，找到起点和初始状态
        /// </summary>
        /// <param name="entities">要处理的几何对象集合</param>
        /// <param name="tolerance">容差参数</param>
        void Initialize(IEnumerable<Entity> entities, double tolerance = 1.0);

        /// <summary>
        /// 寻找下一个决策点/事件
        /// </summary>
        /// <returns>事件结果</returns>
        WalkerEventResult FindNextEvent();

        /// <summary>
        /// 根据当前事件决定下一步方向
        /// </summary>
        /// <param name="eventResult">事件结果</param>
        /// <returns>方向决策</returns>
        DirectionDecision MakeDecision(WalkerEventResult eventResult);

        /// <summary>
        /// 更新步行者状态，包括位置和路径记录
        /// </summary>
        /// <param name="decision">方向决策</param>
        void UpdateState(DirectionDecision decision);

        /// <summary>
        /// 获取最终的轮廓路径
        /// </summary>
        /// <returns>顶点列表</returns>
        List<Point3d> GetResultPath();

        /// <summary>
        /// 检查是否已完成轮廓追踪
        /// </summary>
        bool IsCompleted { get; }

        /// <summary>
        /// 当前位置
        /// </summary>
        Point3d CurrentPosition { get; }

        /// <summary>
        /// 步行者策略名称
        /// </summary>
        string StrategyName { get; }
    }
}
